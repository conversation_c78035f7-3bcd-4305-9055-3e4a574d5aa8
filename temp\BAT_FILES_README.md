# 🚀 MineExtractorWeb v1.0 - BAT-Dateien Übersicht

## 📋 Verfügbare Startdateien

### **🔧 SETUP_SYSTEM.bat** 
**Für Erstinstallation**
- Führt automatisches Setup durch
- Installiert Dependencies
- Konfiguriert System
- **<PERSON><PERSON><PERSON><PERSON> dies ZUERST bei neuer Installation**

### **🚀 START_MineExtractorWeb.bat**
**Für regulären Start mit Diagnose**
- Umfassende System-Checks
- Automatische Problemlösung
- API-Konfigurationshilfe
- Fallback-Optionen
- **Empfohlen für tägliche Nutzung**

### **⚡ QUICK_START.bat**
**Für schnellen Start**
- Minimale Checks
- Direkte GUI-Ausführung
- **Für erfahrene Benutzer mit konfiguriertem System**

---

## 🎯 Empfohlener Workflow

### **Erstmalige Installation:**
```
1. SETUP_SYSTEM.bat ausführen
2. API Keys konfigurieren (automatisch angeboten)
3. System testen
```

### **Tägliche Nutzung:**
```
START_MineExtractorWeb.bat doppelklicken
```

### **Schneller Start (wenn alles konfiguriert):**
```
QUICK_START.bat doppelklicken
```

---

## 🛠️ Troubleshooting

### **Problem: "Python nicht gefunden"**
- Installiere miniforge3: https://github.com/conda-forge/miniforge
- Oder verwende Standard Python 3.8+

### **Problem: "Module nicht gefunden"**
- Führe `SETUP_SYSTEM.bat` aus
- Oder manuell: `pip install -r requirements.txt`

### **Problem: "GUI startet nicht"**
- Verwende `START_MineExtractorWeb.bat` für Diagnose
- Prüfe API-Konfiguration
- Führe `python final_test.py` aus

---

## 📞 Support

Bei Problemen:
1. Führe `START_MineExtractorWeb.bat` aus und folge den Troubleshooting-Optionen
2. Prüfe `logs\mineextractor_web.log` für Details
3. Verwende `python main.py --config` für System-Status

---

**💡 Tipp:** Erstelle eine Desktop-Verknüpfung zu `START_MineExtractorWeb.bat` für einfachen Zugriff!
