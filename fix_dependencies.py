#!/usr/bin/env python3
"""
Fix Dependencies Script für MineExtractorWeb v1.0
Behebt Dependencies-Probleme und installiert essentielle Pakete einzeln
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package_name, description=""):
    """Installiert einzelnes Package mit Fehlerbehandlung"""
    
    print(f"📦 Installing {package_name}... {description}")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], check=True, capture_output=True, text=True)
        
        print(f"   ✅ {package_name} installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ {package_name} installation failed: {e.stderr}")
        return False

def main():
    print("🔧 MineExtractorWeb v1.0 - Dependencies Fix")
    print("=" * 50)
    
    # Essentielle Pakete in der richtigen Reihenfolge
    essential_packages = [
        ("pandas", "Data processing framework"),
        ("aiohttp", "Async HTTP client"),
        ("python-dotenv", "Environment variables"),
        ("requests", "HTTP requests library"),
        ("tenacity", "Retry mechanisms"),
        ("colorama", "Colored terminal output"),
        ("typing-extensions", "Extended typing support"),
        ("python-dateutil", "Date/time utilities"),
        ("certifi", "SSL certificates"),
        ("chardet", "Character encoding detection")
    ]
    
    # Upgrade pip first
    print("📦 Upgrading pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("   ✅ pip upgraded successfully")
    except subprocess.CalledProcessError:
        print("   ⚠️  pip upgrade failed, continuing...")
    
    print()
    
    # Install essential packages one by one
    successful = 0
    failed = 0
    
    for package, description in essential_packages:
        if install_package(package, description):
            successful += 1
        else:
            failed += 1
    
    print(f"\n📊 Installation Summary:")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All dependencies installed successfully!")
        return True
    elif successful >= 4:  # Minimum required packages
        print(f"\n⚠️  Some packages failed, but core dependencies are available")
        print(f"    System should be functional with {successful} packages")
        return True
    else:
        print(f"\n❌ Too many packages failed. Please check your Python installation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
