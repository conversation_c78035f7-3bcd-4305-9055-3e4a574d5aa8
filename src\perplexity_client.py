"""
Perplexity AI Client für MineExtractorWeb v1.0
Spezialisierter Client für Mining-Research mit Perplexity AI
"""

import aiohttp
import asyncio
import json
import time
from typing import Dict, Optional, List, Any
from tenacity import retry, wait_exponential, stop_after_attempt, RetryError
import logging

# Setup logging
logger = logging.getLogger(__name__)

class PerplexityAPIError(Exception):
    """Custom exception für Perplexity API Fehler"""
    pass

class PerplexityClient:
    """
    Spezialisierter Perplexity AI Client für Mining Research
    Optimiert für Quebec Mining Data Extraction mit Enhanced Error Handling
    """
    
    def __init__(self, api_key: str = None):
        # Load API configuration
        try:
            from config.api_keys import APIConfig
            self.api_key = api_key or APIConfig.PERPLEXITY_API_KEY
            self.base_url = APIConfig.PERPLEXITY_BASE_URL
            self.model = APIConfig.PERPLEXITY_MODEL
            self.max_tokens = APIConfig.MAX_TOKENS
            self.temperature = APIConfig.TEMPERATURE
            self.timeout = APIConfig.TIMEOUT_SECONDS
            self.max_retries = APIConfig.MAX_RETRIES
            self.priority_domains = APIConfig.PRIORITY_DOMAINS
        except ImportError:
            # Fallback configuration
            self.api_key = api_key
            self.base_url = 'https://api.perplexity.ai/chat/completions'
            self.model = 'llama-3.1-sonar-large-128k-online'
            self.max_tokens = 2000
            self.temperature = 0.1
            self.timeout = 120
            self.max_retries = 3
            self.priority_domains = ['gov.ca', 'sedarplus.ca']
        
        # Validate API key
        if not self.api_key:
            raise ValueError("Perplexity API key is required")
        
        # Session will be created when needed
        self.session = None
        
        # Request tracking
        self.request_count = 0
        self.total_tokens_used = 0
        self.last_request_time = 0
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._close_session()
    
    async def _ensure_session(self):
        """Stellt sicher, dass eine Session existiert"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _close_session(self):
        """Schließt die Session sauber"""
        if self.session and not self.session.closed:
            await self.session.close()
            # Wait for underlying connections to close
            await asyncio.sleep(0.1)
    
    def _build_headers(self) -> Dict[str, str]:
        """Erstellt HTTP Headers für API-Requests"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "MineExtractorWeb/1.0"
        }
    
    def _build_payload(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        Erstellt Request-Payload für Perplexity API
        
        Args:
            prompt: Der Research-Prompt
            **kwargs: Zusätzliche Parameter für API-Call
        """
        
        payload = {
            "model": kwargs.get('model', self.model),
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get('temperature', self.temperature),
            "max_tokens": kwargs.get('max_tokens', self.max_tokens),
            "return_citations": True,
            "search_domain_filter": kwargs.get('domain_filter', self.priority_domains),
            "search_recency_filter": kwargs.get('recency_filter', "month")
        }
        
        return payload
    
    @retry(
        wait=wait_exponential(multiplier=1, min=4, max=10),
        stop=stop_after_attempt(3),
        reraise=True
    )
    async def _make_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Macht HTTP-Request an Perplexity API mit Retry-Logic
        
        Args:
            payload: Request payload
            
        Returns:
            API response als Dictionary
            
        Raises:
            PerplexityAPIError: Bei API-Fehlern
        """
        
        await self._ensure_session()
        
        # Rate limiting
        await self._apply_rate_limiting()
        
        headers = self._build_headers()
        start_time = time.time()
        
        try:
            logger.info(f"Making Perplexity API request (attempt {self.request_count + 1})")
            
            async with self.session.post(self.base_url, headers=headers, json=payload) as response:
                response_time = time.time() - start_time
                self.request_count += 1
                self.last_request_time = time.time()
                
                # Log request details
                logger.info(f"Perplexity API response: {response.status} ({response_time:.2f}s)")
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Track token usage
                    if 'usage' in result:
                        tokens_used = result['usage'].get('total_tokens', 0)
                        self.total_tokens_used += tokens_used
                        logger.info(f"Tokens used: {tokens_used} (total: {self.total_tokens_used})")
                    
                    return {
                        'success': True,
                        'data': result,
                        'response_time': response_time,
                        'status_code': response.status
                    }
                
                elif response.status == 429:
                    # Rate limit exceeded
                    error_text = await response.text()
                    logger.warning(f"Rate limit exceeded: {error_text}")
                    raise PerplexityAPIError(f"Rate limit exceeded: {error_text}")
                
                elif response.status == 401:
                    # Authentication error
                    error_text = await response.text()
                    logger.error(f"Authentication failed: {error_text}")
                    raise PerplexityAPIError(f"Authentication failed: {error_text}")
                
                elif response.status >= 500:
                    # Server error - retry
                    error_text = await response.text()
                    logger.warning(f"Server error {response.status}: {error_text}")
                    raise PerplexityAPIError(f"Server error {response.status}: {error_text}")
                
                else:
                    # Other client errors - don't retry
                    error_text = await response.text()
                    logger.error(f"API error {response.status}: {error_text}")
                    raise PerplexityAPIError(f"API error {response.status}: {error_text}")
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error: {e}")
            raise PerplexityAPIError(f"Network error: {e}")
        
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise PerplexityAPIError(f"Invalid JSON response: {e}")
    
    async def _apply_rate_limiting(self):
        """Implementiert Rate Limiting zwischen Requests"""
        if self.last_request_time > 0:
            time_since_last = time.time() - self.last_request_time
            min_interval = 1.0  # Minimum 1 second between requests
            
            if time_since_last < min_interval:
                wait_time = min_interval - time_since_last
                logger.info(f"Rate limiting: waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
    
    async def research_mine_comprehensive(self, mine_name: str, **kwargs) -> Dict[str, Any]:
        """
        Führt umfassende Mine-Recherche mit Perplexity AI durch
        
        Args:
            mine_name: Name der Mine (z.B. "Éléonore", "Canadian Malartic")
            **kwargs: Zusätzliche Parameter für API-Call
            
        Returns:
            Dict mit Research-Ergebnissen und Metadaten
        """
        
        try:
            from config.mining_prompts import PromptBuilder
            prompt = PromptBuilder.build_comprehensive_prompt(mine_name)
        except ImportError:
            # Fallback prompt
            prompt = f"""Research comprehensive data about {mine_name} mine in Quebec, Canada.
            Find: operator, status, restoration costs (CAD), mine type, commodity, coordinates, production dates.
            Focus on official government and company sources."""
        
        logger.info(f"Starting comprehensive research for: {mine_name}")
        
        try:
            payload = self._build_payload(prompt, **kwargs)
            api_response = await self._make_request(payload)
            
            if api_response['success']:
                result = self._process_api_response(api_response['data'], mine_name)
                result['response_time'] = api_response['response_time']
                result['request_count'] = self.request_count
                
                logger.info(f"Research completed for {mine_name}")
                return result
            else:
                raise PerplexityAPIError("API request failed")
                
        except RetryError as e:
            logger.error(f"All retry attempts failed for {mine_name}: {e}")
            return self._create_error_response(mine_name, f"Retry exhausted: {e}")
        
        except PerplexityAPIError as e:
            logger.error(f"API error for {mine_name}: {e}")
            return self._create_error_response(mine_name, str(e))
        
        except Exception as e:
            logger.error(f"Unexpected error for {mine_name}: {e}")
            return self._create_error_response(mine_name, f"Unexpected error: {e}")
    
    async def research_targeted(self, mine_name: str, data_type: str = 'financial', **kwargs) -> Dict[str, Any]:
        """
        Führt zielgerichtete Recherche für spezifische Datentypen durch
        
        Args:
            mine_name: Name der Mine
            data_type: Art der Daten ('financial', 'technical', 'operational')
            **kwargs: Zusätzliche Parameter
            
        Returns:
            Targeted research results
        """
        
        try:
            from config.mining_prompts import PromptBuilder
            prompt = PromptBuilder.build_targeted_prompt(mine_name, data_type, 'perplexity')
        except ImportError:
            # Fallback prompt
            prompt = f"""Find {data_type} data for {mine_name} mine in Quebec, Canada."""
        
        logger.info(f"Starting {data_type} research for: {mine_name}")
        
        try:
            payload = self._build_payload(prompt, **kwargs)
            api_response = await self._make_request(payload)
            
            if api_response['success']:
                result = self._process_api_response(api_response['data'], mine_name)
                result['research_type'] = data_type
                result['response_time'] = api_response['response_time']
                
                logger.info(f"{data_type.title()} research completed for {mine_name}")
                return result
            else:
                raise PerplexityAPIError("Targeted research failed")
                
        except Exception as e:
            logger.error(f"Targeted research error for {mine_name}: {e}")
            return self._create_error_response(mine_name, str(e))
    
    def _process_api_response(self, api_data: Dict[str, Any], mine_name: str) -> Dict[str, Any]:
        """
        Verarbeitet rohe API-Response zu strukturiertem Format
        
        Args:
            api_data: Rohe API-Response
            mine_name: Name der Mine
            
        Returns:
            Verarbeitete Response mit extrahierten Daten
        """
        
        try:
            # Extract main content
            content = api_data['choices'][0]['message']['content']
            
            # Extract citations
            citations = api_data.get('citations', [])
            
            # Extract usage information
            usage = api_data.get('usage', {})
            
            result = {
                'success': True,
                'mine_name': mine_name,
                'content': content,
                'citations': citations,
                'usage': usage,
                'raw_response': api_data,
                'timestamp': time.time(),
                'source_count': len(citations)
            }
            
            # Process citations for source metadata
            processed_sources = []
            for citation in citations[:10]:  # Limit to first 10 sources
                if isinstance(citation, dict):
                    source = {
                        'url': citation.get('url', ''),
                        'title': citation.get('title', ''),
                        'snippet': citation.get('snippet', ''),
                        'domain': self._extract_domain(citation.get('url', '')),
                        'source_type': self._classify_source_type(citation.get('url', ''))
                    }
                    processed_sources.append(source)
                else:
                    logger.warning(f"Skipped invalid citation (not a dict): {citation}")
            
            result['processed_sources'] = processed_sources
            
            return result
            
        except (KeyError, IndexError) as e:
            logger.error(f"Error processing API response: {e}")
            return self._create_error_response(mine_name, f"Response processing error: {e}")
    
    def _extract_domain(self, url: str) -> str:
        """Extrahiert Domain aus URL"""
        try:
            from urllib.parse import urlparse
            return urlparse(url).netloc
        except:
            return ""
    
    def _classify_source_type(self, url: str) -> str:
        """Klassifiziert Quellentyp basierend auf Domain"""
        domain = self._extract_domain(url).lower()
        
        government_indicators = ['gov.ca', 'gc.ca', 'gouv.qc.ca', 'sedarplus.ca']
        company_indicators = ['newmont.com', 'agnicoeagle.com', 'iamgold.com']
        industry_indicators = ['mining.com', 'northernminer.com', 'kitco.com']
        
        if any(indicator in domain for indicator in government_indicators):
            return 'government'
        elif any(indicator in domain for indicator in company_indicators):
            return 'company'
        elif any(indicator in domain for indicator in industry_indicators):
            return 'industry'
        else:
            return 'other'
    
    def _create_error_response(self, mine_name: str, error_message: str) -> Dict[str, Any]:
        """Erstellt standardisierte Error-Response"""
        return {
            'success': False,
            'mine_name': mine_name,
            'error': error_message,
            'content': '',
            'citations': [],
            'processed_sources': [],
            'timestamp': time.time(),
            'source_count': 0
        }
    
    async def test_connection(self) -> bool:
        """
        Testet API-Verbindung mit minimaler Anfrage
        
        Returns:
            True wenn Verbindung erfolgreich, False sonst
        """
        
        test_prompt = "Test connection to Perplexity API. Please respond with 'Connection successful.'"
        
        try:
            payload = self._build_payload(test_prompt, max_tokens=50)
            response = await self._make_request(payload)
            
            return response['success']
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """Gibt Nutzungsstatistiken zurück"""
        return {
            'total_requests': self.request_count,
            'total_tokens_used': self.total_tokens_used,
            'avg_tokens_per_request': self.total_tokens_used / max(1, self.request_count),
            'last_request_time': self.last_request_time
        }
    
    def __del__(self):
        """Cleanup beim Zerstören des Objekts"""
        if hasattr(self, 'session') and self.session and not self.session.closed:
            # Create a task to close the session, but don't wait for it
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.session.close())
            except:
                pass  # Ignore errors during cleanup

# Convenience functions
async def quick_research(mine_name: str, api_key: str) -> Dict[str, Any]:
    """
    Quick research function für einfache Nutzung
    
    Args:
        mine_name: Name der Mine
        api_key: Perplexity API Key
        
    Returns:
        Research results
    """
    
    async with PerplexityClient(api_key) as client:
        return await client.research_mine_comprehensive(mine_name)

async def test_api_key(api_key: str) -> bool:
    """
    Testet API Key
    
    Args:
        api_key: Perplexity API Key
        
    Returns:
        True wenn API Key funktioniert
    """
    
    try:
        async with PerplexityClient(api_key) as client:
            return await client.test_connection()
    except:
        return False

if __name__ == "__main__":
    # Test Perplexity Client
    async def test_client():
        print("🧪 Testing Perplexity Client...")
        
        # Test without real API key (will use test mode)
        try:
            client = PerplexityClient("test-key")
            print("✅ Client initialized")
            
            # Test connection would require real API key
            print("⚠️  Skipping connection test (requires real API key)")
            
        except Exception as e:
            print(f"❌ Client test failed: {e}")
    
    # Run test
    asyncio.run(test_client())
    print("✅ Perplexity client test completed!")
