@echo off
echo.
echo 🌐 MineExtractorWeb v1.0 - Quick Start
echo =======================================
echo.

REM Verwende miniforge Python
set PYTHON_CMD=C:\ProgramData\miniforge3\python.exe

REM Fallback auf Standard Python wenn miniforge nicht verfügbar
%PYTHON_CMD% --version >nul 2>&1
if errorlevel 1 (
    set PYTHON_CMD=python
)

echo 🚀 Starte MineExtractorWeb GUI...
echo.

%PYTHON_CMD% main.py

if errorlevel 1 (
    echo.
    echo ❌ Start fehlgeschlagen!
    echo 💡 Verwende START_MineExtractorWeb.bat für detaillierte Diagnose
    echo.
    pause
)
