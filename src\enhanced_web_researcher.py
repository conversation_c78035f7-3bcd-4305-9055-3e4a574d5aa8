"""
Enhanced Multi-Phase Web Mining Researcher v2.0
Umfassende, mehrstufige Research-Engine für detaillierte Mining-Daten
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import logging
from dataclasses import dataclass, field
import unicodedata
import ast
import os
from rapidfuzz import fuzz, process

logger = logging.getLogger(__name__)

@dataclass
class ResearchSource:
    """Detaillierte Quelleninformation"""
    url: str
    title: str
    date_accessed: str
    content_type: str  # "company_report", "government_data", "news", "industry_report"
    reliability_score: float  # 0.0-1.0
    content_snippet: str
    data_points_extracted: List[str] = field(default_factory=list)

@dataclass
class DataPoint:
    """Ein einzelner Datenpunkt mit Quelle"""
    field_name: str
    value: Any
    confidence: float  # 0.0-1.0
    sources: List[ResearchSource] = field(default_factory=list)
    extraction_method: str = ""
    cross_validated: bool = False

@dataclass 
class EnhancedMineResearchResult:
    """Erweiterte Research-Ergebnisse mit detaillierter Quellenangabe"""
    mine_name: str
    data_points: Dict[str, DataPoint] = field(default_factory=dict)
    all_sources: List[ResearchSource] = field(default_factory=list)
    research_phases_completed: List[str] = field(default_factory=list)
    total_research_time: float = 0.0
    data_completeness_score: float = 0.0
    debug_log: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_data_point(self, field_name: str, value: Any, confidence: float, 
                      sources: List[ResearchSource], method: str = ""):
        """Fügt einen Datenpunkt mit Quellenangabe hinzu"""
        self.data_points[field_name] = DataPoint(
            field_name=field_name,
            value=value,
            confidence=confidence,
            sources=sources,
            extraction_method=method
        )
    
    def get_sources_summary(self) -> str:
        """Erstellt Zusammenfassung aller Quellen"""
        sources_by_type = {}
        for source in self.all_sources:
            if source.content_type not in sources_by_type:
                sources_by_type[source.content_type] = []
            sources_by_type[source.content_type].append(source)
        
        summary = f"Sources for {self.mine_name}:\n"
        for content_type, sources in sources_by_type.items():
            summary += f"\n{content_type.title()} ({len(sources)}):\n"
            for i, source in enumerate(sources[:3], 1):  # Limit to top 3 per type
                summary += f"  {i}. {source.title} ({source.url})\n"
        
        return summary

class EnhancedMiningResearcher:
    """
    Erweiterte Research-Engine mit mehrstufiger Suchstrategie
    """
    
    def __init__(self, config: Dict[str, str]):
        """
        Args:
            config: Dictionary mit API-Keys und Konfiguration
        """
        self.config = config
        self.session = None
        self.logger = logging.getLogger(__name__)
        # Multi-API-Clients initialisieren
        self.api_clients = {}
        # Perplexity
        try:
            from src.perplexity_client import PerplexityClient
            if config.get('perplexity_key'):
                self.api_clients['perplexity'] = PerplexityClient(config['perplexity_key'])
        except Exception as e:
            self.logger.warning(f"PerplexityClient konnte nicht geladen werden: {e}")
        # Tavily
        try:
            from src.tavily_client import TavilyClient
            if config.get('tavily_key'):
                self.api_clients['tavily'] = TavilyClient(config['tavily_key'])
        except Exception as e:
            self.logger.warning(f"TavilyClient konnte nicht geladen werden: {e}")
        # OpenAI (Platzhalter)
        if config.get('openai_key'):
            self.api_clients['openai'] = None  # TODO: OpenAIClient implementieren
        # Gemini (Platzhalter)
        if config.get('gemini_key'):
            self.api_clients['gemini'] = None  # TODO: GeminiClient implementieren
        # DeepSeek (Platzhalter)
        if config.get('deepseek_key'):
            self.api_clients['deepseek'] = None  # TODO: DeepSeekClient implementieren
        
        # Erweiterte Research-Phasen mit fokussierten Such-Strategien
        self.research_phases = {
            "basic_info": {
                "name": "Basic Mine Information",
                "keywords": [
                    "{mine_name} mine Quebec Canada", 
                    "{mine_name} mining operation location",
                    "{mine_name} mine operator company",
                    "{mine_name} mine Quebec coordinates",
                    "{mine_name} mine MERN database"
                ],
                "focus": ["location", "operator", "mine_type", "status", "coordinates"]
            },
            "production_data": {
                "name": "Production & Timeline Data", 
                "keywords": [
                    "{mine_name} production start date year",
                    "{mine_name} first production commercial",
                    "{mine_name} annual production tonnes per year",
                    "{mine_name} production end closure date",
                    "{mine_name} ore production volume statistics",
                    "{mine_name} mining output capacity",
                    "{mine_name} production history timeline"
                ],
                "focus": ["start_date", "end_date", "production_rate", "ore_type", "production_volumes"]
            },
            "technical_specs": {
                "name": "Technical Specifications & Area",
                "keywords": [
                    "{mine_name} mine area square kilometers km2",
                    "{mine_name} mining concession lease area",
                    "{mine_name} pit dimensions size depth",
                    "{mine_name} mine technical specifications",
                    "{mine_name} mining method open pit underground",
                    "{mine_name} reserves geology deposit",
                    "{mine_name} processing plant capacity"
                ],
                "focus": ["area_km2", "depth", "reserves", "geology", "dimensions", "capacity"]
            },
            "financial_data": {
                "name": "Financial & Restoration Costs",
                "keywords": [
                    "{mine_name} restoration costs CAD dollars",
                    "{mine_name} closure costs environmental bond",
                    "{mine_name} rehabilitation costs Quebec",
                    "{mine_name} environmental financial assurance",
                    "{mine_name} cleanup costs remediation",
                    "{mine_name} mine closure cost estimate"
                ],
                "focus": ["closure_costs", "restoration_costs", "financial_assurance", "cost_estimates"]
            },
            "government_data": {
                "name": "Government & Regulatory Records",
                "keywords": [
                    "{mine_name} MERN Quebec mining permit",
                    "{mine_name} GESTIM database record",
                    "{mine_name} environmental assessment Quebec",
                    "{mine_name} mining permit license MRNF",
                    "{mine_name} government mining record",
                    "site:mrnf.gouv.qc.ca {mine_name}"
                ],
                "focus": ["permits", "environmental_data", "government_records", "regulatory_status"]
            },
            "coordinates_location": {
                "name": "Precise Location & Coordinates",
                "keywords": [
                    "{mine_name} coordinates latitude longitude",
                    "{mine_name} mine location GPS coordinates",
                    "{mine_name} Quebec mine map location",
                    "{mine_name} UTM coordinates mining claim",
                    "{mine_name} geographic location degrees"
                ],
                "focus": ["coordinates", "location", "geographic_data", "mapping"]
            }
        }
        
        # Erweiterte Quebec-spezifische Suchterms und Datenquellen
        self.quebec_specific_terms = [
            "MERN", "GESTIM", "MRNF", "Quebec mining", 
            "ministère ressources naturelles", "exploration minière", "exploitation minière",
            "SEDAR+ mining", "Quebec mineral deposits", "mining Quebec statistics",
            "sites.google.com/view/gestim", "mrnf.gouv.qc.ca", "donneesquebec.ca"
        ]
        
        # Spezifische Suchdomains für bessere Quellenqualität
        self.priority_domains = [
            "mrnf.gouv.qc.ca", "sedarplus.ca", "sec.gov", "mining.com",
            "northernminer.com", "cim.org", "aem.gouv.qc.ca",
            "donneesquebec.ca", "statcan.gc.ca", "nrcan.gc.ca"
        ]
        
        # Erweiterte und spezialisierte Prompts für detaillierte Datenextraktion
        self.specialized_prompts = {
            "basic_info": """
You are a multilingual mining data specialist. Extract ALL available basic information for the mine '{mine_name}' in Quebec, Canada. 

- Return a JSON object with ALL of the following fields (even if empty):
  - operator, betreiber, exploitant
  - status, aktivitaetsstatus, statut
  - mine_type, minentyp, type_mine
  - coordinates, koordinaten, coordonnées
  - location, standort, emplacement
  - area_km2, flaeche_km2, superficie_km2
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "operator": "...",
  "status": "...",
  "mine_type": "...",
  "coordinates": "...",
  "location": "...",
  "area_km2": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
""",
            "production_data": """
You are a multilingual mining data specialist. Extract ALL available production and timeline data for the mine '{mine_name}' in Quebec, Canada.

- Return a JSON object with ALL of the following fields (even if empty):
  - production_start_year, produktionsstart, année_début_production
  - production_end_year, produktionsende, année_fin_production
  - annual_production_tonnes, foerdermenge_jahr, production_annuelle_tonnes
  - commodity_type, rohstoffabbau, type_ressource
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "production_start_year": "...",
  "production_end_year": "...",
  "annual_production_tonnes": "...",
  "commodity_type": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
""",
            "technical_specs": """
You are a multilingual mining data specialist. Extract ALL available technical specifications for the mine '{mine_name}' in Quebec, Canada.

- Return a JSON object with ALL of the following fields (even if empty):
  - mine_type, minentyp, type_mine
  - area_km2, flaeche_km2, superficie_km2
  - depth, tiefe, profondeur
  - reserves, reserven, réserves
  - geology, geologie, géologie
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "mine_type": "...",
  "area_km2": "...",
  "depth": "...",
  "reserves": "...",
  "geology": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
""",
            "financial_data": """
You are a multilingual mining data specialist. Extract ALL available financial and restoration cost data for the mine '{mine_name}' in Quebec, Canada.

- Return a JSON object with ALL of the following fields (even if empty):
  - restoration_costs_cad, restaurationskosten_cad, coûts_restauration_cad
  - closure_costs, schliessungskosten, coûts_fermeture
  - financial_assurance, finanzielle_sicherheit, garantie_financière
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "restoration_costs_cad": "...",
  "closure_costs": "...",
  "financial_assurance": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
""",
            "government_data": """
You are a multilingual mining data specialist. Extract ALL available government and regulatory data for the mine '{mine_name}' in Quebec, Canada.

- Return a JSON object with ALL of the following fields (even if empty):
  - permits, genehmigungen, permis
  - environmental_data, umweltdaten, données_environnementales
  - government_records, behördendaten, données_gouvernementales
  - regulatory_status, regulatorischer_status, statut_réglementaire
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "permits": "...",
  "environmental_data": "...",
  "government_records": "...",
  "regulatory_status": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
""",
            "coordinates_location": """
You are a multilingual mining data specialist. Extract ALL available location and coordinates data for the mine '{mine_name}' in Quebec, Canada.

- Return a JSON object with ALL of the following fields (even if empty):
  - coordinates, koordinaten, coordonnées
  - location, standort, emplacement
  - utm_coordinates, utm_koordinaten, coordonnées_utm
  - sources (list of URLs)
- Use English, German and French synonyms for each field.
- Cite all sources (URLs) for each data point.
- Example output:
{
  "coordinates": "...",
  "location": "...",
  "utm_coordinates": "...",
  "sources": ["...", "..."]
}
- If a field is not available, set it to an empty string.
- Antwort auch auf Deutsch und Französisch, falls möglich.

Return ONLY a valid JSON object, no explanation, no markdown, no comments, no code block.
"""
        }

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60))
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def research_mine_comprehensive(self, mine_name: str, focus_areas: list = None, timeout: int = 120, progress_callback=None) -> 'EnhancedMineResearchResult':
        parser = None
        try:
            from src.data_parser import MiningDataParser
            parser = MiningDataParser()
        except Exception:
            pass
        import re
        variants = set()
        if parser:
            name_orig, name_no_accents = parser._normalize_mine_name(mine_name)
            variants.add(name_orig)
            variants.add(name_no_accents)
            # Zusätzliche Varianten generieren
            base_names = [name_orig, name_no_accents]
            for n in base_names:
                n_clean = n.replace('–', '-').replace('—', '-').replace('_', ' ').replace('.', ' ').strip()
                n_no_space = n_clean.replace(' ', '')
                n_dash = n_clean.replace(' ', '-')
                n_space = n_clean.replace('-', ' ')
                variants.add(n_clean)
                variants.add(n_no_space)
                variants.add(n_dash)
                variants.add(n_space)
                # Varianten mit "Gold", "Project", "Mine" etc.
                for suffix in ["Gold", "Project", "Mine", "Mines", "Deposit", "Projek", "Projet"]:
                    variants.add(f"{n_clean} {suffix}")
                    variants.add(f"{n_clean}-{suffix}")
            # Optional: Synonyme aus Vorlage (wenn vorhanden, z.B. als Komma-getrennte Liste im Namen)
            if "," in mine_name:
                for alt in mine_name.split(","):
                    alt = alt.strip()
                    if alt and alt not in variants:
                        variants.add(alt)
        else:
            variants.add(mine_name)
        best_result = None
        best_score = -1
        for variant in variants:
            self.logger.info(f"🔍 Enhanced Suche für Namensvariante: {variant}")
            result = await self._research_single_variant(variant, focus_areas, timeout, progress_callback)
            if result and result.data_completeness_score > best_score:
                best_result = result
                best_score = result.data_completeness_score
        return best_result if best_result else EnhancedMineResearchResult(mine_name=mine_name)

    async def _research_single_variant(self, mine_name: str, focus_areas: list, timeout: int, progress_callback=None) -> 'EnhancedMineResearchResult':
        return await self._original_research_mine_comprehensive(mine_name, focus_areas, timeout, progress_callback)

    async def _original_research_mine_comprehensive(self, mine_name: str, focus_areas: list, timeout: int, progress_callback=None) -> 'EnhancedMineResearchResult':
        """
        Führt umfassende mehrstufige Research durch
        
        Args:
            mine_name: Name der Mine
            progress_callback: Callback für Progress-Updates
            
        Returns:
            EnhancedMineResearchResult mit allen gefundenen Daten
        """
        
        self.logger.info(f"Starting comprehensive research for {mine_name}")
        start_time = time.time()
        
        result = EnhancedMineResearchResult(mine_name=mine_name)
        
        total_phases = len(self.research_phases)
        completed_phases = 0
        
        # Führe alle Research-Phasen durch
        for phase_id, phase_config in self.research_phases.items():
            
            if progress_callback:
                progress = (completed_phases / total_phases) * 100
                progress_callback(progress, f"Phase {completed_phases+1}/{total_phases}: {phase_config['name']}")
            
            self.logger.info(f"Starting phase: {phase_config['name']}")
            
            # Führe Research-Phase durch
            phase_results = await self._research_phase(mine_name, phase_id, phase_config)
            
            # Integriere Ergebnisse
            await self._integrate_phase_results(result, phase_results, phase_id)
            
            result.research_phases_completed.append(phase_id)
            completed_phases += 1
            
            # Kurze Pause zwischen Phasen
            await asyncio.sleep(2)
        
        # Cross-Validation und Datenverbesserung
        if progress_callback:
            progress_callback(90, "Cross-validating and improving data...")
        
        await self._cross_validate_data(result)
        await self._enhance_missing_data(result)
        await self._log_missing_fields(result)
        
        # Berechne Completeness Score
        result.data_completeness_score = self._calculate_completeness_score(result)
        result.total_research_time = time.time() - start_time
        
        if progress_callback:
            progress_callback(100, f"Research completed! {len(result.data_points)} data points found")
        
        self.logger.info(f"Research completed for {mine_name}: {len(result.data_points)} data points, "
                   f"{len(result.all_sources)} sources, {result.total_research_time:.1f}s")
        
        return result

    async def _research_phase(self, mine_name: str, phase_id: str, 
                            phase_config: Dict) -> Dict[str, Any]:
        """Führt eine einzelne Research-Phase durch (jetzt Multi-API)."""
        phase_results = {
            "sources": [],
            "data_extracted": {},
            "search_queries_used": [],
            "mine_name": mine_name
        }
        search_queries = []
        for keyword_template in phase_config["keywords"]:
            query = keyword_template.format(mine_name=mine_name)
            search_queries.append(query)
            for quebec_term in self.quebec_specific_terms[:2]:
                quebec_query = f"{query} {quebec_term}"
                search_queries.append(quebec_query)
        phase_results["search_queries_used"] = search_queries
        # Multi-API-Suche für jede Query
        self.current_phase_debug_log = []  # Reset für diese Phase
        for query in search_queries[:8]:
            try:
                self.logger.debug(f"Multi-API-Search: {query}")
                api_results = await self._multi_api_search(query, phase_id)
                for search_result in api_results:
                    # Extrahiere Daten mit spezialisiertem Prompt (wie bisher)
                    extracted_data = await self._extract_specialized_data(
                        search_result, mine_name, phase_id
                    )
                    if "sources" in search_result:
                        phase_results["sources"].extend(search_result["sources"])
                    if extracted_data:
                        phase_results["data_extracted"].update(extracted_data)
                await asyncio.sleep(1.5)
            except Exception as e:
                self.logger.warning(f"Multi-API-Search fehlgeschlagen für '{query}': {e}")
                continue
        # Debug-Log für diese Phase an phase_results anhängen
        phase_results['api_debug_log'] = getattr(self, 'current_phase_debug_log', [])
        return phase_results

    async def _multi_api_search(self, query: str, phase_id: str) -> list:
        """Führt die Suche parallel über alle verfügbaren APIs durch und sammelt die Ergebnisse. Erweitertes Logging für API-Nutzung."""
        results = []
        tasks = []
        api_status_log = []
        # Perplexity
        if 'perplexity' in self.api_clients and self.api_clients['perplexity']:
            key_status = 'OK' if self.api_clients['perplexity'] else 'MISSING'
            self.logger.info(f"[API-LOG] Perplexity: Key {key_status}, Query: '{query[:40]}...'")
            tasks.append(self._perplexity_search(query, phase_id))
            api_status_log.append({'api': 'perplexity', 'key': key_status, 'query': query})
        # Tavily
        if 'tavily' in self.api_clients and self.api_clients['tavily']:
            key_status = 'OK' if self.api_clients['tavily'] else 'MISSING'
            self.logger.info(f"[API-LOG] Tavily: Key {key_status}, Query: '{query[:40]}...'")
            tasks.append(self._tavily_search(query, phase_id))
            api_status_log.append({'api': 'tavily', 'key': key_status, 'query': query})
        # OpenAI (Platzhalter)
        if 'openai' in self.api_clients and self.api_clients['openai']:
            key_status = 'OK' if self.api_clients['openai'] else 'MISSING'
            self.logger.info(f"[API-LOG] OpenAI: Key {key_status}, Query: '{query[:40]}...'")
            tasks.append(self._openai_search(query, phase_id))
            api_status_log.append({'api': 'openai', 'key': key_status, 'query': query})
        # Gemini (Platzhalter)
        if 'gemini' in self.api_clients and self.api_clients['gemini']:
            key_status = 'OK' if self.api_clients['gemini'] else 'MISSING'
            self.logger.info(f"[API-LOG] Gemini: Key {key_status}, Query: '{query[:40]}...'")
            tasks.append(self._gemini_search(query, phase_id))
            api_status_log.append({'api': 'gemini', 'key': key_status, 'query': query})
        # DeepSeek (Platzhalter)
        if 'deepseek' in self.api_clients and self.api_clients['deepseek']:
            key_status = 'OK' if self.api_clients['deepseek'] else 'MISSING'
            self.logger.info(f"[API-LOG] DeepSeek: Key {key_status}, Query: '{query[:40]}...'")
            tasks.append(self._deepseek_search(query, phase_id))
            api_status_log.append({'api': 'deepseek', 'key': key_status, 'query': query})
        if not tasks:
            self.logger.error("Keine API-Clients für Multi-API-Suche konfiguriert!")
            return []
        # Parallel ausführen
        api_results = await asyncio.gather(*tasks, return_exceptions=True)
        for idx, res in enumerate(api_results):
            api_name = api_status_log[idx]['api'] if idx < len(api_status_log) else 'unknown'
            if isinstance(res, dict) and res:
                self.logger.info(f"[API-LOG] {api_name}: SUCCESS for Query '{query[:40]}...'")
                results.append(res)
                api_status_log[idx]['status'] = 'success'
            elif isinstance(res, list):
                self.logger.info(f"[API-LOG] {api_name}: SUCCESS (list) for Query '{query[:40]}...'")
                results.extend(res)
                api_status_log[idx]['status'] = 'success-list'
            elif isinstance(res, Exception):
                self.logger.warning(f"[API-LOG] {api_name}: ERROR: {res}")
                api_status_log[idx]['status'] = f'error: {res}'
            else:
                self.logger.warning(f"[API-LOG] {api_name}: EMPTY result for Query '{query[:40]}...'")
                api_status_log[idx]['status'] = 'empty'
        # Rückgabe und Logging für Debug-Log
        if hasattr(self, 'current_phase_debug_log'):
            self.current_phase_debug_log.extend(api_status_log)
        else:
            self.current_phase_debug_log = api_status_log
        return results

    async def _perplexity_search(self, query: str, phase_id: str) -> Optional[Dict[str, Any]]:
        """Führt Perplexity-Suche mit phasen-spezifischem Prompt durch"""
        
        try:
            from config.api_keys import APIConfig
            
            if not APIConfig.PERPLEXITY_API_KEY:
                self.logger.error("Perplexity API key not configured")
                return None
            
            # Phasen-spezifischer Prompt mit mine_name formatierung
            if phase_id in self.specialized_prompts:
                # Extrahiere mine_name aus der query (erste Wörter vor "mine")
                mine_name = query.split(' mine')[0].strip()
                if ' ' in mine_name:
                    mine_name = mine_name.split()[-1]  # Letztes Wort falls mehrere
                
                system_prompt = self.specialized_prompts[phase_id].format(mine_name=mine_name)
            else:
                system_prompt = """
You are a mining industry researcher. Search for detailed, specific information about mining operations in Quebec, Canada.
Focus on factual data, numerical values, dates, and official sources.
Always cite your sources and indicate confidence levels for extracted data.
"""
            
            headers = {
                "Authorization": f"Bearer {APIConfig.PERPLEXITY_API_KEY}",
                "Content-Type": "application/json"
            }
            
            # Simplified payload to avoid potential issues
            payload = {
                "model": "llama-3.1-sonar-large-128k-online",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query}
                ],
                "max_tokens": 1500,
                "temperature": 0.1
            }
            
            self.logger.debug(f"Making Perplexity API call for query: {query[:50]}...")
            
            async with self.session.post(
                "https://api.perplexity.ai/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                self.logger.debug(f"API response status: {response.status}")
                
                if response.status == 200:
                    try:
                        # Get raw response text first
                        raw_response = await response.text()
                        self.logger.debug(f"Raw response length: {len(raw_response)}")
                        
                        # Parse JSON
                        result = json.loads(raw_response)
                        
                        # Validate response structure
                        if not isinstance(result, dict):
                            self.logger.error(f"API returned non-dict response: {type(result)}")
                            return None
                        
                        # Extract content
                        content = ""
                        if "choices" in result and isinstance(result["choices"], list) and len(result["choices"]) > 0:
                            choice = result["choices"][0]
                            if isinstance(choice, dict) and "message" in choice:
                                message = choice["message"]
                                if isinstance(message, dict) and "content" in message:
                                    content = message["content"]
                        
                        if not content:
                            self.logger.warning("No content found in API response")
                            self.logger.debug(f"Response structure: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
                            return None
                        
                        self.logger.debug(f"Content extracted, length: {len(content)}")
                        
                        # Create basic source (simplified approach)
                        sources = []
                        
                        # Try to extract citations if available
                        if "citations" in result and isinstance(result["citations"], list):
                            for citation in result["citations"]:
                                if isinstance(citation, dict):
                                    url = citation.get("url", "")
                                    title = citation.get("title", "Unknown Source")
                                    
                                    if url:  # Only add if URL exists
                                        source = ResearchSource(
                                            url=url,
                                            title=title,
                                            date_accessed=datetime.now().isoformat(),
                                            content_type=self._classify_source_type(url),
                                            reliability_score=self._assess_source_reliability(url),
                                            content_snippet=content[:200] + "..." if len(content) > 200 else content
                                        )
                                        source.data_points_extracted.append(f"Phase: {phase_id}")
                                        sources.append(source)
                        
                        # Always create at least one source reference
                        if not sources:
                            general_source = ResearchSource(
                                url="https://perplexity.ai/search",
                                title=f"Perplexity AI Search: {query[:50]}...",
                                date_accessed=datetime.now().isoformat(),
                                content_type="ai_search",
                                reliability_score=0.6,
                                content_snippet=content[:200] + "..." if len(content) > 200 else content
                            )
                            general_source.data_points_extracted.append(f"AI Search: {phase_id}")
                            sources.append(general_source)
                        
                        self.logger.debug(f"Search successful: {len(sources)} sources found")
                        
                        return {
                            "content": content,
                            "sources": sources,
                            "query": query,
                            "phase": phase_id
                        }
                    
                    except json.JSONDecodeError as e:
                        self.logger.error(f"Failed to parse API response as JSON: {e}")
                        self.logger.error(f"Raw response: {raw_response[:500]}...")
                        return None
                    
                    except Exception as e:
                        self.logger.error(f"Error processing API response: {e}")
                        self.logger.error(f"Response type: {type(result) if 'result' in locals() else 'Unknown'}")
                        return None
                
                else:
                    error_text = await response.text()
                    self.logger.error(f"Perplexity API error {response.status}: {error_text[:200]}")
                    return None
                    
        except asyncio.TimeoutError:
            self.logger.error(f"Timeout during Perplexity search for: {query[:50]}")
            return None
        except Exception as e:
            self.logger.error(f"Perplexity search failed for '{query[:50]}': {e}")
            return None

    async def _tavily_search(self, query: str, phase_id: str) -> dict:
        """Führt eine Tavily-Suche durch (sofern implementiert)."""
        try:
            client = self.api_clients.get('tavily')
            if not client:
                return {}
            # Platzhalter: Implementiere hier die echte Tavily-Logik
            result = await client.search_government_data(query)
            return result if result else {}
        except Exception as e:
            self.logger.warning(f"Tavily-Suche fehlgeschlagen: {e}")
            return {}

    async def _openai_search(self, query: str, phase_id: str) -> dict:
        """Platzhalter für OpenAI-Search."""
        self.logger.info(f"OpenAI-Search für '{query}' (noch nicht implementiert)")
        return {}

    async def _gemini_search(self, query: str, phase_id: str) -> dict:
        """Platzhalter für Gemini-Search."""
        self.logger.info(f"Gemini-Search für '{query}' (noch nicht implementiert)")
        return {}

    async def _deepseek_search(self, query: str, phase_id: str) -> dict:
        """Platzhalter für DeepSeek-Search."""
        self.logger.info(f"DeepSeek-Search für '{query}' (noch nicht implementiert)")
        return {}

    async def _extract_specialized_data(self, search_results: Dict[str, Any], 
                                      mine_name: str, phase_id: str) -> Dict[str, Any]:
        """Extrahiert spezifische Daten basierend auf Phase und Inhalt"""
        
        content = search_results.get("content", "")
        if not content:
            return {}
        
        extracted = {}
        
        try:
            # Verwende einen zweiten Perplexity-Call für strukturierte Datenextraktion
            extraction_prompt = f"""
You are a mining data extraction specialist. Extract PRECISE data from the following text about {mine_name} mine.

Source Text: {content}

Extract data in this EXACT JSON format (use null if not found, include source quotes):
{{
    "mine_operator": {{
        "value": "exact company name",
        "confidence": 0.8,
        "source_quote": "relevant text from source"
    }},
    "production_start_year": {{
        "value": 2010,
        "confidence": 0.9,
        "source_quote": "text mentioning start date"
    }},
    "annual_production_tonnes": {{
        "value": 450000,
        "confidence": 0.7,
        "source_quote": "production volume text"
    }},
    "mine_area_km2": {{
        "value": 15.5,
        "confidence": 0.6,
        "source_quote": "area/size text"
    }},
    "restoration_costs_cad": {{
        "value": 85000000,
        "confidence": 0.8,
        "source_quote": "restoration cost text"
    }}
}}

IMPORTANT: 
- Return ONLY valid JSON
- Use exact numbers without units
- Set confidence between 0.1-1.0
- Use null for missing data
- Ensure proper JSON syntax with commas
"""
            
            # Strukturierte Extraktion mit robuster JSON-Behandlung
            extraction_result = await self._robust_json_extraction(extraction_prompt)
            
            if extraction_result:
                extracted.update(extraction_result)
            
        except Exception as e:
            self.logger.warning(f"Data extraction failed: {e}")
        
        return extracted
    
    async def _robust_json_extraction(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Robustes Parsen von KI-JSON-Antworten mit Fallbacks, Reparatur und Logging"""
        import re, time
        # Entferne Markdown-Codeblöcke und KI-Kommentare
        prompt = re.sub(r'```json|```', '', prompt, flags=re.IGNORECASE).strip()
        prompt = re.sub(r'#.*', '', prompt)  # Zeilen mit # entfernen
        prompt = re.sub(r'//.*', '', prompt)  # Zeilen mit // entfernen
        prompt = re.sub(r'(?i)explanation:.*', '', prompt)  # "explanation:" entfernen
        # Entferne alles vor dem ersten '{' und nach dem letzten '}'
        if '{' in prompt and '}' in prompt:
            start = prompt.find('{')
            end = prompt.rfind('}') + 1
            prompt = prompt[start:end]
        prompt = prompt.strip()
        # Fallback 1: Standard JSON-Parsing
        try:
            return json.loads(prompt)
        except Exception as e:
            self.logger.warning(f"JSONDecodeError (1): {e}")
        # Fallback 2: Reparatur von häufigen Fehlern
        try:
            json_str = prompt
            # Fehlende Anführungszeichen um Keys
            json_str = re.sub(r'([,{]\s*)([a-zA-Z0-9_]+)(\s*:)','"\\2"\\3', json_str)
            # Fehlende Kommas zwischen Feldern
            json_str = re.sub(r'}\s*{', '}, {', json_str)
            # Doppelte Kommas
            json_str = re.sub(r',\s*,', ',', json_str)
            # Komma vor schließender Klammer
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)
            # Entferne Steuerzeichen
            json_str = re.sub(r'[\x00-\x1F\x7F]', '', json_str)
            # Fehlende geschweifte Klammern ergänzen
            if not json_str.strip().startswith('{'):
                json_str = '{' + json_str
            if not json_str.strip().endswith('}'):
                json_str = json_str + '}'
            return json.loads(json_str)
        except Exception as e:
            self.logger.warning(f"JSONDecodeError (2): {e}")
        # Fallback 3: ast.literal_eval
        try:
            data = ast.literal_eval(prompt)
            if isinstance(data, dict):
                return data
        except Exception as e:
            self.logger.warning(f"ast.literal_eval failed: {e}")
        # Fallback 4: Regex für einfache Key-Value-Paare und Arrays
        try:
            extracted = {}
            # Key-Value-Paare
            pattern = r'"([^"\n]+)"\s*:\s*"([^"\n]*)"'
            for match in re.finditer(pattern, prompt):
                key, value = match.groups()
                extracted[key.strip()] = value.strip()
            # Arrays (z.B. "sources": [ ... ])
            arr_pattern = r'"([^"]+)"\s*:\s*\[(.*?)\]'
            for match in re.finditer(arr_pattern, prompt, re.DOTALL):
                key, arr = match.groups()
                arr_items = [item.strip().strip('"') for item in arr.split(',') if item.strip()]
                extracted[key.strip()] = arr_items
            if extracted:
                return extracted
        except Exception as e:
            self.logger.warning(f"Regex extraction failed: {e}")
        # Fallback 5: Nur ein Key-Value-Paar (z.B. "operator": "...")
        try:
            single_kv = re.search(r'"([^"\n]+)"\s*:\s*"([^"\n]*)"', prompt)
            if single_kv:
                key, value = single_kv.groups()
                return {key.strip(): value.strip()}
        except Exception as e:
            self.logger.warning(f"Single key-value fallback failed: {e}")
        # Logging des fehlerhaften Outputs inkl. Fehlerursache
        try:
            ts = time.strftime("%Y%m%d_%H%M%S")
            outdir = "output"
            os.makedirs(outdir, exist_ok=True)
            fname = os.path.join(outdir, f"failed_ai_output_{ts}.json")
            with open(fname, "w", encoding="utf-8") as f:
                f.write(prompt)
            self.logger.error(f"KI-Output konnte nicht geparst werden. Original gespeichert in: {fname}")
        except Exception as e:
            self.logger.error(f"Fehler beim Logging des fehlerhaften KI-Outputs: {e}")
        return None

    async def _structured_extraction(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Führt strukturierte Datenextraktion durch"""
        
        try:
            from config.api_keys import APIConfig
            
            headers = {
                "Authorization": f"Bearer {APIConfig.PERPLEXITY_API_KEY}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "llama-3.1-sonar-large-128k-online",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.0
            }
            
            async with self.session.post(
                "https://api.perplexity.ai/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        
                        # Debug: Check if result is actually a dict
                        if not isinstance(result, dict):
                            self.logger.error(f"Structured extraction: Unexpected API response type: {type(result)}")
                            return None
                        
                        if "choices" in result and result["choices"]:
                            content = result["choices"][0]["message"]["content"]
                            
                            # Versuche JSON zu extrahieren
                            try:
                                # Finde JSON im Content
                                start = content.find("{")
                                end = content.rfind("}") + 1
                                
                                if start >= 0 and end > start:
                                    json_str = content[start:end]
                                    return json.loads(json_str)
                                else:
                                    self.logger.warning("No JSON found in structured extraction response")
                                    return None
                                    
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"Could not parse JSON from extraction result: {e}")
                                self.logger.debug(f"Content was: {content[:300]}")
                                return None
                        else:
                            self.logger.warning("No choices in structured extraction API response")
                            return None
                            
                    except json.JSONDecodeError as e:
                        self.logger.error(f"Structured extraction: Failed to parse JSON response: {e}")
                        return None
                    
                    except Exception as e:
                        self.logger.error(f"Structured extraction: Error processing API response: {e}")
                        return None
                else:
                    self.logger.error(f"Structured extraction API error: {response.status}")
                    return None
                        
        except Exception as e:
            self.logger.error(f"Structured extraction failed: {e}")
            return None

    async def _integrate_phase_results(self, result: EnhancedMineResearchResult, phase_results: Dict[str, Any], phase_id: str):
        """Integriert die Ergebnisse einer Phase mit Fuzzy Matching und Feld-Synonym-Mapping. Ergänzt das API-Debug-Log im Ergebnis."""
        # Felder zusammenführen (Fuzzy + Synonym-Mapping)
        for field, value in phase_results.get("data_extracted", {}).items():
            std_field = map_field_synonym(field)
            # Fuzzy-Matching gegen bestehende Felder (nur auf Standardnamen)
            best_match = None
            best_score = 0
            for existing_field in result.data_points.keys():
                score = fuzz.ratio(std_field.lower(), map_field_synonym(existing_field).lower())
                if score > best_score:
                    best_score = score
                    best_match = existing_field
            if best_score >= FUZZY_FIELD_THRESHOLD:
                # Felder sind ähnlich, zusammenführen (z.B. Wert ergänzen, falls leer)
                if not result.data_points[best_match].value and value:
                    result.data_points[best_match].value = value
            else:
                # Neues Feld (immer als Standardname)
                result.add_data_point(std_field, value, 0.7, [], method=f"phase_{phase_id}")
        # Quellen zusammenführen (Fuzzy)
        for src in phase_results.get("sources", []):
            src_url = src if isinstance(src, str) else src.get('url', '')
            if not src_url:
                continue
            found = False
            for existing_src in result.all_sources:
                score = fuzz.ratio(src_url.lower(), getattr(existing_src, 'url', '').lower())
                if score >= FUZZY_SOURCE_THRESHOLD:
                    found = True
                    break
            if not found:
                from dataclasses import asdict
                if hasattr(result, 'all_sources'):
                    if isinstance(src, dict):
                        try:
                            from src.enhanced_web_researcher import ResearchSource
                            result.all_sources.append(ResearchSource(**src))
                        except Exception:
                            result.all_sources.append(src)
                    else:
                        try:
                            from src.enhanced_web_researcher import ResearchSource
                            result.all_sources.append(ResearchSource(url=src_url, title="", date_accessed="", content_type="", reliability_score=0.5, content_snippet=""))
                        except Exception:
                            result.all_sources.append(src_url)
        # API-Debug-Log für diese Phase im Ergebnis speichern
        if not hasattr(result, 'api_debug_log'):
            result.api_debug_log = {}
        result.api_debug_log[phase_id] = phase_results.get('api_debug_log', [])

    def _find_relevant_sources(self, sources: List[ResearchSource], 
                              field_name: str, source_quote: str) -> List[ResearchSource]:
        """Findet die relevantesten Quellen für ein Datenfeld"""
        
        if not sources:
            return []
        
        relevant_sources = []
        
        # Priorität 1: Quellen die das spezifische Zitat enthalten
        if source_quote and len(source_quote.strip()) > 10:
            for source in sources:
                if source_quote.lower() in source.content_snippet.lower():
                    relevant_sources.append(source)
        
        # Priorität 2: Quellen die den Feldnamen enthalten
        if not relevant_sources:
            field_keywords = field_name.lower().replace("_", " ").split()
            for source in sources:
                snippet_lower = source.content_snippet.lower()
                if any(keyword in snippet_lower for keyword in field_keywords):
                    relevant_sources.append(source)
        
        # Priorität 3: Quellen mit höchster Zuverlässigkeit
        if not relevant_sources:
            # Sortiere nach Zuverlässigkeit und nehme die besten
            sorted_sources = sorted(sources, key=lambda s: s.reliability_score, reverse=True)
            relevant_sources = sorted_sources[:2]  # Top 2 most reliable
        
        return relevant_sources[:3]  # Maximal 3 Quellen pro Feld

    def _classify_source_type(self, url: str) -> str:
        """Erweiterte Klassifizierung des Quellentyps basierend auf URL"""
        
        url_lower = url.lower()
        
        # Regierungsquellen (höchste Priorität)
        if any(domain in url_lower for domain in [
            "gov.ca", "mrnf.gouv.qc.ca", "gestim", "aem.gouv.qc.ca", 
            "donneesquebec.ca", "statcan.gc.ca", "nrcan.gc.ca"
        ]):
            return "government_data"
        
        # Unternehmensberichte und Filings
        elif any(domain in url_lower for domain in [
            "sedarplus.ca", "sec.gov", "tsx.com", "csx.ca"
        ]):
            return "company_report"
        
        # Technische und wissenschaftliche Berichte
        elif any(domain in url_lower for domain in [
            "cim.org", "smebc.ca", "pdac.ca", "researchgate.net"
        ]):
            return "technical_report"
        
        # Bergbauindustrie-Publikationen
        elif any(domain in url_lower for domain in [
            "mining.com", "northernminer.com", "mining-journal.com", 
            "kitco.com", "miningweekly.com"
        ]):
            return "industry_report"
        
        # Nachrichten und Medien
        elif any(domain in url_lower for domain in [
            "cbc.ca", "globalnews.ca", "lapresse.ca", "ledevoir.com",
            "reuters.com", "bloomberg.com"
        ]):
            return "news"
        
        # Finanzquellen
        elif any(domain in url_lower for domain in [
            "yahoo.com/finance", "marketwatch.com", "fool.ca"
        ]):
            return "financial_data"
        
        else:
            return "other"

    def _assess_source_reliability(self, url: str) -> float:
        """Erweiterte Bewertung der Quellenzuverlässigkeit (0.0-1.0)"""
        
        url_lower = url.lower()
        
        # Sehr hohe Zuverlässigkeit (0.95)
        if any(domain in url_lower for domain in [
            "mrnf.gouv.qc.ca", "gestim", "aem.gouv.qc.ca", "statcan.gc.ca", "nrcan.gc.ca"
        ]):
            return 0.95
        
        # Hohe Zuverlässigkeit (0.90)
        elif any(domain in url_lower for domain in [
            "sedarplus.ca", "sec.gov", "donneesquebec.ca", "gov.ca"
        ]):
            return 0.90
        
        # Gute Zuverlässigkeit (0.80)
        elif any(domain in url_lower for domain in [
            "cim.org", "smebc.ca", "pdac.ca", "tsx.com"
        ]):
            return 0.80
        
        # Mittlere Zuverlässigkeit (0.70)
        elif any(domain in url_lower for domain in [
            "mining.com", "northernminer.com", "mining-journal.com"
        ]):
            return 0.70
        
        # Begrenzte Zuverlässigkeit (0.60)
        elif any(domain in url_lower for domain in [
            "kitco.com", "miningweekly.com", "reuters.com", "bloomberg.com"
        ]):
            return 0.60
        
        # Niedrige Zuverlässigkeit (0.40)
        elif any(domain in url_lower for domain in [
            "wikipedia.org", "yahoo.com", "facebook.com", "twitter.com"
        ]):
            return 0.40
        
        # Standard Zuverlässigkeit
        else:
            return 0.50

    def _calculate_field_confidence(self, field_name: str, value: Any, 
                                  phase_results: Dict[str, Any]) -> float:
        """Berechnet Confidence für ein spezifisches Datenfeld"""
        
        base_confidence = 0.6
        
        # Erhöhe Confidence basierend auf Quellqualität
        sources = phase_results.get("sources", [])
        if sources:
            avg_reliability = sum(s.reliability_score for s in sources) / len(sources)
            base_confidence += (avg_reliability - 0.5) * 0.3
        
        # Erhöhe Confidence für numerische Daten
        if isinstance(value, (int, float)):
            base_confidence += 0.1
        
        # Erhöhe Confidence für spezifische Felder mit guten Quellen
        if field_name in ["mine_operator", "commodity_type", "mine_status"]:
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)

    async def _cross_validate_data(self, result: EnhancedMineResearchResult):
        """Cross-validiert Daten aus verschiedenen Quellen"""
        
        # TODO: Implementiere Cross-Validation zwischen verschiedenen Datenquellen
        # Vergleiche Daten aus verschiedenen Phasen und markiere Widersprüche
        
        for field_name, data_point in result.data_points.items():
            if len(data_point.sources) > 1:
                data_point.cross_validated = True
                data_point.confidence += 0.1  # Erhöhe Confidence für cross-validierte Daten

    async def _enhance_missing_data(self, result: EnhancedMineResearchResult):
        """Versucht fehlende Daten durch zusätzliche gezielte Suchen zu finden"""
        
        # Erweiterte Liste kritischer Felder für gezielte Nachsuche
        critical_fields = [
            "production_start_year", "production_end_year", "annual_production_tonnes", 
            "mine_area_km2", "restoration_costs_cad", "coordinates_lat", "coordinates_lon",
            "total_production_tonnes", "commodity_type", "mine_operator"
        ]
        
        missing_critical = [field for field in critical_fields 
                          if field not in result.data_points]
        
        if missing_critical:
            self.logger.info(f"Attempting to find missing critical data: {missing_critical}")
            
            # Führe gezielte Suchen für fehlende Daten durch
            for field in missing_critical[:3]:  # Limit to avoid too many additional searches
                await self._targeted_field_search(result, field)

    async def _targeted_field_search(self, result: EnhancedMineResearchResult, field_name: str):
        """Führt gezielte Suche für ein spezifisches fehlendes Feld durch"""
        
        mine_name = result.mine_name
        
        # Erweiterte und spezifische Suchqueries für jedes Datenfeld
        field_queries = {
            "production_start_year": [
                f"{mine_name} production start date year commercial",
                f"{mine_name} first ore production began",
                f"{mine_name} mining operation commenced year",
                f"{mine_name} initial production startup",
                f"site:sedarplus.ca {mine_name} production start"
            ],
            "production_end_year": [
                f"{mine_name} production end closure date",
                f"{mine_name} mine closed ceased operations",
                f"{mine_name} final production year",
                f"{mine_name} decommissioned shutdown",
                f"site:mrnf.gouv.qc.ca {mine_name} closure"
            ],
            "annual_production_tonnes": [
                f"{mine_name} annual production tonnes per year",
                f"{mine_name} yearly output volume statistics",
                f"{mine_name} production rate tonnes daily",
                f"{mine_name} ore production capacity million tonnes",
                f"site:sedarplus.ca {mine_name} production figures"
            ],
            "mine_area_km2": [
                f"{mine_name} mine area square kilometers km2",
                f"{mine_name} mining lease concession area size",
                f"{mine_name} pit area hectares square km",
                f"{mine_name} mining property area dimensions",
                f"site:mrnf.gouv.qc.ca {mine_name} area"
            ],
            "restoration_costs_cad": [
                f"{mine_name} restoration costs CAD million dollars",
                f"{mine_name} closure bond environmental assurance",
                f"{mine_name} rehabilitation costs estimate Quebec",
                f"{mine_name} environmental cleanup costs financial",
                f"site:mrnf.gouv.qc.ca {mine_name} restoration costs"
            ],
            "coordinates_lat": [
                f"{mine_name} coordinates latitude longitude degrees",
                f"{mine_name} GPS location coordinates Quebec",
                f"{mine_name} mine location map coordinates",
                f"{mine_name} geographic coordinates UTM"
            ],
            "coordinates_lon": [
                f"{mine_name} coordinates longitude latitude",
                f"{mine_name} mine GPS coordinates Quebec",
                f"{mine_name} location geographic position"
            ],
            "mine_operator": [
                f"{mine_name} mine operator company owner",
                f"{mine_name} mining company operates",
                f"{mine_name} owned operated by company",
                f"site:sedarplus.ca {mine_name} operator"
            ],
            "commodity_type": [
                f"{mine_name} commodity gold copper iron ore",
                f"{mine_name} mineral type mined extracted",
                f"{mine_name} ore type primary commodity"
            ]
        }
        
        queries = field_queries.get(field_name, [])
        
        for query in queries:
            try:
                search_result = await self._perplexity_search(query, "targeted")
                
                if search_result:
                    extracted = await self._extract_specialized_data(
                        search_result, mine_name, "targeted"
                    )
                    
                    if field_name in extracted and extracted[field_name] is not None:
                        # Gefundene Daten hinzufügen
                        result.add_data_point(
                            field_name=field_name,
                            value=extracted[field_name],
                            confidence=0.7,  # Moderate confidence für targeted search
                            sources=search_result.get("sources", []),
                            method="targeted_search"
                        )
                        break  # Stop searching for this field
                
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.warning(f"Targeted search failed for {field_name}: {e}")

    def _calculate_completeness_score(self, result: EnhancedMineResearchResult) -> float:
        """Berechnet Completeness Score basierend auf gefundenen Daten"""
        
        # Wichtige Felder und ihre Gewichtung
        important_fields = {
            "mine_operator": 0.15,
            "production_start_year": 0.12,
            "production_end_year": 0.08,
            "annual_production_tonnes": 0.12,
            "commodity_type": 0.10,
            "mine_area_km2": 0.10,
            "mine_type": 0.08,
            "restoration_costs_cad": 0.10,
            "mine_status": 0.08,
            "coordinates_lat": 0.04,
            "coordinates_lon": 0.03
        }
        
        total_weight = sum(important_fields.values())
        achieved_weight = 0.0
        
        for field_name, weight in important_fields.items():
            if field_name in result.data_points:
                data_point = result.data_points[field_name]
                # Gewichte die Gewichtung mit der Confidence
                achieved_weight += weight * data_point.confidence
        
        return achieved_weight / total_weight

    def get_research_summary(self, result: EnhancedMineResearchResult) -> str:
        """Erstellt erweiterte Research-Zusammenfassung mit Quellendetails"""
        
        summary = f"""
🔍 Enhanced Research Summary for {result.mine_name}
{'='*60}

📊 Data Completeness: {result.data_completeness_score:.1%}
⏱️ Research Time: {result.total_research_time:.1f} seconds
🔍 Phases Completed: {len(result.research_phases_completed)}
📚 Total Sources: {len(result.all_sources)}
📋 Data Points Found: {len(result.data_points)}

📈 Extracted Data with Sources:
"""
        
        for field_name, data_point in result.data_points.items():
            confidence_bar = "█" * int(data_point.confidence * 10)
            
            # Formatiere Feldname für bessere Lesbarkeit
            display_name = field_name.replace('_', ' ').title()
            
            summary += f"\n  • {display_name}: {data_point.value}\n"
            summary += f"    Confidence: {confidence_bar} {data_point.confidence:.1%}\n"
            
            # Zeige Quellen für dieses Feld
            if data_point.sources:
                summary += f"    Sources ({len(data_point.sources)}): \n"
                for i, source in enumerate(data_point.sources[:2], 1):  # Max 2 sources per field
                    reliability_stars = "★" * int(source.reliability_score * 5)
                    summary += f"      {i}. {source.title[:50]}... ({reliability_stars})\n"
                    summary += f"         URL: {source.url[:60]}...\n"
            
            # Zeige Extractionsmethode wenn verfügbar
            if hasattr(data_point, 'extraction_method') and data_point.extraction_method:
                method_info = data_point.extraction_method
                if len(method_info) > 80:
                    method_info = method_info[:80] + "..."
                summary += f"    Method: {method_info}\n"
        
        summary += f"\n📚 Sources by Type and Reliability:\n"
        
        source_analysis = {}
        for source in result.all_sources:
            source_type = source.content_type.replace('_', ' ').title()
            if source_type not in source_analysis:
                source_analysis[source_type] = {
                    'count': 0,
                    'avg_reliability': 0.0,
                    'urls': []
                }
            
            source_analysis[source_type]['count'] += 1
            source_analysis[source_type]['avg_reliability'] += source.reliability_score
            source_analysis[source_type]['urls'].append(source.url)
        
        # Berechne Durchschnitte
        for source_type, stats in source_analysis.items():
            if stats['count'] > 0:
                stats['avg_reliability'] = stats['avg_reliability'] / stats['count']
        
        # Sortiere nach Zuverlässigkeit
        sorted_sources = sorted(source_analysis.items(), 
                              key=lambda x: x[1]['avg_reliability'], 
                              reverse=True)
        
        for source_type, stats in sorted_sources:
            reliability_stars = "★" * int(stats['avg_reliability'] * 5)
            summary += f"  • {source_type}: {stats['count']} sources ({reliability_stars} {stats['avg_reliability']:.1%})\n"
        
        # Füge Top-Quellen hinzu
        top_sources = sorted(result.all_sources, 
                           key=lambda s: s.reliability_score, 
                           reverse=True)[:3]
        
        if top_sources:
            summary += f"\n🏆 Top 3 Most Reliable Sources:\n"
            for i, source in enumerate(top_sources, 1):
                reliability_stars = "★" * int(source.reliability_score * 5)
                summary += f"  {i}. {source.title} ({reliability_stars})\n"
                summary += f"     {source.url}\n"
                summary += f"     Type: {source.content_type.replace('_', ' ').title()}\n"
        
        return summary

    async def _log_missing_fields(self, result: EnhancedMineResearchResult):
        """Erstellt ein Debug-Log für alle fehlenden Felder und deren Ursachen."""
        # Definiere alle erwarteten Felder (aus allen Phasen)
        expected_fields = set()
        for phase in self.research_phases.values():
            expected_fields.update(phase.get('focus', []))
        found_fields = set(result.data_points.keys())
        missing_fields = expected_fields - found_fields
        for field in missing_fields:
            log_entry = {
                'mine_name': result.mine_name,
                'field': field,
                'reason': 'not found',
                'phases_checked': list(result.research_phases_completed),
                'sources_used': [s.url for s in result.all_sources],
            }
            result.debug_log.append(log_entry)
        # Optional: Parsing-Fehler, API-Fehler etc. können hier ebenfalls ergänzt werden


# Factory function für einfache Verwendung
async def create_enhanced_researcher(config: Dict[str, str]) -> EnhancedMiningResearcher:
    """Erstellt Enhanced Mining Researcher Instanz"""
    
    researcher = EnhancedMiningResearcher(config)
    await researcher.__aenter__()
    return researcher


if __name__ == "__main__":
    # Test der Enhanced Research Engine
    async def test_enhanced_research():
        config = {"perplexity_key": "test-key"}
        
        async with EnhancedMiningResearcher(config) as researcher:
            result = await researcher.research_mine_comprehensive("Éléonore")
            
            print(researcher.get_research_summary(result))
            print(result.get_sources_summary())
    
    asyncio.run(test_enhanced_research())

# Mapping-Tabelle für Feldnamen-Synonyme (de/en/fr)
FIELD_SYNONYM_MAP = {
    'operator': ['operator', 'betreiber', 'exploitant'],
    'status': ['status', 'aktivitaetsstatus', 'statut'],
    'mine_type': ['mine_type', 'minentyp', 'type_mine'],
    'coordinates': ['coordinates', 'koordinaten', 'coordonnées'],
    'location': ['location', 'standort', 'emplacement'],
    'area_km2': ['area_km2', 'flaeche_km2', 'superficie_km2'],
    'restoration_costs': ['restoration_costs', 'restaurationskosten_cad', 'coûts_restauration_cad'],
    'closure_costs': ['closure_costs', 'schliessungskosten', 'coûts_fermeture'],
    'financial_assurance': ['financial_assurance', 'finanzielle_sicherheit', 'garantie_financière'],
    'permits': ['permits', 'genehmigungen', 'permis'],
    'environmental_data': ['environmental_data', 'umweltdaten', 'données_environnementales'],
    'government_records': ['government_records', 'behördendaten', 'données_gouvernementales'],
    'regulatory_status': ['regulatory_status', 'regulatorischer_status', 'statut_réglementaire'],
    'production_start_year': ['production_start_year', 'produktionsstart', 'année_début_production'],
    'production_end_year': ['production_end_year', 'produktionsende', 'année_fin_production'],
    'annual_production_tonnes': ['annual_production_tonnes', 'foerdermenge_jahr', 'production_annuelle_tonnes'],
    'commodity_type': ['commodity_type', 'rohstoffabbau', 'type_ressource'],
    'depth': ['depth', 'tiefe', 'profondeur'],
    'reserves': ['reserves', 'reserven', 'réserves'],
    'geology': ['geology', 'geologie', 'géologie'],
    'utm_coordinates': ['utm_coordinates', 'utm_koordinaten', 'coordonnées_utm'],
    # ... weitere Felder nach Bedarf ...
}

def map_field_synonym(field: str) -> str:
    """Mappt einen Feldnamen (egal in welcher Sprache) auf den Standardnamen."""
    field_lower = field.lower()
    for std, syns in FIELD_SYNONYM_MAP.items():
        if field_lower in [s.lower() for s in syns]:
            return std
    return field  # Fallback: Originalname
