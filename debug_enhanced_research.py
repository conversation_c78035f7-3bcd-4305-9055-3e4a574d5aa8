#!/usr/bin/env python3
"""
Debug Script for Enhanced Research Engine
Tests API calls with detailed debugging
"""

import sys
import os
import asyncio
import aiohttp
import json
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def debug_perplexity_api():
    """Debuggt die Perplexity API-Calls"""
    
    print("🔧 Debug: Perplexity API Call")
    print("=" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        if not APIConfig.PERPLEXITY_API_KEY:
            print("❌ Perplexity API key not found")
            return False
        
        print(f"✅ API Key found: {APIConfig.PERPLEXITY_API_KEY[:10]}...")
        
        # Simple test call
        headers = {
            "Authorization": f"Bearer {APIConfig.PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "llama-3.1-sonar-large-128k-online",
            "messages": [
                {"role": "user", "content": "What is the Éléonore mine in Quebec?"}
            ],
            "max_tokens": 500,
            "temperature": 0.1
        }
        
        print("🌐 Making API call...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.perplexity.ai/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                
                print(f"📊 Response status: {response.status}")
                print(f"📊 Response headers: {dict(response.headers)}")
                
                if response.status == 200:
                    # Get raw response first
                    raw_text = await response.text()
                    print(f"📄 Raw response length: {len(raw_text)}")
                    print(f"📄 Raw response start: {raw_text[:200]}...")
                    
                    try:
                        # Try to parse JSON
                        result = json.loads(raw_text)
                        print(f"✅ JSON parsed successfully")
                        print(f"📊 Result type: {type(result)}")
                        print(f"📊 Result keys: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
                        
                        if isinstance(result, dict):
                            if "choices" in result:
                                print(f"✅ Choices found: {len(result['choices'])}")
                                if result["choices"]:
                                    content = result["choices"][0]["message"]["content"]
                                    print(f"✅ Content extracted: {content[:100]}...")
                                
                            if "citations" in result:
                                print(f"✅ Citations found: {len(result['citations'])}")
                            else:
                                print("⚠️ No citations in response")
                        
                        return True
                        
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON decode error: {e}")
                        print(f"📄 Raw response: {raw_text}")
                        return False
                
                else:
                    error_text = await response.text()
                    print(f"❌ API error {response.status}: {error_text}")
                    return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_enhanced_researcher():
    """Debuggt die Enhanced Research Engine"""
    
    print("\n🔧 Debug: Enhanced Research Engine")
    print("=" * 40)
    
    try:
        from enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
        
        print("🏗️ Creating Enhanced Mining Researcher...")
        researcher = EnhancedMiningResearcher(config)
        
        print("✅ Enhanced Mining Researcher created")
        
        # Test async context
        print("🔄 Testing async context...")
        async with researcher:
            print("✅ Async context working")
            
            # Test simple search (without full research)
            print("🔍 Testing simple search...")
            
            # Manually create a simple search query
            query = "Éléonore mine Quebec Canada"
            phase_id = "test"
            
            print(f"🔍 Query: {query}")
            print(f"🔍 Phase: {phase_id}")
            
            try:
                result = await researcher._perplexity_search(query, phase_id)
                
                if result:
                    print("✅ Search successful!")
                    print(f"📊 Content length: {len(result.get('content', ''))}")
                    print(f"📊 Sources found: {len(result.get('sources', []))}")
                    print(f"📄 Content preview: {result.get('content', '')[:200]}...")
                else:
                    print("❌ Search returned None")
                
            except Exception as e:
                print(f"❌ Search failed: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced researcher debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Hauptfunktion"""
    
    print("🔧 Enhanced Research Engine Debug Session")
    print("=" * 50)
    
    # Test 1: Direct API call
    api_success = await debug_perplexity_api()
    
    # Test 2: Enhanced researcher
    if api_success:
        researcher_success = await debug_enhanced_researcher()
        
        if researcher_success:
            print("\n🎉 All debug tests passed!")
            print("✅ Enhanced Research Engine should work")
            return 0
        else:
            print("\n❌ Enhanced researcher has issues")
            return 1
    else:
        print("\n❌ API call failed")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
