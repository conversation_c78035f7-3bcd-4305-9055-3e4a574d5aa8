# Technische Architektur - MineExtractorWeb v1.0
**Multi-System Web Research Engine - Detaillierte Systemarchitektur**

---

## 🏗️ **SYSTEM-ARCHITEKTUR OVERVIEW**

```
┌─────────────────────────────────────────────────────────────────┐
│                        USER INTERFACE LAYER                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │   Existing GUI  │ │  Web Research   │ │    Batch       │   │
│  │   Integration   │ │      Tab        │ │  Processing    │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    ORCHESTRATION LAYER                          │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │             Multi-Source Research Orchestrator              │ │
│  │  • Task Distribution  • Progress Tracking  • Error Handling │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      RESEARCH ENGINE LAYER                      │
│                                                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │   TIER 1 APIs  │ │   TIER 2 APIs  │ │ TIER 3 SCRAPERS │   │
│  │                 │ │                 │ │                 │   │
│  │  • Perplexity   │ │    • Exa.ai     │ │   • Apify       │   │
│  │  • Tavily      │ │    • Serper     │ │   • ScrapingBee │   │
│  │  • SearchAPI   │ │    • Brave      │ │   • FireCrawl   │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    FALLBACK SYSTEMS                         │ │
│  │  • Custom Selenium  • BeautifulSoup  • Direct HTTP        │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                   DATA PROCESSING LAYER                         │
│                                                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │   Data Parsing  │ │   Aggregation   │ │    Quality      │   │
│  │   & Extraction  │ │   & Merging     │ │   Assessment    │   │
│  │                 │ │                 │ │                 │   │
│  │  • RegEx Engine │ │ • Conflict Res. │ │ • Confidence    │   │
│  │  • AI Parsing   │ │ • Source Weight │ │ • Validation    │   │
│  │  • Normalization│ │ • Deduplication │ │ • Cross-Check   │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      OUTPUT LAYER                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │         Identical CSV Format to Existing System             │ │
│  │    • Same Field Structure  • Same Encoding  • Same Delim   │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔧 **CORE COMPONENTS**

### **1. Multi-Source Research Orchestrator**

```python
class MultiSourceOrchestrator:
    """
    Zentrale Steuerung für Multi-System Research
    Koordiniert alle APIs und Scraper für optimale Datenabdeckung
    """
    
    def __init__(self, config):
        # Tier 1: Primary Research APIs (Höchste Priorität)
        self.tier1_apis = [
            PerplexityResearcher(config['perplexity_key']),
            TavilyResearcher(config['tavily_key']),
            SearchAPIResearcher(config['searchapi_key'])
        ]
        
        # Tier 2: Specialized Search APIs  
        self.tier2_apis = [
            ExaResearcher(config['exa_key']),
            SerperResearcher(config['serper_key']),
            BraveResearcher(config['brave_key'])
        ]
        
        # Tier 3: Managed Scraping Services
        self.tier3_scrapers = [
            ApifyMiningScrapers(config['apify_key']),
            ScrapingBeeService(config['scrapingbee_key']),
            FireCrawlService(config['firecrawl_key'])
        ]
        
        # Fallback Systems
        self.fallback_scrapers = [
            CustomSeleniumScraper(),
            BeautifulSoupScraper(),
            DirectHTTPScraper()
        ]
        
        # Quality & Aggregation
        self.aggregator = DataAggregator()
        self.quality_assessor = QualityAssessment()
        
    async def research_comprehensive(self, mine_name: str) -> MineData:
        """
        Orchestriert vollständige Multi-Source-Recherche
        """
        # 1. Parallel Research Phase
        tier1_results = await self._execute_tier1_research(mine_name)
        tier2_results = await self._execute_tier2_research(mine_name)
        tier3_results = await self._execute_tier3_scraping(mine_name)
        
        # 2. Fallback wenn primäre Quellen fehlschlagen
        if not self._sufficient_data(tier1_results + tier2_results):
            fallback_results = await self._execute_fallback_scraping(mine_name)
            tier3_results.extend(fallback_results)
        
        # 3. Data Aggregation & Quality Assessment
        all_results = tier1_results + tier2_results + tier3_results
        aggregated_data = self.aggregator.merge_multi_source(all_results)
        quality_score = self.quality_assessor.assess_comprehensive(aggregated_data)
        
        # 4. Generate Final MineData
        return self._create_final_mine_data(mine_name, aggregated_data, quality_score)
```

### **2. Tier-Based Research Strategy**

#### **Tier 1: Primary Research APIs (95% Coverage Target)**
```python
class Tier1ResearchAPIs:
    """
    Hochwertige Research-APIs mit bester Datenqualität
    Fokus: Umfassende, aktuelle und zuverlässige Informationen
    """
    
    # Perplexity AI Pro - Deep Research Engine
    PERPLEXITY_CONFIG = {
        'model': 'llama-3.1-sonar-large-128k-online',
        'search_depth': 'deep',
        'domain_filter': ['gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca'],
        'recency_filter': 'month',
        'max_tokens': 2000,
        'temperature': 0.1
    }
    
    # Tavily AI - Government & Regulatory Data
    TAVILY_CONFIG = {
        'search_depth': 'advanced',
        'include_domains': ['gov.ca', 'sec.gov', 'tsx.com'],
        'focus_areas': ['financial', 'regulatory', 'environmental'],
        'max_results': 15
    }
    
    # SearchAPI - Comprehensive Web Search
    SEARCHAPI_CONFIG = {
        'engine': 'google',
        'location': 'Canada',
        'language': 'en',
        'num_results': 20,
        'safe_search': 'off'
    }
```

#### **Tier 2: Specialized Search APIs (85% Coverage Target)**
```python
class Tier2SpecializedAPIs:
    """
    Spezialisierte APIs für schwer findbare Daten
    Fokus: Technische Daten, Finanzinformationen, Koordinaten
    """
    
    # Exa.ai - Semantic Mining Data Search
    EXA_CONFIG = {
        'type': 'neural',
        'use_autoprompt': True,
        'num_results': 10,
        'include_domains': [
            'mining.com', 'miningweekly.com', 'northernminer.com',
            'kitco.com', 's2analytics.com'
        ]
    }
    
    # Serper API - Real-time Search Results
    SERPER_CONFIG = {
        'type': 'search',
        'autocorrect': True,
        'num': 10,
        'location': 'Canada'
    }
    
    # Brave Search API - Privacy-Focused Results
    BRAVE_CONFIG = {
        'count': 20,
        'country': 'ca',
        'search_lang': 'en',
        'result_filter': 'web'
    }
```

#### **Tier 3: Managed Scraping Services (75% Coverage Target)**
```python
class Tier3ScrapingServices:
    """
    Spezialisierte Scraping-Services für strukturierte Datenquellen
    Fokus: Government Databases, Company Filings, Industry Data
    """
    
    # Apify - Government Mining Database Scrapers
    APIFY_SCRAPERS = {
        'quebec_mern': 'quebec-mining-database-scraper',
        'sedar_plus': 'canadian-company-filings-scraper', 
        'nrcan_data': 'natural-resources-canada-scraper',
        'ontario_mndm': 'ontario-mining-database-scraper'
    }
    
    # ScrapingBee - JavaScript-Heavy Mining Sites
    SCRAPINGBEE_CONFIG = {
        'render_js': True,
        'premium_proxy': True,
        'country_code': 'ca',
        'wait': 3000,
        'timeout': 30000
    }
    
    # FireCrawl - Modern SPA Mining Applications
    FIRECRAWL_CONFIG = {
        'wait_for': 'networkidle',
        'actions': ['click', 'scroll', 'wait'],
        'extract_schema': 'mining_data'
    }
```

---

## 📊 **DATA FLOW & PROCESSING**

### **Data Flow Diagram:**
```
Mine Name Input
      │
      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TIER 1 APIs  │    │   TIER 2 APIs  │    │ TIER 3 SCRAPERS │
│   (Parallel)    │    │   (Parallel)    │    │   (Sequential)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
      │                        │                        │
      ▼                        ▼                        ▼
┌─────────────────────────────────────────────────────────────────┐
│                     RESULT AGGREGATION                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │  Source Weight  │ │ Conflict Resol. │ │   Confidence    │   │
│  │   Calculation   │ │   Algorithm     │ │    Scoring      │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
      │
      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    QUALITY ASSESSMENT                           │
│  • Plausibility Checks  • Cross-Validation  • Completeness     │
└─────────────────────────────────────────────────────────────────┘
      │
      ▼
┌─────────────────────────────────────────────────────────────────┐
│                     FINAL OUTPUT                                │
│        Same CSV Format as Existing PDF-Based System            │
└─────────────────────────────────────────────────────────────────┘
```

### **Source Quality Weighting Algorithm:**
```python
class SourceQualityWeighting:
    """
    Gewichtungsalgorithmus für Multi-Source-Daten
    Berücksichtigt Quellentyp, Aktualität und Datentyp
    """
    
    SOURCE_WEIGHTS = {
        # Government Sources (Höchste Zuverlässigkeit)
        'government': {
            'base_weight': 1.0,
            'data_types': {
                'permits': 0.95,
                'coordinates': 0.90,
                'status': 0.95,
                'environmental': 0.85
            }
        },
        
        # Company Reports (Hohe Zuverlässigkeit)
        'company_reports': {
            'base_weight': 0.85,
            'data_types': {
                'financial': 0.90,
                'production': 0.85,
                'operator': 0.95,
                'technical': 0.80
            }
        },
        
        # Industry Databases (Mittlere Zuverlässigkeit)
        'industry_data': {
            'base_weight': 0.70,
            'data_types': {
                'market_data': 0.75,
                'technical': 0.70,
                'news': 0.60,
                'analysis': 0.65
            }
        },
        
        # News & Media (Geringere Zuverlässigkeit)
        'news_media': {
            'base_weight': 0.50,
            'data_types': {
                'status_updates': 0.60,
                'financial': 0.45,
                'announcements': 0.55
            }
        }
    }
    
    def calculate_weight(self, source_type, data_type, age_days):
        """
        Berechnet Gewichtung basierend auf Quelle, Datentyp und Alter
        """
        base_weight = self.SOURCE_WEIGHTS[source_type]['base_weight']
        data_weight = self.SOURCE_WEIGHTS[source_type]['data_types'].get(data_type, 0.5)
        
        # Age penalty (neuere Daten höher gewichtet)
        age_factor = max(0.3, 1.0 - (age_days / 365) * 0.5)
        
        return base_weight * data_weight * age_factor
```

---

## 🛡️ **ERROR HANDLING & RESILIENCE**

### **Multi-Layer Error Handling:**
```python
class ResilienceFramework:
    """
    Umfassendes Error-Handling für Multi-System-Architektur
    Gewährleistet Ausfallsicherheit und Datenqualität
    """
    
    async def execute_with_resilience(self, mine_name, research_functions):
        """
        Führt Research mit automatischem Fallback durch
        """
        results = []
        failed_services = []
        
        # Phase 1: Primäre Services (Tier 1)
        for service_func in research_functions['tier1']:
            try:
                result = await self._execute_with_retry(service_func, mine_name)
                if result and self._validate_result(result):
                    results.append(result)
            except Exception as e:
                failed_services.append(('tier1', service_func.__name__, str(e)))
                continue
        
        # Phase 2: Backup Services wenn Tier 1 unzureichend
        if len(results) < 2:  # Mindestens 2 Quellen erforderlich
            for service_func in research_functions['tier2']:
                try:
                    result = await self._execute_with_retry(service_func, mine_name)
                    if result:
                        results.append(result)
                except Exception as e:
                    failed_services.append(('tier2', service_func.__name__, str(e)))
        
        # Phase 3: Fallback Services als letzte Option
        if len(results) == 0:
            for service_func in research_functions['fallback']:
                try:
                    result = await service_func(mine_name)
                    if result:
                        results.append(result)
                        break  # Ein Fallback-Ergebnis reicht
                except Exception as e:
                    failed_services.append(('fallback', service_func.__name__, str(e)))
        
        return results, failed_services
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=10), 
           stop=stop_after_attempt(3))
    async def _execute_with_retry(self, service_func, mine_name):
        """
        Führt Service-Aufrufe mit Retry-Logic durch
        """
        return await service_func(mine_name)
```

### **Monitoring & Alerting:**
```python
class SystemMonitoring:
    """
    Überwachung der Multi-System-Performance
    """
    
    def __init__(self):
        self.metrics = {
            'api_response_times': defaultdict(list),
            'success_rates': defaultdict(float),
            'error_counts': defaultdict(int),
            'data_quality_scores': []
        }
    
    def log_api_performance(self, api_name, response_time, success, error=None):
        """
        Protokolliert API-Performance für Monitoring
        """
        self.metrics['api_response_times'][api_name].append(response_time)
        
        if success:
            self.metrics['success_rates'][api_name] = self._update_success_rate(api_name, True)
        else:
            self.metrics['error_counts'][api_name] += 1
            self.metrics['success_rates'][api_name] = self._update_success_rate(api_name, False)
            
        # Alert bei kritischen Problemen
        if self.metrics['success_rates'][api_name] < 0.5:
            self._send_alert(f"Critical: {api_name} success rate below 50%")
```

---

## ⚡ **PERFORMANCE OPTIMIZATION**

### **Parallel Processing Strategy:**
```python
class ParallelProcessingEngine:
    """
    Optimierte parallele Verarbeitung für maximalen Durchsatz
    """
    
    def __init__(self, max_concurrent=10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_mine_list(self, mine_names):
        """
        Verarbeitet Liste von Minen mit optimaler Parallelisierung
        """
        # Batch-Verarbeitung um API-Limits zu respektieren
        batches = self._create_batches(mine_names, batch_size=5)
        
        all_results = []
        for batch in batches:
            batch_results = await asyncio.gather(
                *[self.research_single_mine(name) for name in batch],
                return_exceptions=True
            )
            all_results.extend(batch_results)
            
            # Rate limiting zwischen Batches
            await asyncio.sleep(2)
        
        return all_results
    
    async def research_single_mine(self, mine_name):
        """
        Einzelne Mine mit Semaphore-Control
        """
        async with self.semaphore:
            return await self._execute_research(mine_name)
```

### **Caching Strategy:**
```python
class IntelligentCaching:
    """
    Intelligentes Caching für häufig abgefragte Daten
    """
    
    def __init__(self, cache_duration_hours=24):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_duration = cache_duration_hours * 3600
    
    async def get_or_research(self, mine_name, research_func):
        """
        Gibt gecachte Daten zurück oder führt neue Recherche durch
        """
        cache_key = self._generate_cache_key(mine_name)
        
        # Check cache validity
        if (cache_key in self.cache and 
            time.time() - self.cache_timestamps[cache_key] < self.cache_duration):
            return self.cache[cache_key]
        
        # Execute research and cache result
        result = await research_func(mine_name)
        self.cache[cache_key] = result
        self.cache_timestamps[cache_key] = time.time()
        
        return result
```

---

## 🔌 **API INTEGRATION DETAILS**

### **Perplexity AI Integration:**
```python
class PerplexityAdvancedResearcher:
    """
    Erweiterte Perplexity-Integration für Mining-spezifische Recherche
    """
    
    MINING_PROMPTS = {
        'comprehensive': """
        Research {mine_name} mine in Quebec, Canada. Find:
        
        CRITICAL DATA:
        - Current operator company (exact legal name)
        - Operating status (active/closed/planned with current year)
        - Environmental restoration costs in CAD dollars (from official reports)
        - Mine type (open-pit/underground/surface)
        - Primary commodity (gold/copper/nickel/iron/etc.)
        - Production start/end dates
        - Annual production volume
        - Mine area in hectares or km²
        - GPS coordinates if available
        
        PRIORITY SOURCES:
        - Quebec MERN database (mrnf.gouv.qc.ca)
        - SEDAR+ company filings (sedarplus.ca)
        - Company technical reports
        - Environmental impact assessments
        - TSX/SEC regulatory filings
        
        Provide specific values with source URLs and confidence levels.
        """,
        
        'financial_focus': """
        Research financial and regulatory data for {mine_name} mine:
        - Environmental restoration/closure costs (CAD)
        - Mining permits and regulatory status
        - Company financial obligations
        - Government bonding requirements
        Source: Official government and company filings only.
        """,
        
        'technical_focus': """
        Research technical specifications for {mine_name} mine:
        - Mine type and operation method
        - Production capacity and volumes
        - Mine site area and coordinates
        - Mining infrastructure details
        Source: Technical reports and engineering documents.
        """
    }
    
    async def research_with_multiple_prompts(self, mine_name):
        """
        Führt mehrere spezialisierte Prompts parallel durch
        """
        tasks = []
        for prompt_type, template in self.MINING_PROMPTS.items():
            prompt = template.format(mine_name=mine_name)
            task = self._single_perplexity_request(prompt, prompt_type)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return self._merge_perplexity_results(results)
```

---

## 📋 **INTEGRATION CHECKLIST**

### **Phase 1 - Foundation Setup:**
- [ ] ✅ Projekt-Verzeichnisstruktur erstellt
- [ ] ⏳ Perplexity API Account & Key Setup
- [ ] ⏳ Tavily API Account & Key Setup  
- [ ] ⏳ Basic Multi-Source Research Class
- [ ] ⏳ Integration in bestehende GUI
- [ ] ⏳ Test mit 5 Quebec-Minen

### **Phase 2 - Multi-API Integration:**
- [ ] ⏳ Exa.ai API Integration
- [ ] ⏳ SearchAPI Integration
- [ ] ⏳ Parallel Processing Framework
- [ ] ⏳ Data Aggregation Engine
- [ ] ⏳ Quality Assessment Framework

### **Phase 3 - Scraping Services:**
- [ ] ⏳ Apify Government Scrapers
- [ ] ⏳ ScrapingBee Integration
- [ ] ⏳ FireCrawl Modern Sites
- [ ] ⏳ Custom Fallback Scrapers

### **Phase 4 - Production Ready:**
- [ ] ⏳ Error Handling & Resilience
- [ ] ⏳ Monitoring & Alerting
- [ ] ⏳ Performance Optimization
- [ ] ⏳ Caching Implementation
- [ ] ⏳ Documentation & Training

---

**Status:** ✅ Architecture Design Complete  
**Nächster Schritt:** Implementation Phase 1 - Foundation Setup  
**Geschätzte Entwicklungszeit:** 8 Wochen für vollständiges System  

*Diese Architektur gewährleistet maximale Datenabdeckung durch Multi-System-Redundanz und höchste Ausfallsicherheit.*