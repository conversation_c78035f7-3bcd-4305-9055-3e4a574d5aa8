@echo off
echo.
echo ========================================
echo   🔧 MINE EXTRACTOR WEB v1.0 - SETUP
echo   Automatische Installation & Konfiguration
echo ========================================
echo.

REM Verwende miniforge Python
set PYTHON_CMD=C:\ProgramData\miniforge3\python.exe

REM Prüfe Python-Verfügbarkeit
%PYTHON_CMD% --version >nul 2>&1
if errorlevel 1 (
    echo ❌ miniforge Python nicht gefunden, versuche Standard Python...
    set PYTHON_CMD=python
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Kein Python gefunden!
        echo.
        echo 📥 Bitte installiere Python von:
        echo    https://github.com/conda-forge/miniforge
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Python gefunden: %PYTHON_CMD%
%PYTHON_CMD% --version
echo.

echo 🔧 Starte automatisches Setup...
echo.

%PYTHON_CMD% setup.py

if errorlevel 1 (
    echo.
    echo ❌ Setup fehlgeschlagen!
    echo.
    echo 💡 Manuelle Schritte:
    echo    1. pip install -r requirements.txt
    echo    2. python tools\api_key_manager.py
    echo    3. python main.py --test-apis
    echo.
) else (
    echo.
    echo ✅ Setup erfolgreich abgeschlossen!
    echo.
    echo 🎯 Nächste Schritte:
    echo    1. 🔑 API Keys konfigurieren (falls noch nicht geschehen)
    echo    2. 🚀 System starten mit: START_MineExtractorWeb.bat
    echo.
    
    set /p start_choice="System jetzt starten? (j/n): "
    if /i "%start_choice%"=="j" (
        echo.
        echo 🚀 Starte MineExtractorWeb...
        call START_MineExtractorWeb.bat
    )
)

echo.
pause
