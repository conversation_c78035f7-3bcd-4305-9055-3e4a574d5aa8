#!/usr/bin/env python3
"""
MineExtractorWeb v1.0 - Main Entry Point
Multi-System Web Research Engine für Quebec Mining Data

Usage:
    python main.py                    # Standalone GUI
    python main.py --help            # Show help
    python main.py --test-apis       # Test API connections
    python main.py --config          # Show configuration status
    python main.py --research <mine> # Quick CLI research
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# Version info
__version__ = "1.0.0"
__author__ = "Claude Sonnet 4"

def setup_logging():
    """Konfiguriert Logging für die Anwendung"""
    import logging
    
    # Basic logging setup
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("logs/mineextractor_web.log", encoding='utf-8')
        ]
    )
    
    # Ensure logs directory exists
    os.makedirs("logs", exist_ok=True)

def print_banner():
    """Zeigt Anwendungs-Banner"""
    print("=" * 70)
    print(f"🌐 MineExtractorWeb v{__version__}")
    print("Multi-System Web Research Engine für Quebec Mining Data")
    print("=" * 70)

def check_dependencies():
    """Prüft kritische Dependencies"""
    missing_deps = []
    
    try:
        import aiohttp
    except ImportError:
        missing_deps.append("aiohttp")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    if missing_deps:
        print("❌ Missing critical dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Install dependencies with: pip install -r requirements.txt")
        return False
    
    return True

def show_configuration_status():
    """Zeigt Konfigurationsstatus"""
    print("\n📋 Configuration Status:")
    print("-" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        # Show API status
        print(APIConfig.get_api_status_summary())
        
        # Show active APIs
        active_apis = APIConfig.get_active_apis()
        if active_apis:
            print(f"\n✅ Active APIs: {', '.join(active_apis)}")
        else:
            print("\n❌ No APIs configured")
        
        # Show project info
        try:
            from config.settings import ProjectSettings
            info = ProjectSettings.get_project_info()
            print(f"\n📁 Project Root: {info['root']}")
            print(f"📊 Output Directory: {info['output_dir']}")
            print(f"📝 Log Directory: {info['log_dir']}")
        except ImportError:
            print("\n⚠️  Project settings not available")
            
    except ImportError:
        print("❌ Configuration modules not available")
        print("   Ensure project is properly set up")

async def test_api_connections():
    """Testet alle API-Verbindungen"""
    print("\n🧪 Testing API Connections:")
    print("-" * 40)
    
    try:
        from src.web_researcher import create_researcher_from_config
        
        researcher = create_researcher_from_config()
        test_results = await researcher.test_api_connections()
        
        for api_name, success in test_results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {api_name.title()} API")
        
        successful_apis = sum(test_results.values())
        total_apis = len(test_results)
        
        print(f"\n📊 API Test Summary: {successful_apis}/{total_apis} APIs working")
        
        if successful_apis == 0:
            print("\n⚠️  No APIs are working. Please check your configuration.")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API testing failed: {e}")
        print("\n💡 Ensure API keys are configured in environment variables")
        return False

async def quick_research(mine_name: str):
    """Führt schnelle CLI-Research durch"""
    print(f"\n🔍 Quick Research: {mine_name}")
    print("-" * 40)
    
    try:
        from src.web_researcher import create_researcher_from_config
        
        researcher = create_researcher_from_config()
        
        print(f"🚀 Starting research for {mine_name}...")
        result = await researcher.research_mine_comprehensive(mine_name)
        
        # Display results
        print(f"\n📊 Research Results for {mine_name}:")
        print(f"   Completion: {result.data_completeness:.1%}")
        print(f"   Confidence: {result.confidence_score:.1%}")
        print(f"   Duration: {result.research_duration:.1f}s")
        print(f"   Status: {result.validation_status}")
        
        if result.mine_data.betreiber:
            print(f"   Operator: {result.mine_data.betreiber}")
        
        if result.mine_data.restaurationskosten_cad:
            print(f"   Restoration Costs: ${result.mine_data.restaurationskosten_cad} CAD")
        
        if result.mine_data.rohstoffabbau:
            print(f"   Commodity: {result.mine_data.rohstoffabbau}")
        
        if result.sources:
            print(f"   Sources: {len(result.sources)} found")
        
        # Save quick result
        quick_output = f"quick_research_{mine_name.replace(' ', '_')}.csv"
        import pandas as pd
        df = pd.DataFrame([result.mine_data.to_dict()])
        df.to_csv(quick_output, index=False, encoding='utf-8-sig', sep='|')
        print(f"   💾 Saved to: {quick_output}")
        
    except Exception as e:
        print(f"❌ Quick research failed: {e}")

def start_standalone_gui():
    """Startet standalone GUI"""
    print("\n🖥️ Starting Standalone GUI...")
    print("-" * 40)
    
    try:
        from src.gui_integration import create_standalone_gui
        
        gui = create_standalone_gui()
        print("✅ GUI created successfully!")
        print("💡 Close the GUI window to exit")
        
        gui.run_standalone()
        
    except ImportError as e:
        print(f"❌ GUI import failed: {e}")
        print("   Ensure tkinter is available on your system")
    except Exception as e:
        print(f"❌ GUI startup failed: {e}")

def show_help():
    """Zeigt erweiterte Hilfe"""
    print(f"""
🌐 MineExtractorWeb v{__version__} - Usage Guide
{'=' * 60}

BASIC USAGE:
  python main.py                    Start standalone GUI
  python main.py --gui              Start standalone GUI (same as above)

CONFIGURATION:
  python main.py --config           Show configuration status
  python main.py --test-apis        Test all API connections
  python main.py --setup            Interactive setup wizard

QUICK RESEARCH:
  python main.py --research <mine>  Quick CLI research for single mine
  
  Examples:
    python main.py --research "Éléonore"
    python main.py --research "Canadian Malartic"

INTEGRATION:
  To integrate with existing MineExtractor GUI:
  
  from src.gui_integration import integrate_with_existing_gui
  web_extension = integrate_with_existing_gui(existing_gui, existing_notebook)

API SETUP:
  1. Create .env file in project root
  2. Add your API keys:
     PERPLEXITY_API_KEY=your_key_here
     TAVILY_API_KEY=your_key_here (optional)
     EXA_API_KEY=your_key_here (optional)
  
  3. Test configuration:
     python main.py --test-apis

REQUIREMENTS:
  Install dependencies: pip install -r requirements.txt
  
  Minimum requirements:
    - Python 3.8+
    - aiohttp (API requests)
    - pandas (data processing)
    - tkinter (GUI - usually included with Python)

FILES & DIRECTORIES:
  📁 config/     - Configuration files
  📁 src/        - Source code
  📁 logs/       - Log files
  📁 output/     - Research results
  📁 Doku/       - Documentation
  📄 .env        - Environment variables (create this)

TROUBLESHOOTING:
  - Missing dependencies: pip install -r requirements.txt
  - API errors: Check API keys in .env file
  - GUI issues: Ensure tkinter is installed
  - Permission errors: Run as administrator (Windows) or use sudo (Linux/Mac)

SUPPORT:
  Check documentation in Doku/ directory for detailed information.
""")

async def interactive_setup():
    """Interaktiver Setup-Wizard"""
    print("\n🔧 Interactive Setup Wizard")
    print("=" * 40)
    
    # Check environment file
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
        create_env = input("Create .env file now? (y/n): ").lower().strip()
        
        if create_env == 'y':
            try:
                from config.api_keys import ENV_TEMPLATE
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(ENV_TEMPLATE)
                print("✅ .env template created")
                print("💡 Please edit .env file and add your API keys")
            except Exception as e:
                print(f"❌ Failed to create .env: {e}")
    
    # Test API keys
    print("\n🧪 Testing API connections...")
    api_test_success = await test_api_connections()
    
    if not api_test_success:
        print("\n⚠️  API configuration incomplete")
        print("💡 Please configure API keys in .env file")
        
        # Show available APIs
        print("\nAvailable APIs for Phase 1:")
        print("  🔹 Perplexity AI (Required) - https://www.perplexity.ai/")
        print("  🔹 Tavily AI (Optional) - https://tavily.com/")
        print("  🔹 Exa.ai (Optional) - https://exa.ai/")
    
    # Test dependencies
    print("\n📦 Checking dependencies...")
    if check_dependencies():
        print("✅ All critical dependencies available")
    else:
        print("❌ Missing dependencies - run: pip install -r requirements.txt")
    
    # Show next steps
    print("\n🎯 Next Steps:")
    if api_test_success:
        print("  1. ✅ APIs configured - ready for research!")
        print("  2. Run: python main.py --gui")
    else:
        print("  1. Configure API keys in .env file")
        print("  2. Test APIs: python main.py --test-apis")
        print("  3. Start GUI: python main.py --gui")

def main():
    """Hauptfunktion"""
    
    # Setup
    setup_logging()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="MineExtractorWeb v1.0 - Web Research Engine",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                        # Start GUI
  python main.py --test-apis            # Test API connections
  python main.py --research "Éléonore"  # Quick research
        """
    )
    
    parser.add_argument("--version", action="version", version=f"MineExtractorWeb v{__version__}")
    parser.add_argument("--gui", action="store_true", help="Start standalone GUI (default)")
    parser.add_argument("--config", action="store_true", help="Show configuration status")
    parser.add_argument("--test-apis", action="store_true", help="Test API connections")
    parser.add_argument("--research", type=str, help="Quick research for specified mine")
    parser.add_argument("--setup", action="store_true", help="Interactive setup wizard")
    parser.add_argument("--help-extended", action="store_true", help="Show extended help")
    
    args = parser.parse_args()
    
    # Show banner
    print_banner()
    
    # Check dependencies first
    if not check_dependencies():
        return 1
    
    # Handle arguments
    try:
        if args.help_extended:
            show_help()
            return 0
        
        elif args.config:
            show_configuration_status()
            return 0
        
        elif args.test_apis:
            success = asyncio.run(test_api_connections())
            return 0 if success else 1
        
        elif args.research:
            asyncio.run(quick_research(args.research))
            return 0
        
        elif args.setup:
            asyncio.run(interactive_setup())
            return 0
        
        else:
            # Default: Start GUI
            start_standalone_gui()
            return 0
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        return 1
    
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
