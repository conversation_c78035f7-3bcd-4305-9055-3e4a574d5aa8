#!/usr/bin/env python3
"""
Enhanced Research Engine Test Suite
Umfassende Tests für MineExtractorWeb v2.0 Enhanced Research Engine
"""

import sys
import os
import asyncio
import json
from pathlib import Path
import time

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

class EnhancedResearchTester:
    """Comprehensive Test Suite für Enhanced Research Engine"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    def print_banner(self):
        """Test Suite Banner"""
        print("=" * 70)
        print("🧪 MineExtractorWeb v2.0 - Enhanced Research Engine Test Suite")
        print("Comprehensive Testing of Multi-Phase Research Capabilities")
        print("=" * 70)
    
    async def run_all_tests(self):
        """Führt alle Tests durch"""
        
        self.print_banner()
        
        test_suite = [
            ("Configuration Test", self.test_configuration),
            ("API Connectivity Test", self.test_api_connectivity),
            ("Enhanced Research Engine Test", self.test_enhanced_research_engine),
            ("Multi-Phase Research Test", self.test_multi_phase_research),
            ("Source Documentation Test", self.test_source_documentation),
            ("Data Quality Test", self.test_data_quality),
            ("Performance Test", self.test_performance),
            ("Integration Test", self.test_gui_integration),
            ("Export Functionality Test", self.test_export_functionality)
        ]
        
        print(f"\n🎯 Running {len(test_suite)} comprehensive tests...\n")
        
        for test_name, test_func in test_suite:
            print(f"{'='*50}")
            print(f"🧪 {test_name}")
            print(f"{'='*50}")
            
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                self.test_results[test_name] = result
                self.total_tests += 1
                
                if result['passed']:
                    self.passed_tests += 1
                    print(f"✅ {test_name}: PASSED")
                    if 'details' in result:
                        print(f"   {result['details']}")
                else:
                    print(f"❌ {test_name}: FAILED")
                    print(f"   Error: {result.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ {test_name}: EXCEPTION")
                print(f"   Exception: {str(e)}")
                self.test_results[test_name] = {'passed': False, 'error': str(e)}
                self.total_tests += 1
            
            print()
        
        self.print_test_summary()
        return self.passed_tests == self.total_tests
    
    def test_configuration(self):
        """Test 1: Configuration und Setup"""
        
        try:
            from config.api_keys import APIConfig
            
            results = []
            
            # Test API Config Loading
            validation = APIConfig.validate_config()
            results.append(f"API Configuration loaded: {len(validation)} APIs detected")
            
            # Test Environment Variables
            env_file = Path(".env")
            if env_file.exists():
                results.append("✅ .env file found")
            else:
                results.append("⚠️ .env file not found")
            
            # Test Critical APIs
            if APIConfig.PERPLEXITY_API_KEY:
                results.append("✅ Perplexity API key configured")
            else:
                results.append("❌ Perplexity API key missing (required)")
                return {'passed': False, 'error': 'Perplexity API key required for Enhanced Research'}
            
            if APIConfig.TAVILY_API_KEY:
                results.append("✅ Tavily API key configured")
            else:
                results.append("⚠️ Tavily API key not configured (optional)")
            
            return {
                'passed': True,
                'details': ' | '.join(results)
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_api_connectivity(self):
        """Test 2: API Connectivity"""
        
        try:
            from config.api_keys import APIConfig
            
            results = []
            all_connected = True
            
            # Test Perplexity API
            if APIConfig.PERPLEXITY_API_KEY:
                try:
                    from src.perplexity_client import test_api_key
                    perplexity_success = await test_api_key(APIConfig.PERPLEXITY_API_KEY)
                    if perplexity_success:
                        results.append("✅ Perplexity API connected")
                    else:
                        results.append("❌ Perplexity API connection failed")
                        all_connected = False
                except Exception as e:
                    results.append(f"❌ Perplexity API test error: {str(e)}")
                    all_connected = False
            
            # Test Tavily API
            if APIConfig.TAVILY_API_KEY:
                try:
                    from src.tavily_client import test_api_key
                    tavily_success = await test_api_key(APIConfig.TAVILY_API_KEY)
                    if tavily_success:
                        results.append("✅ Tavily API connected")
                    else:
                        results.append("❌ Tavily API connection failed")
                        # Don't fail the test for Tavily as it's optional
                except Exception as e:
                    results.append(f"⚠️ Tavily API test error: {str(e)}")
            
            return {
                'passed': all_connected,
                'details': ' | '.join(results)
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_enhanced_research_engine(self):
        """Test 3: Enhanced Research Engine Initialization"""
        
        try:
            from src.enhanced_web_researcher import EnhancedMiningResearcher
            from config.api_keys import APIConfig
            
            config = {
                'perplexity_key': APIConfig.PERPLEXITY_API_KEY
            }
            
            # Test Engine Initialization
            async with EnhancedMiningResearcher(config) as researcher:
                
                # Test Research Phases Configuration
                phases = researcher.research_phases
                phase_count = len(phases)
                
                # Test Specialized Prompts
                prompts = researcher.specialized_prompts
                prompt_count = len(prompts)
                
                results = [
                    f"✅ Enhanced Research Engine initialized",
                    f"✅ {phase_count} research phases configured",
                    f"✅ {prompt_count} specialized prompts loaded",
                    f"✅ Quebec-specific terms: {len(researcher.quebec_specific_terms)}"
                ]
                
                return {
                    'passed': True,
                    'details': ' | '.join(results)
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_multi_phase_research(self):
        """Test 4: Multi-Phase Research mit Test Mine"""
        
        try:
            from src.enhanced_web_researcher import EnhancedMiningResearcher
            from config.api_keys import APIConfig
            
            config = {
                'perplexity_key': APIConfig.PERPLEXITY_API_KEY
            }
            
            # Test mit bekannter Mine
            test_mine = "Éléonore"
            
            async with EnhancedMiningResearcher(config) as researcher:
                
                print(f"   🔍 Testing multi-phase research for: {test_mine}")
                
                start_time = time.time()
                
                # Progress Tracking
                progress_updates = []
                def progress_callback(progress, status):
                    progress_updates.append(f"{progress:.0f}%: {status}")
                    print(f"      Progress: {progress:.0f}% - {status}")
                
                # Run Enhanced Research
                result = await researcher.research_mine_comprehensive(
                    test_mine, 
                    progress_callback=progress_callback
                )
                
                duration = time.time() - start_time
                
                # Evaluate Results
                results = []
                
                if len(result.data_points) > 0:
                    results.append(f"✅ Found {len(result.data_points)} data points")
                else:
                    results.append("⚠️ No data points found")
                
                if len(result.all_sources) > 0:
                    results.append(f"✅ Found {len(result.all_sources)} sources")
                else:
                    results.append("❌ No sources found")
                
                if result.data_completeness_score > 0.3:
                    results.append(f"✅ Good data completeness: {result.data_completeness_score:.1%}")
                else:
                    results.append(f"⚠️ Low data completeness: {result.data_completeness_score:.1%}")
                
                if len(result.research_phases_completed) >= 3:
                    results.append(f"✅ Completed {len(result.research_phases_completed)} phases")
                else:
                    results.append(f"⚠️ Only {len(result.research_phases_completed)} phases completed")
                
                results.append(f"⏱️ Research time: {duration:.1f}s")
                
                # Test passes if we have data points and sources
                passed = len(result.data_points) > 0 and len(result.all_sources) > 0
                
                return {
                    'passed': passed,
                    'details': ' | '.join(results),
                    'test_result': result
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_source_documentation(self):
        """Test 5: Source Documentation und Attribution"""
        
        try:
            # Use result from previous test if available
            if 'Multi-Phase Research Test' in self.test_results:
                prev_result = self.test_results['Multi-Phase Research Test']
                if 'test_result' in prev_result:
                    enhanced_result = prev_result['test_result']
                    
                    results = []
                    
                    # Test Source Metadata
                    sources_with_metadata = 0
                    for source in enhanced_result.all_sources:
                        if hasattr(source, 'reliability_score') and hasattr(source, 'content_type'):
                            sources_with_metadata += 1
                    
                    if sources_with_metadata > 0:
                        results.append(f"✅ {sources_with_metadata} sources with full metadata")
                    else:
                        results.append("❌ No sources with metadata")
                    
                    # Test Source Types
                    source_types = set()
                    for source in enhanced_result.all_sources:
                        if hasattr(source, 'content_type'):
                            source_types.add(source.content_type)
                    
                    if len(source_types) > 1:
                        results.append(f"✅ Diverse source types: {len(source_types)}")
                    else:
                        results.append("⚠️ Limited source diversity")
                    
                    # Test Data Point Source Attribution
                    attributed_data_points = 0
                    for data_point in enhanced_result.data_points.values():
                        if hasattr(data_point, 'sources') and len(data_point.sources) > 0:
                            attributed_data_points += 1
                    
                    if attributed_data_points > 0:
                        results.append(f"✅ {attributed_data_points} data points with source attribution")
                    else:
                        results.append("❌ No data points with source attribution")
                    
                    passed = sources_with_metadata > 0 and attributed_data_points > 0
                    
                    return {
                        'passed': passed,
                        'details': ' | '.join(results)
                    }
            
            return {'passed': False, 'error': 'No research results available for source testing'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def test_data_quality(self):
        """Test 6: Data Quality und Confidence Scoring"""
        
        try:
            # Use result from previous test if available
            if 'Multi-Phase Research Test' in self.test_results:
                prev_result = self.test_results['Multi-Phase Research Test']
                if 'test_result' in prev_result:
                    enhanced_result = prev_result['test_result']
                    
                    results = []
                    
                    # Test Confidence Scores
                    confidence_scores = []
                    for data_point in enhanced_result.data_points.values():
                        if hasattr(data_point, 'confidence'):
                            confidence_scores.append(data_point.confidence)
                    
                    if confidence_scores:
                        avg_confidence = sum(confidence_scores) / len(confidence_scores)
                        results.append(f"✅ Average confidence: {avg_confidence:.1%}")
                        
                        if avg_confidence > 0.7:
                            results.append("✅ High confidence data")
                        elif avg_confidence > 0.5:
                            results.append("⚠️ Medium confidence data")
                        else:
                            results.append("❌ Low confidence data")
                    else:
                        results.append("❌ No confidence scores found")
                    
                    # Test Data Completeness
                    if enhanced_result.data_completeness_score > 0.5:
                        results.append(f"✅ Good completeness: {enhanced_result.data_completeness_score:.1%}")
                    else:
                        results.append(f"⚠️ Low completeness: {enhanced_result.data_completeness_score:.1%}")
                    
                    # Test Cross-Validation
                    cross_validated = 0
                    for data_point in enhanced_result.data_points.values():
                        if hasattr(data_point, 'cross_validated') and data_point.cross_validated:
                            cross_validated += 1
                    
                    if cross_validated > 0:
                        results.append(f"✅ {cross_validated} cross-validated data points")
                    else:
                        results.append("⚠️ No cross-validated data points")
                    
                    passed = len(confidence_scores) > 0 and enhanced_result.data_completeness_score > 0.3
                    
                    return {
                        'passed': passed,
                        'details': ' | '.join(results)
                    }
            
            return {'passed': False, 'error': 'No research results available for data quality testing'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_performance(self):
        """Test 7: Performance und Efficiency"""
        
        try:
            from src.enhanced_web_researcher import EnhancedMiningResearcher
            from config.api_keys import APIConfig
            
            config = {
                'perplexity_key': APIConfig.PERPLEXITY_API_KEY
            }
            
            # Performance Test mit mehreren kleinen Minen
            test_mines = ["Éléonore", "Raglan"]
            
            async with EnhancedMiningResearcher(config) as researcher:
                
                start_time = time.time()
                results_data = []
                
                for mine in test_mines:
                    mine_start = time.time()
                    
                    result = await researcher.research_mine_comprehensive(mine)
                    
                    mine_duration = time.time() - mine_start
                    results_data.append({
                        'mine': mine,
                        'duration': mine_duration,
                        'data_points': len(result.data_points),
                        'sources': len(result.all_sources)
                    })
                
                total_duration = time.time() - start_time
                avg_duration = total_duration / len(test_mines)
                
                results = []
                results.append(f"✅ Processed {len(test_mines)} mines in {total_duration:.1f}s")
                results.append(f"✅ Average time per mine: {avg_duration:.1f}s")
                
                # Performance Thresholds
                if avg_duration < 60:  # Under 1 minute per mine
                    results.append("✅ Good performance (< 60s per mine)")
                    passed = True
                elif avg_duration < 120:  # Under 2 minutes per mine
                    results.append("⚠️ Acceptable performance (< 120s per mine)")
                    passed = True
                else:
                    results.append("❌ Slow performance (> 120s per mine)")
                    passed = False
                
                # Efficiency metrics
                total_data_points = sum(r['data_points'] for r in results_data)
                total_sources = sum(r['sources'] for r in results_data)
                
                results.append(f"✅ Total data points: {total_data_points}")
                results.append(f"✅ Total sources: {total_sources}")
                
                return {
                    'passed': passed,
                    'details': ' | '.join(results)
                }
                
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def test_gui_integration(self):
        """Test 8: GUI Integration"""
        
        try:
            # Test Enhanced GUI Import
            try:
                from src.enhanced_gui_integration import EnhancedWebResearchGUI
                results = ["✅ Enhanced GUI module imported"]
                enhanced_available = True
            except ImportError:
                results = ["❌ Enhanced GUI module not available"]
                enhanced_available = False
            
            # Test Basic GUI Import (fallback)
            try:
                from src.gui_integration import WebResearchGUIExtension
                results.append("✅ Basic GUI module imported")
                basic_available = True
            except ImportError:
                results.append("❌ Basic GUI module not available")
                basic_available = False
            
            # Test GUI Creation Functions
            try:
                from src.gui_integration import create_standalone_gui
                results.append("✅ GUI creation function available")
            except ImportError:
                results.append("❌ GUI creation function not available")
            
            # Test requires at least one GUI to be available
            passed = enhanced_available or basic_available
            
            return {
                'passed': passed,
                'details': ' | '.join(results)
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def test_export_functionality(self):
        """Test 9: Export und Output Functionality"""
        
        try:
            results = []
            
            # Test pandas availability for CSV export
            try:
                import pandas as pd
                results.append("✅ Pandas available for CSV export")
                csv_available = True
            except ImportError:
                results.append("❌ Pandas not available")
                csv_available = False
            
            # Test JSON export capability
            try:
                import json
                test_data = {'test': 'data'}
                json.dumps(test_data)
                results.append("✅ JSON export capability")
                json_available = True
            except:
                results.append("❌ JSON export not available")
                json_available = False
            
            # Test file system access
            try:
                test_file = Path("test_export.tmp")
                test_file.write_text("test")
                test_file.unlink()
                results.append("✅ File system write access")
                file_access = True
            except:
                results.append("❌ File system write access failed")
                file_access = False
            
            passed = csv_available and json_available and file_access
            
            return {
                'passed': passed,
                'details': ' | '.join(results)
            }
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    def print_test_summary(self):
        """Zeigt Test Summary"""
        
        print("=" * 70)
        print("📊 ENHANCED RESEARCH ENGINE TEST SUMMARY")
        print("=" * 70)
        
        print(f"\n🎯 Tests Run: {self.total_tests}")
        print(f"✅ Tests Passed: {self.passed_tests}")
        print(f"❌ Tests Failed: {self.total_tests - self.passed_tests}")
        print(f"📈 Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            print(f"   {status} {test_name}")
            
            if not result['passed'] and 'error' in result:
                print(f"      Error: {result['error']}")
        
        if self.passed_tests == self.total_tests:
            print(f"\n🎉 ALL TESTS PASSED! Enhanced Research Engine is ready for production.")
            print(f"🚀 You can now use the Enhanced Multi-Phase Research capabilities.")
        else:
            print(f"\n⚠️  Some tests failed. Please check the configuration and try again.")
            print(f"💡 Basic functionality may still be available.")
        
        print("\n" + "=" * 70)
    
    def generate_test_report(self):
        """Generiert detaillierten Test Report"""
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = f"enhanced_research_test_report_{timestamp}.json"
        
        report_data = {
            'timestamp': timestamp,
            'version': 'MineExtractorWeb v2.0',
            'test_summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.total_tests - self.passed_tests,
                'success_rate': (self.passed_tests/self.total_tests)*100 if self.total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform
            }
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n📄 Detailed test report saved to: {report_file}")
            
        except Exception as e:
            print(f"\n⚠️ Failed to save test report: {e}")


async def main():
    """Hauptfunktion für Enhanced Research Engine Tests"""
    
    tester = EnhancedResearchTester()
    
    try:
        success = await tester.run_all_tests()
        tester.generate_test_report()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Test suite interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test suite failed with unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    print(f"\n{'🎉 Enhanced Research Engine Ready!' if exit_code == 0 else '❌ Tests Failed - Check Configuration'}")
    sys.exit(exit_code)
