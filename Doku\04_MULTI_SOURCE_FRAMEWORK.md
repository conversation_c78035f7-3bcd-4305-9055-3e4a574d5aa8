# Multi-Source Framework - MineExtractorWeb v1.0
**Advanced Multi-API Research Engine für maximale Datenabdeckung**

---

## 🎯 **FRAMEWORK OVERVIEW**

Das Multi-Source Framework ist das Herzstück von Phase 2+, das mehrere Research-APIs und Scraping-Services intelligent koordiniert, um maximale Datenabdeckung bei optimaler Kosteneffizienz zu erreichen.

### **Kern-Prinzipien:**
- **Redundanz durch Diversität:** Mehrere unabhängige Datenquellen
- **Intelligente Priorisierung:** Qualitäts- und kostenbasierte API-Auswahl
- **Adaptive Strategien:** Dynamische Anpassung basierend auf Verfügbarkeit
- **Quality-First Approach:** Datenqualität vor Geschwindigkeit

---

## 🏗️ **FRAMEWORK ARCHITEKTUR**

### **Layer 1: Research Orchestrator**

```python
class MultiSourceOrchestrator:
    """
    Zentrale Koordination aller Research-Quellen
    Implementiert intelligente Strategien für optimale Datenabdeckung
    """
    
    def __init__(self, config):
        # Tier-basierte API Organisation
        self.tier1_apis = self._initialize_tier1_apis(config)
        self.tier2_apis = self._initialize_tier2_apis(config)
        self.tier3_scrapers = self._initialize_tier3_scrapers(config)
        
        # Quality & Aggregation Components
        self.aggregator = AdvancedDataAggregator()
        self.quality_assessor = QualityAssessmentEngine()
        self.conflict_resolver = ConflictResolutionEngine()
        
        # Performance Monitoring
        self.performance_monitor = APIPerformanceMonitor()
        self.cost_optimizer = CostOptimizationEngine()
    
    async def research_comprehensive(self, mine_name: str, 
                                   strategy: str = "balanced") -> EnhancedMineData:
        """
        Orchestriert vollständige Multi-Source-Recherche
        
        Strategies:
        - "fast": Nur Tier 1 APIs, minimale Latenz
        - "balanced": Tier 1 + 2, optimales Kosten/Nutzen-Verhältnis  
        - "comprehensive": Alle Tiers, maximale Datenabdeckung
        - "budget": Kostenoptimiert, beste Qualität pro Dollar
        """
        
        # 1. Strategy Selection & Planning
        execution_plan = self._create_execution_plan(mine_name, strategy)
        
        # 2. Parallel Research Execution
        research_results = await self._execute_parallel_research(execution_plan)
        
        # 3. Data Aggregation & Quality Assessment
        aggregated_data = await self._aggregate_and_assess(research_results)
        
        # 4. Conflict Resolution & Validation
        final_data = await self._resolve_and_validate(aggregated_data)
        
        return final_data
```

### **Layer 2: Tier-Based API Management**

#### **Tier 1: Primary Research APIs**
```python
class Tier1APIManager:
    """
    Hochwertige Research-APIs mit bester Datenqualität
    Success Rate Target: 95%
    """
    
    def __init__(self, config):
        self.apis = {
            'perplexity': PerplexityAdvancedResearcher(config['perplexity_key']),
            'tavily': TavilySpecializedResearcher(config['tavily_key']),
            'searchapi': SearchAPIResearcher(config['searchapi_key'])
        }
        
        # API-spezifische Konfigurationen
        self.api_configs = {
            'perplexity': {
                'specialty': 'comprehensive_research',
                'strength': 'current_data_deep_analysis',
                'cost_per_request': 0.005,
                'quality_score': 0.95,
                'timeout': 120,
                'rate_limit': 60  # requests per minute
            },
            'tavily': {
                'specialty': 'government_regulatory_data',
                'strength': 'factual_extraction_official_sources',
                'cost_per_request': 0.02,
                'quality_score': 0.85,
                'timeout': 60,
                'rate_limit': 10
            },
            'searchapi': {
                'specialty': 'comprehensive_web_search',
                'strength': 'broad_coverage_recent_data',
                'cost_per_request': 0.001,
                'quality_score': 0.70,
                'timeout': 30,
                'rate_limit': 100
            }
        }
    
    async def execute_tier1_research(self, mine_name: str) -> List[ResearchResult]:
        """Führt parallele Tier 1 Research durch"""
        
        tasks = []
        for api_name, api_client in self.apis.items():
            if self._is_api_available(api_name):
                task = self._execute_with_monitoring(api_name, api_client, mine_name)
                tasks.append(task)
        
        # Execute all APIs in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_results = [r for r in results if isinstance(r, ResearchResult)]
        
        return successful_results
    
    async def _execute_with_monitoring(self, api_name: str, api_client, mine_name: str):
        """Führt API-Aufruf mit Performance-Monitoring durch"""
        
        start_time = time.time()
        
        try:
            # Execute API-specific research method
            if api_name == 'perplexity':
                result = await api_client.research_comprehensive_mining(mine_name)
            elif api_name == 'tavily':
                result = await api_client.search_government_mining_data(mine_name)
            elif api_name == 'searchapi':
                result = await api_client.search_mining_comprehensive(mine_name)
            
            # Monitor success
            response_time = time.time() - start_time
            self._log_api_performance(api_name, response_time, True)
            
            return ResearchResult(
                source=api_name,
                data=result,
                quality_score=self.api_configs[api_name]['quality_score'],
                response_time=response_time,
                success=True
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            self._log_api_performance(api_name, response_time, False, str(e))
            
            return ResearchResult(
                source=api_name,
                data={},
                quality_score=0.0,
                response_time=response_time,
                success=False,
                error=str(e)
            )
```

#### **Tier 2: Specialized Search APIs**
```python
class Tier2APIManager:
    """
    Spezialisierte APIs für schwer findbare Daten
    Success Rate Target: 85%
    """
    
    def __init__(self, config):
        self.apis = {
            'exa': ExaSemanticResearcher(config['exa_key']),
            'serper': SerperRealTimeResearcher(config['serper_key']),
            'brave': BravePrivacyResearcher(config['brave_key'])
        }
    
    async def execute_specialized_research(self, mine_name: str, 
                                         data_gaps: List[str]) -> List[ResearchResult]:
        """
        Führt spezialisierte Recherche für identifizierte Datenlücken durch
        
        Args:
            mine_name: Name der Mine
            data_gaps: Liste der fehlenden Datentypen ['restoration_costs', 'coordinates', etc.]
        """
        
        results = []
        
        for gap in data_gaps:
            # Select best API for specific data type
            best_api = self._select_api_for_data_type(gap)
            
            if best_api:
                try:
                    result = await self._execute_specialized_search(best_api, mine_name, gap)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Tier 2 search failed for {gap}: {e}")
        
        return results
    
    def _select_api_for_data_type(self, data_type: str) -> Optional[str]:
        """Wählt optimale API für spezifischen Datentyp"""
        
        api_specialties = {
            'restoration_costs': 'exa',  # Semantic search für financial data
            'coordinates': 'serper',     # Real-time search für location data
            'technical_specs': 'exa',    # Deep technical search
            'regulatory_status': 'brave', # Privacy-focused gov search
            'production_data': 'serper'   # Current production info
        }
        
        return api_specialties.get(data_type)
```

#### **Tier 3: Managed Scraping Services**
```python
class Tier3ScrapingManager:
    """
    Spezialisierte Scraping-Services für strukturierte Datenquellen
    Success Rate Target: 75%
    """
    
    def __init__(self, config):
        self.scrapers = {
            'apify': ApifyGovernmentScrapers(config['apify_key']),
            'scrapingbee': ScrapingBeeJSRenderer(config['scrapingbee_key']),
            'firecrawl': FireCrawlSPAExtractor(config['firecrawl_key'])
        }
        
        # Government database mappings
        self.government_databases = {
            'quebec_mern': {
                'url': 'https://gestim.mines.gouv.qc.ca/',
                'scraper': 'apify',
                'data_types': ['permits', 'status', 'coordinates', 'operator']
            },
            'sedar_plus': {
                'url': 'https://sedarplus.ca/',
                'scraper': 'scrapingbee',
                'data_types': ['financial', 'technical_reports', 'operator']
            },
            'nrcan_mining': {
                'url': 'https://www.nrcan.gc.ca/mining/',
                'scraper': 'firecrawl',
                'data_types': ['production', 'commodity', 'statistics']
            }
        }
    
    async def execute_government_scraping(self, mine_name: str) -> List[ResearchResult]:
        """Führt strukturiertes Scraping von Regierungsdatenbanken durch"""
        
        results = []
        
        for db_name, db_config in self.government_databases.items():
            try:
                scraper = self.scrapers[db_config['scraper']]
                
                # Execute database-specific scraping
                scraping_result = await scraper.scrape_mining_database(
                    mine_name, db_config
                )
                
                if scraping_result:
                    results.append(ResearchResult(
                        source=f"gov_scraping_{db_name}",
                        data=scraping_result,
                        quality_score=0.75,
                        source_type=SourceType.GOVERNMENT
                    ))
                    
            except Exception as e:
                logger.error(f"Government scraping failed for {db_name}: {e}")
        
        return results
```

---

## 🧠 **INTELLIGENT DATA AGGREGATION**

### **Advanced Data Aggregation Engine**

```python
class AdvancedDataAggregator:
    """
    Intelligente Aggregation von Multi-Source-Daten
    Berücksichtigt Quellenqualität, Aktualität und Datentyp
    """
    
    def __init__(self):
        # Source reliability weights
        self.source_weights = {
            'government': 1.0,      # Highest reliability
            'company_reports': 0.85, # High reliability  
            'industry_data': 0.70,   # Medium reliability
            'news_media': 0.50,      # Lower reliability
            'api_research': 0.80     # Good reliability
        }
        
        # Data type specific weights
        self.data_type_weights = {
            'restoration_costs': {
                'government': 0.95,
                'company_reports': 0.90,
                'api_research': 0.70
            },
            'operator': {
                'company_reports': 0.95,
                'government': 0.90,
                'api_research': 0.80
            },
            'coordinates': {
                'government': 0.98,
                'api_research': 0.75,
                'industry_data': 0.60
            }
        }
    
    async def aggregate_multi_source_data(self, 
                                        research_results: List[ResearchResult]) -> AggregatedMineData:
        """
        Aggregiert Daten aus verschiedenen Quellen zu konsolidiertem Dataset
        """
        
        # Group results by data field
        field_data = defaultdict(list)
        
        for result in research_results:
            for field, value in result.data.items():
                if value and str(value).strip():
                    field_data[field].append({
                        'value': value,
                        'source': result.source,
                        'source_type': result.source_type,
                        'quality_score': result.quality_score,
                        'timestamp': result.timestamp
                    })
        
        # Aggregate each field using weighted consensus
        aggregated_data = {}
        confidence_scores = {}
        
        for field, values in field_data.items():
            if values:
                aggregated_value, confidence = self._aggregate_field_values(field, values)
                aggregated_data[field] = aggregated_value
                confidence_scores[field] = confidence
        
        return AggregatedMineData(
            data=aggregated_data,
            confidence_scores=confidence_scores,
            source_count=len(research_results),
            aggregation_method="weighted_consensus"
        )
    
    def _aggregate_field_values(self, field: str, values: List[Dict]) -> Tuple[str, float]:
        """
        Aggregiert Werte für ein spezifisches Feld unter Berücksichtigung von Gewichtungen
        """
        
        # Calculate weighted scores for each value
        value_scores = defaultdict(float)
        total_weight = 0
        
        for item in values:
            # Base weight from source type
            base_weight = self.source_weights.get(item['source_type'], 0.5)
            
            # Data type specific weight
            type_weights = self.data_type_weights.get(field, {})
            type_weight = type_weights.get(item['source_type'], 1.0)
            
            # Quality score
            quality_weight = item['quality_score']
            
            # Age penalty (prefer newer data)
            age_penalty = self._calculate_age_penalty(item['timestamp'])
            
            # Combined weight
            final_weight = base_weight * type_weight * quality_weight * age_penalty
            
            value_scores[item['value']] += final_weight
            total_weight += final_weight
        
        # Select value with highest weighted score
        if value_scores:
            best_value = max(value_scores, key=value_scores.get)
            confidence = value_scores[best_value] / total_weight if total_weight > 0 else 0
            
            return best_value, confidence
        
        return "", 0.0
    
    def _calculate_age_penalty(self, timestamp: float) -> float:
        """Berechnet Altersstrafe für Daten (neuere Daten bevorzugt)"""
        
        current_time = time.time()
        age_days = (current_time - timestamp) / 86400  # Convert to days
        
        # Exponential decay: newer data weighted higher
        return max(0.3, math.exp(-age_days / 365))  # 1-year half-life
```

### **Conflict Resolution Engine**

```python
class ConflictResolutionEngine:
    """
    Löst Konflikte zwischen widersprüchlichen Datenquellen
    Implementiert verschiedene Resolution-Strategien
    """
    
    def __init__(self):
        self.resolution_strategies = {
            'restoration_costs': self._resolve_financial_conflicts,
            'operator': self._resolve_operator_conflicts,
            'status': self._resolve_status_conflicts,
            'coordinates': self._resolve_coordinate_conflicts,
            'default': self._resolve_by_source_reliability
        }
    
    async def resolve_conflicts(self, aggregated_data: AggregatedMineData) -> ResolvedMineData:
        """
        Identifiziert und löst Datenkonflikte zwischen Quellen
        """
        
        conflicts = self._identify_conflicts(aggregated_data)
        resolved_data = aggregated_data.data.copy()
        resolution_log = []
        
        for field, conflict_info in conflicts.items():
            # Select appropriate resolution strategy
            resolver = self.resolution_strategies.get(field, self.resolution_strategies['default'])
            
            # Resolve conflict
            resolved_value, resolution_reason = resolver(conflict_info)
            
            resolved_data[field] = resolved_value
            resolution_log.append({
                'field': field,
                'conflict_values': conflict_info['values'],
                'resolved_value': resolved_value,
                'reason': resolution_reason
            })
        
        return ResolvedMineData(
            data=resolved_data,
            resolution_log=resolution_log,
            conflict_count=len(conflicts)
        )
    
    def _resolve_financial_conflicts(self, conflict_info: Dict) -> Tuple[str, str]:
        """
        Spezielle Resolution für Finanz-/Kostendaten
        Bevorzugt offizielle Quellen und neuere Daten
        """
        
        values = conflict_info['values']
        
        # Priority: Government > Company Reports > API Research
        for value_info in values:
            if value_info['source_type'] == 'government':
                return value_info['value'], "Government source priority"
        
        for value_info in values:
            if value_info['source_type'] == 'company_reports':
                return value_info['value'], "Official company report"
        
        # Fallback to highest quality score
        best_value = max(values, key=lambda x: x['quality_score'])
        return best_value['value'], "Highest quality score"
    
    def _resolve_operator_conflicts(self, conflict_info: Dict) -> Tuple[str, str]:
        """
        Löst Konflikte bei Betreiber-Information
        Berücksichtigt Unternehmensstrukturen und Akquisitionen
        """
        
        values = conflict_info['values']
        
        # Check for parent-subsidiary relationships
        company_names = [v['value'] for v in values]
        
        # Known parent companies (could be expanded with corporate database)
        parent_companies = {
            'Newmont Corporation': ['Newmont', 'Newmont Corp', 'Newmont Goldcorp'],
            'Agnico Eagle Mines Limited': ['Agnico Eagle', 'AEM', 'Agnico'],
            'IAMGOLD Corporation': ['IAMGOLD', 'IAM', 'Iamgold Corp']
        }
        
        # Prefer full legal name over abbreviations
        for parent, subsidiaries in parent_companies.items():
            if parent in company_names:
                return parent, "Full legal company name"
            
        # Fallback to most recent data
        newest_value = max(values, key=lambda x: x['timestamp'])
        return newest_value['value'], "Most recent information"
```

---

## 📊 **QUALITY ASSESSMENT ENGINE**

### **Comprehensive Quality Assessment**

```python
class QualityAssessmentEngine:
    """
    Umfassende Qualitätsbewertung für Multi-Source-Daten
    Implementiert verschiedene Qualitätsmetriken
    """
    
    def __init__(self):
        self.quality_metrics = {
            'completeness': self._assess_completeness,
            'consistency': self._assess_consistency,
            'accuracy': self._assess_accuracy,
            'freshness': self._assess_freshness,
            'source_reliability': self._assess_source_reliability
        }
        
        # Expected data field importance weights
        self.field_importance = {
            'name': 1.0,
            'operator': 0.9,
            'restoration_costs': 0.95,
            'status': 0.85,
            'mine_type': 0.7,
            'commodity': 0.8,
            'coordinates': 0.6,
            'production_data': 0.75
        }
    
    async def assess_comprehensive_quality(self, 
                                         resolved_data: ResolvedMineData) -> QualityReport:
        """
        Führt umfassende Qualitätsbewertung durch
        """
        
        quality_scores = {}
        detailed_assessments = {}
        
        for metric_name, metric_func in self.quality_metrics.items():
            score, details = metric_func(resolved_data)
            quality_scores[metric_name] = score
            detailed_assessments[metric_name] = details
        
        # Calculate overall quality score
        overall_score = self._calculate_overall_quality(quality_scores)
        
        # Generate recommendations
        recommendations = self._generate_quality_recommendations(quality_scores, detailed_assessments)
        
        return QualityReport(
            overall_score=overall_score,
            metric_scores=quality_scores,
            detailed_assessments=detailed_assessments,
            recommendations=recommendations,
            quality_grade=self._assign_quality_grade(overall_score)
        )
    
    def _assess_completeness(self, resolved_data: ResolvedMineData) -> Tuple[float, Dict]:
        """
        Bewertet Vollständigkeit der Daten
        """
        
        expected_fields = list(self.field_importance.keys())
        filled_fields = []
        missing_fields = []
        
        for field in expected_fields:
            if resolved_data.data.get(field) and str(resolved_data.data[field]).strip():
                filled_fields.append(field)
            else:
                missing_fields.append(field)
        
        # Weight by field importance
        filled_weight = sum(self.field_importance[field] for field in filled_fields)
        total_weight = sum(self.field_importance.values())
        
        completeness_score = filled_weight / total_weight if total_weight > 0 else 0
        
        details = {
            'filled_fields': filled_fields,
            'missing_fields': missing_fields,
            'weighted_completeness': completeness_score,
            'field_count': f"{len(filled_fields)}/{len(expected_fields)}"
        }
        
        return completeness_score, details
    
    def _assess_consistency(self, resolved_data: ResolvedMineData) -> Tuple[float, Dict]:
        """
        Bewertet interne Konsistenz der Daten
        """
        
        consistency_checks = []
        
        # Check 1: Mine type vs commodity consistency
        mine_type = resolved_data.data.get('mine_type', '').lower()
        commodity = resolved_data.data.get('commodity', '').lower()
        
        if mine_type and commodity:
            # Known inconsistent combinations
            inconsistent_combinations = [
                ('underground', 'iron ore'),  # Iron ore typically open-pit
                ('surface', 'gold')  # Gold often underground
            ]
            
            is_consistent = not any(
                mine_type_check in mine_type and commodity_check in commodity
                for mine_type_check, commodity_check in inconsistent_combinations
            )
            
            consistency_checks.append(('mine_type_commodity', is_consistent))
        
        # Check 2: Status vs production dates consistency
        status = resolved_data.data.get('status', '').lower()
        prod_end = resolved_data.data.get('production_end', '')
        
        if status and prod_end:
            # If status is "closed" there should be an end date
            if 'closed' in status and not prod_end:
                consistency_checks.append(('status_production_end', False))
            else:
                consistency_checks.append(('status_production_end', True))
        
        # Calculate consistency score
        if consistency_checks:
            consistent_count = sum(1 for _, is_consistent in consistency_checks if is_consistent)
            consistency_score = consistent_count / len(consistency_checks)
        else:
            consistency_score = 1.0  # No checks to fail
        
        details = {
            'checks_performed': len(consistency_checks),
            'checks_passed': sum(1 for _, result in consistency_checks if result),
            'consistency_issues': [check for check, result in consistency_checks if not result]
        }
        
        return consistency_score, details
    
    def _assess_accuracy(self, resolved_data: ResolvedMineData) -> Tuple[float, Dict]:
        """
        Bewertet Plausibilität/Genauigkeit der Daten
        """
        
        plausibility_checks = []
        
        # Check restoration costs plausibility
        costs = resolved_data.data.get('restoration_costs', '')
        if costs:
            try:
                cost_value = float(re.sub(r'[^\d.]', '', costs))
                # Reasonable range for restoration costs (CAD)
                if 100_000 <= cost_value <= 10_000_000_000:
                    plausibility_checks.append(('restoration_costs_range', True))
                else:
                    plausibility_checks.append(('restoration_costs_range', False))
            except ValueError:
                plausibility_checks.append(('restoration_costs_format', False))
        
        # Check coordinate plausibility for Quebec
        x_coord = resolved_data.data.get('x_coordinate', '')
        y_coord = resolved_data.data.get('y_coordinate', '')
        
        if x_coord and y_coord:
            try:
                x, y = float(x_coord), float(y_coord)
                # Quebec coordinate ranges (approximate)
                if 45 <= y <= 62 and -85 <= x <= -57:
                    plausibility_checks.append(('quebec_coordinates', True))
                else:
                    plausibility_checks.append(('quebec_coordinates', False))
            except ValueError:
                plausibility_checks.append(('coordinate_format', False))
        
        # Calculate accuracy score
        if plausibility_checks:
            accurate_count = sum(1 for _, is_accurate in plausibility_checks if is_accurate)
            accuracy_score = accurate_count / len(plausibility_checks)
        else:
            accuracy_score = 0.8  # Neutral score when no checks possible
        
        details = {
            'plausibility_checks': len(plausibility_checks),
            'checks_passed': sum(1 for _, result in plausibility_checks if result),
            'accuracy_issues': [check for check, result in plausibility_checks if not result]
        }
        
        return accuracy_score, details
    
    def _assess_freshness(self, resolved_data: ResolvedMineData) -> Tuple[float, Dict]:
        """
        Bewertet Aktualität der Daten
        """
        
        current_time = time.time()
        timestamps = []
        
        # Collect timestamps from resolution log
        for log_entry in resolved_data.resolution_log:
            if 'timestamp' in log_entry:
                timestamps.append(log_entry['timestamp'])
        
        if not timestamps:
            return 0.5, {'status': 'no_timestamp_data'}  # Neutral score
        
        # Calculate average age
        avg_timestamp = sum(timestamps) / len(timestamps)
        age_days = (current_time - avg_timestamp) / 86400
        
        # Freshness scoring (newer is better)
        if age_days <= 30:      # Within 1 month
            freshness_score = 1.0
        elif age_days <= 90:    # Within 3 months
            freshness_score = 0.8
        elif age_days <= 365:   # Within 1 year
            freshness_score = 0.6
        elif age_days <= 730:   # Within 2 years
            freshness_score = 0.4
        else:                   # Older than 2 years
            freshness_score = 0.2
        
        details = {
            'average_age_days': round(age_days, 1),
            'data_sources_count': len(timestamps),
            'freshness_category': self._categorize_freshness(age_days)
        }
        
        return freshness_score, details
    
    def _generate_quality_recommendations(self, quality_scores: Dict, 
                                        detailed_assessments: Dict) -> List[str]:
        """
        Generiert Empfehlungen zur Verbesserung der Datenqualität
        """
        
        recommendations = []
        
        # Completeness recommendations
        if quality_scores['completeness'] < 0.7:
            missing_fields = detailed_assessments['completeness']['missing_fields']
            high_priority_missing = [field for field in missing_fields 
                                   if self.field_importance.get(field, 0) > 0.8]
            
            if high_priority_missing:
                recommendations.append(
                    f"Priority: Search for missing critical fields: {', '.join(high_priority_missing)}"
                )
        
        # Consistency recommendations
        if quality_scores['consistency'] < 0.8:
            issues = detailed_assessments['consistency']['consistency_issues']
            if issues:
                recommendations.append(
                    f"Review data consistency issues: {', '.join(issues)}"
                )
        
        # Freshness recommendations
        if quality_scores['freshness'] < 0.6:
            age_days = detailed_assessments['freshness'].get('average_age_days', 0)
            recommendations.append(
                f"Data is {age_days:.0f} days old - consider updated search"
            )
        
        return recommendations
```

---

## 🚀 **EXECUTION STRATEGIES**

### **Strategy-Based Research Planning**

```python
class ResearchStrategyEngine:
    """
    Intelligente Strategieauswahl für verschiedene Anwendungsfälle
    Optimiert Kosten, Zeit und Datenqualität basierend auf Anforderungen
    """
    
    def __init__(self):
        self.strategies = {
            'fast': FastStrategy(),
            'balanced': BalancedStrategy(),
            'comprehensive': ComprehensiveStrategy(),
            'budget': BudgetOptimizedStrategy(),
            'government_focus': GovernmentFocusStrategy(),
            'financial_focus': FinancialFocusStrategy()
        }
    
    def create_execution_plan(self, mine_name: str, strategy: str, 
                            constraints: Dict = None) -> ExecutionPlan:
        """
        Erstellt optimalen Execution Plan basierend auf Strategie
        """
        
        strategy_engine = self.strategies.get(strategy, self.strategies['balanced'])
        
        return strategy_engine.create_plan(mine_name, constraints or {})

class BalancedStrategy:
    """
    Ausgewogene Strategie für optimales Kosten/Nutzen-Verhältnis
    Target: 85% Datenabdeckung bei moderaten Kosten
    """
    
    def create_plan(self, mine_name: str, constraints: Dict) -> ExecutionPlan:
        """
        Balanced Strategy Implementation
        """
        
        plan = ExecutionPlan(mine_name=mine_name, strategy="balanced")
        
        # Phase 1: Tier 1 APIs (parallel)
        plan.add_phase(PhaseConfig(
            name="primary_research",
            apis=['perplexity', 'tavily'],
            execution_mode='parallel',
            timeout=90,
            required_success_count=1,
            cost_limit=0.025
        ))
        
        # Phase 2: Gap Analysis & Tier 2 (conditional)
        plan.add_phase(PhaseConfig(
            name="gap_filling",
            apis=['exa', 'searchapi'],
            execution_mode='conditional',
            trigger_condition='completeness < 0.7',
            timeout=60,
            cost_limit=0.015
        ))
        
        # Phase 3: Government Scraping (if critical data missing)
        plan.add_phase(PhaseConfig(
            name="government_scraping",
            scrapers=['apify_quebec_mern'],
            execution_mode='conditional',
            trigger_condition='missing_critical_fields',
            timeout=120,
            cost_limit=0.05
        ))
        
        return plan

class ComprehensiveStrategy:
    """
    Umfassende Strategie für maximale Datenabdeckung
    Target: 95% Datenabdeckung, höhere Kosten akzeptabel
    """
    
    def create_plan(self, mine_name: str, constraints: Dict) -> ExecutionPlan:
        """
        Comprehensive Strategy Implementation
        """
        
        plan = ExecutionPlan(mine_name=mine_name, strategy="comprehensive")
        
        # Phase 1: All Tier 1 APIs (parallel)
        plan.add_phase(PhaseConfig(
            name="comprehensive_primary",
            apis=['perplexity', 'tavily', 'searchapi'],
            execution_mode='parallel',
            timeout=120,
            required_success_count=2,
            cost_limit=0.05
        ))
        
        # Phase 2: All Tier 2 APIs (parallel)
        plan.add_phase(PhaseConfig(
            name="comprehensive_secondary",
            apis=['exa', 'serper', 'brave'],
            execution_mode='parallel',
            timeout=90,
            cost_limit=0.08
        ))
        
        # Phase 3: All Scraping Services (sequential)
        plan.add_phase(PhaseConfig(
            name="comprehensive_scraping",
            scrapers=['apify_quebec_mern', 'scrapingbee_company', 'firecrawl_industry'],
            execution_mode='sequential',
            timeout=300,
            cost_limit=0.15
        ))
        
        # Phase 4: Quality Enhancement (conditional)
        plan.add_phase(PhaseConfig(
            name="quality_enhancement",
            apis=['perplexity'],  # Re-search with refined prompts
            execution_mode='conditional',
            trigger_condition='quality_score < 0.8',
            timeout=60,
            cost_limit=0.02
        ))
        
        return plan
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Adaptive Performance Tuning**

```python
class AdaptivePerformanceOptimizer:
    """
    Dynamische Performance-Optimierung basierend auf historischen Daten
    Lernt aus vergangenen Requests und passt Strategien an
    """
    
    def __init__(self):
        self.performance_history = PerformanceDatabase()
        self.api_health_monitor = APIHealthMonitor()
        
    async def optimize_execution_plan(self, plan: ExecutionPlan) -> ExecutionPlan:
        """
        Optimiert Execution Plan basierend auf aktueller API-Performance
        """
        
        # Check current API health
        api_health = await self.api_health_monitor.check_all_apis()
        
        # Adjust plan based on API availability
        optimized_plan = plan.copy()
        
        for phase in optimized_plan.phases:
            # Remove unhealthy APIs
            healthy_apis = [api for api in phase.apis 
                          if api_health.get(api, {}).get('healthy', False)]
            
            if len(healthy_apis) < len(phase.apis):
                logger.warning(f"Removing unhealthy APIs: {set(phase.apis) - set(healthy_apis)}")
                phase.apis = healthy_apis
            
            # Adjust timeouts based on recent performance
            avg_response_time = self._get_average_response_time(phase.apis)
            if avg_response_time:
                phase.timeout = max(phase.timeout, avg_response_time * 2)
        
        return optimized_plan
    
    def _get_average_response_time(self, apis: List[str]) -> float:
        """Berechnet durchschnittliche Response-Zeit für API-Liste"""
        
        total_time = 0
        count = 0
        
        for api in apis:
            recent_times = self.performance_history.get_recent_response_times(api, days=7)
            if recent_times:
                total_time += sum(recent_times)
                count += len(recent_times)
        
        return total_time / count if count > 0 else 0
```

---

**Status:** ✅ Multi-Source Framework Complete  
**Implementation Phase:** Phase 2+ (nach successful Phase 1)  
**Complexity Level:** Advanced - erfordert solide Phase 1 Basis  

*Dieses Framework ermöglicht skalierbare Multi-API-Integration mit intelligenter Qualitätssicherung und adaptiver Performance-Optimierung.*