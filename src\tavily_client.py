"""
Tavily AI Client für MineExtractorWeb v1.0
Minimaler Client für Tavily API Integration (Phase 2 Vorbereitung)
"""

import aiohttp
import asyncio
import json
import time
from typing import Dict, Optional, List, Any
import logging

logger = logging.getLogger(__name__)

class TavilyAPIError(Exception):
    """Custom exception für Tavily API Fehler"""
    pass

class TavilyClient:
    """
    Minimaler Tavily AI Client für Government Data Research
    Phase 2 Implementation - Government & Regulatory Data Focus
    """
    
    def __init__(self, api_key: str = None):
        # Load API configuration
        try:
            from config.api_keys import APIConfig
            self.api_key = api_key or APIConfig.TAVILY_API_KEY
            self.base_url = APIConfig.TAVILY_BASE_URL
        except ImportError:
            # Fallback configuration
            self.api_key = api_key
            self.base_url = 'https://api.tavily.com/search'
        
        # Validate API key
        if not self.api_key:
            raise ValueError("Tavily API key is required")
        
        # Session will be created when needed
        self.session = None
        
        # Request tracking
        self.request_count = 0
        self.last_request_time = 0
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._close_session()
    
    async def _ensure_session(self):
        """Stellt sicher, dass eine Session existiert"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _close_session(self):
        """Schließt die Session sauber"""
        if self.session and not self.session.closed:
            await self.session.close()
            await asyncio.sleep(0.1)
    
    def _build_headers(self) -> Dict[str, str]:
        """Erstellt HTTP Headers für API-Requests"""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "MineExtractorWeb/1.0"
        }
    
    async def test_connection(self) -> bool:
        """
        Testet API-Verbindung mit minimaler Anfrage
        
        Returns:
            True wenn Verbindung erfolgreich, False sonst
        """
        
        try:
            await self._ensure_session()
            
            # Corrected Tavily API payload format
            test_payload = {
                "api_key": self.api_key,
                "query": "test",
                "search_depth": "basic",
                "include_answer": False,
                "include_images": False,
                "include_raw_content": False,
                "max_results": 1
            }
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "MineExtractorWeb/1.0"
            }
            
            # Use POST to /search endpoint
            search_url = "https://api.tavily.com/search"
            
            async with self.session.post(search_url, headers=headers, json=test_payload) as response:
                response_text = await response.text()
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        logger.info("Tavily API connection test successful")
                        return True
                    except:
                        logger.warning("Tavily API returned non-JSON response")
                        return False
                elif response.status == 401:
                    logger.error("Tavily API authentication failed - check API key")
                    return False
                elif response.status == 403:
                    logger.error("Tavily API access forbidden - check API key permissions")
                    return False
                elif response.status == 400:
                    logger.warning(f"Tavily API bad request: {response_text}")
                    return False
                else:
                    logger.warning(f"Tavily API test returned status {response.status}: {response_text}")
                    return False
                    
        except asyncio.TimeoutError:
            logger.error("Tavily API connection timeout")
            return False
        except Exception as e:
            logger.error(f"Tavily connection test failed: {e}")
            return False
    
    async def search_government_data(self, mine_name: str) -> Dict[str, Any]:
        """
        Placeholder für Government Data Search (Phase 2)
        
        Args:
            mine_name: Name der Mine
            
        Returns:
            Search results (currently placeholder)
        """
        
        logger.info(f"Government data search for {mine_name} - Phase 2 feature")
        
        # This will be implemented in Phase 2
        return {
            'success': False,
            'error': 'Tavily government data search not implemented yet (Phase 2)',
            'mine_name': mine_name,
            'results': []
        }

# Convenience functions
async def test_api_key(api_key: str) -> bool:
    """
    Testet Tavily API Key
    
    Args:
        api_key: Tavily API Key
        
    Returns:
        True wenn API Key funktioniert
    """
    
    try:
        async with TavilyClient(api_key) as client:
            return await client.test_connection()
    except Exception as e:
        logger.error(f"Tavily API key test failed: {e}")
        return False

if __name__ == "__main__":
    # Test Tavily Client
    async def test_client():
        print("🧪 Testing Tavily Client...")
        
        try:
            # Test ohne real API key
            client = TavilyClient("test-key") 
            print("✅ Tavily client initialized")
            print("⚠️  Phase 2 feature - Government data search coming soon")
            
        except Exception as e:
            print(f"❌ Tavily client test failed: {e}")
    
    # Run test
    asyncio.run(test_client())
    print("✅ Tavily client test completed!")
