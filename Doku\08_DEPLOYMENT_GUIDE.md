# Deployment Guide - MineExtractorWeb v1.0
**Production Deployment & Setup Guide für Web-Enhanced Mining Research System**

---

## 🎯 **DEPLOYMENT OVERVIEW**

Dieser Guide führt durch die komplette Production-Deployment des MineExtractorWeb Systems, von der initialen Installation bis zur produktiven Nutzung.

### **Deployment-Ziele:**
- **Production-Ready Setup:** Stabiles, zuverlässiges System
- **Performance Optimization:** Optimierte Konfiguration für beste Performance
- **Security & Compliance:** Sichere API-Handhabung und Datenschutz
- **Monitoring & Maintenance:** Überwachung und Wartungsroutinen

---

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **Systemvoraussetzungen:**

```bash
# Mindestanforderungen
OS: Windows 10/11 (64-bit) oder Windows Server 2019+
RAM: 8GB minimum, 16GB empfohlen
Storage: 5GB freier Speicherplatz
Internet: Stabile Breitbandverbindung
Python: 3.9+ (empfohlen: 3.11)

# Empfohlene Systemkonfiguration
OS: Windows 11 Pro
RAM: 32GB
CPU: Intel i7/AMD Ryzen 7 oder besser
Storage: SSD mit 20GB+ freiem Speicherplatz
Internet: 50+ Mbps symmetrisch
```

### **Account Setup Checklist:**

```markdown
## API-Accounts (Phase 1 - Minimum)
- [ ] ✅ Perplexity AI Pro Account ($20/Monat)
  - Account erstellt: [ ]
  - API Key generiert: [ ]
  - API Key getestet: [ ]

## API-Accounts (Phase 2+ - Vollausbau)  
- [ ] ⏳ Tavily AI Account ($20/Monat)
- [ ] ⏳ Exa.ai Account ($29/Monat)
- [ ] ⏳ SearchAPI Account ($29/Monat)
- [ ] ⏳ Apify Account ($49/Monat)
- [ ] ⏳ ScrapingBee Account ($29/Monat)
- [ ] ⏳ FireCrawl Account ($29/Monat)

## Security Setup
- [ ] ✅ API Keys sicher gespeichert
- [ ] ✅ Environment Variables konfiguriert
- [ ] ✅ Backup-Strategie für Konfiguration
- [ ] ✅ Access Control für System-Verzeichnisse
```

---

## 🚀 **PHASE 1 DEPLOYMENT (Quick Start)**

### **Step 1: Systemvorbereitung**

```powershell
# PowerShell als Administrator ausführen

# 1. Python Installation überprüfen
python --version
# Sollte Python 3.9+ anzeigen

# 2. Arbeitsverzeichnis erstellen
New-Item -ItemType Directory -Path "C:\MineExtractorWeb" -Force
Set-Location "C:\MineExtractorWeb"

# 3. Virtual Environment erstellen
python -m venv venv
.\venv\Scripts\Activate.ps1

# 4. Upgrade pip
python -m pip install --upgrade pip
```

### **Step 2: Basis-Installation**

```powershell
# 1. Repository klonen/kopieren
# Annahme: Dateien sind bereits in C:\Temp\Minen\MineExtractorWeb_v1\
Copy-Item -Path "C:\Temp\Minen\MineExtractorWeb_v1\*" -Destination "C:\MineExtractorWeb" -Recurse -Force

# 2. Dependencies installieren
pip install -r requirements.txt

# 3. Zusätzliche Production Dependencies
pip install psutil watchdog python-dotenv cryptography
```

### **Step 3: Environment Configuration**

```powershell
# 1. Erstelle .env Datei
@"
# MineExtractorWeb Production Configuration
# Phase 1 - Basic Setup

# Core APIs
PERPLEXITY_API_KEY=your_perplexity_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
GEMINI_API_KEY=your_gemini_key_here

# System Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=120
MAX_RETRIES=3

# Data Paths
DATA_DIRECTORY=C:\MineExtractorWeb\data
LOG_DIRECTORY=C:\MineExtractorWeb\logs
OUTPUT_DIRECTORY=C:\MineExtractorWeb\output
BACKUP_DIRECTORY=C:\MineExtractorWeb\backups

# Performance Settings
ENABLE_CACHING=true
CACHE_DURATION_HOURS=24
BATCH_SIZE=10

# Security Settings
ENCRYPT_API_KEYS=true
SECURE_MODE=true
"@ | Out-File -FilePath ".env" -Encoding UTF8
```

### **Step 4: Verzeichnisstruktur Setup**

```powershell
# Erstelle Produktions-Verzeichnisstruktur
$directories = @(
    "data",
    "logs",
    "output", 
    "backups",
    "config",
    "temp",
    "reports"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force
}

# Setze Berechtigungen
icacls "config" /grant:r "$env:USERNAME:(OI)(CI)F" /T
icacls "logs" /grant:r "$env:USERNAME:(OI)(CI)F" /T
icacls "backups" /grant:r "$env:USERNAME:(OI)(CI)F" /T
```

### **Step 5: Initial Configuration**

```python
# config/production_config.py
import os
from pathlib import Path

class ProductionConfig:
    """Production Configuration für MineExtractorWeb"""
    
    # Base paths
    BASE_DIR = Path("C:/MineExtractorWeb")
    DATA_DIR = BASE_DIR / "data"
    LOG_DIR = BASE_DIR / "logs" 
    OUTPUT_DIR = BASE_DIR / "output"
    BACKUP_DIR = BASE_DIR / "backups"
    
    # API Configuration
    PERPLEXITY_API_KEY = os.getenv('PERPLEXITY_API_KEY')
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    
    # Performance Settings
    MAX_CONCURRENT_REQUESTS = int(os.getenv('MAX_CONCURRENT_REQUESTS', 5))
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 120))
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', 10))
    
    # Caching
    ENABLE_CACHING = os.getenv('ENABLE_CACHING', 'true').lower() == 'true'
    CACHE_DURATION = int(os.getenv('CACHE_DURATION_HOURS', 24)) * 3600
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}'
    
    # Security
    ENCRYPT_API_KEYS = os.getenv('ENCRYPT_API_KEYS', 'true').lower() == 'true'
    SECURE_MODE = os.getenv('SECURE_MODE', 'true').lower() == 'true'
    
    @classmethod
    def validate_config(cls):
        """Validiert Production Configuration"""
        errors = []
        
        if not cls.PERPLEXITY_API_KEY:
            errors.append("PERPLEXITY_API_KEY not configured")
        
        if not cls.DATA_DIR.exists():
            errors.append(f"Data directory not found: {cls.DATA_DIR}")
        
        if not cls.LOG_DIR.exists():
            errors.append(f"Log directory not found: {cls.LOG_DIR}")
        
        return errors
```

### **Step 6: System Testing**

```python
# deployment/system_test.py
import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from config.production_config import ProductionConfig
from src.web_researcher import WebMiningResearcher

class SystemTester:
    """Comprehensive System Testing für Production Deployment"""
    
    def __init__(self):
        self.config = ProductionConfig()
        self.test_results = {}
    
    async def run_full_system_test(self):
        """Führt vollständigen System-Test durch"""
        
        print("🧪 Starting MineExtractorWeb System Test")
        print("=" * 50)
        
        # Configuration Test
        config_result = self.test_configuration()
        self.test_results['configuration'] = config_result
        
        # API Connectivity Test
        api_result = await self.test_api_connectivity()
        self.test_results['api_connectivity'] = api_result
        
        # File System Test
        filesystem_result = self.test_filesystem_access()
        self.test_results['filesystem'] = filesystem_result
        
        # Performance Test
        performance_result = await self.test_basic_performance()
        self.test_results['performance'] = performance_result
        
        # Generate Test Report
        self.generate_test_report()
        
        return self.test_results
    
    def test_configuration(self):
        """Testet System-Konfiguration"""
        
        print("\n📋 Testing Configuration...")
        
        config_errors = ProductionConfig.validate_config()
        
        if config_errors:
            print("❌ Configuration Errors:")
            for error in config_errors:
                print(f"   - {error}")
            return False
        else:
            print("✅ Configuration Valid")
            return True
    
    async def test_api_connectivity(self):
        """Testet API-Verbindungen"""
        
        print("\n🔌 Testing API Connectivity...")
        
        api_results = {}
        
        # Test Perplexity
        if ProductionConfig.PERPLEXITY_API_KEY:
            try:
                researcher = WebMiningResearcher({
                    'perplexity_key': ProductionConfig.PERPLEXITY_API_KEY
                })
                
                # Simple connectivity test
                connected = await researcher.test_api_connection()
                api_results['perplexity'] = connected
                
                if connected:
                    print("✅ Perplexity API: Connected")
                else:
                    print("❌ Perplexity API: Connection failed")
                    
            except Exception as e:
                print(f"❌ Perplexity API: Error - {e}")
                api_results['perplexity'] = False
        else:
            print("⚠️ Perplexity API: Not configured")
            api_results['perplexity'] = None
        
        return api_results
    
    def test_filesystem_access(self):
        """Testet Dateisystem-Zugriff"""
        
        print("\n💾 Testing Filesystem Access...")
        
        directories_to_test = [
            ProductionConfig.DATA_DIR,
            ProductionConfig.LOG_DIR,
            ProductionConfig.OUTPUT_DIR,
            ProductionConfig.BACKUP_DIR
        ]
        
        filesystem_ok = True
        
        for directory in directories_to_test:
            try:
                # Test write access
                test_file = directory / "test_write.tmp"
                test_file.write_text("test")
                test_file.unlink()
                
                print(f"✅ {directory.name}: Read/Write OK")
                
            except Exception as e:
                print(f"❌ {directory.name}: Access failed - {e}")
                filesystem_ok = False
        
        return filesystem_ok
    
    async def test_basic_performance(self):
        """Testet Basic Performance"""
        
        print("\n⚡ Testing Basic Performance...")
        
        if not ProductionConfig.PERPLEXITY_API_KEY:
            print("⚠️ Skipping performance test - API not configured")
            return None
        
        try:
            import time
            
            researcher = WebMiningResearcher({
                'perplexity_key': ProductionConfig.PERPLEXITY_API_KEY
            })
            
            # Time a simple research operation
            start_time = time.time()
            result = await researcher.research_mine_comprehensive("Test Mine")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            print(f"✅ Performance Test: {response_time:.2f}s response time")
            
            if response_time < 60:
                return "excellent"
            elif response_time < 120:
                return "good"
            else:
                return "slow"
                
        except Exception as e:
            print(f"❌ Performance Test failed: {e}")
            return "failed"
    
    def generate_test_report(self):
        """Generiert Test-Report"""
        
        print("\n📊 System Test Report")
        print("=" * 30)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result is True or result in ['excellent', 'good'])
        
        print(f"Overall: {passed_tests}/{total_tests} tests passed")
        
        for test_name, result in self.test_results.items():
            status = "✅" if result is True or result in ['excellent', 'good'] else "❌"
            print(f"{status} {test_name}: {result}")
        
        if passed_tests == total_tests:
            print("\n🎉 System ready for production!")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} issues need to be resolved")

# Ausführen des System-Tests
if __name__ == "__main__":
    async def main():
        tester = SystemTester()
        await tester.run_full_system_test()
    
    asyncio.run(main())
```

---

## 🏭 **PRODUCTION SETUP (Phase 1)**

### **Windows Service Installation:**

```python
# deployment/windows_service.py
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
from pathlib import Path

class MineExtractorWebService(win32serviceutil.ServiceFramework):
    """Windows Service für MineExtractorWeb"""
    
    _svc_name_ = "MineExtractorWeb"
    _svc_display_name_ = "MineExtractor Web Research Service"
    _svc_description_ = "Automated mining data research service"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_running = True
        
        # Service configuration
        self.service_dir = Path("C:/MineExtractorWeb")
        self.log_file = self.service_dir / "logs" / "service.log"
    
    def SvcStop(self):
        """Service Stop Handler"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        self.log_message("Service stopped")
    
    def SvcDoRun(self):
        """Service Main Loop"""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        self.log_message("Service started")
        self.main_service_loop()
    
    def main_service_loop(self):
        """Hauptschleife des Service"""
        
        while self.is_running:
            # Check for stop signal
            rc = win32event.WaitForSingleObject(self.hWaitStop, 30000)  # 30 seconds
            
            if rc == win32event.WAIT_OBJECT_0:
                # Stop signal received
                break
            
            # Perform service tasks
            try:
                self.check_system_health()
                self.process_scheduled_tasks()
                
            except Exception as e:
                self.log_message(f"Service error: {e}")
    
    def check_system_health(self):
        """Überprüft System-Gesundheit"""
        
        # Check disk space
        import shutil
        free_space = shutil.disk_usage(self.service_dir).free
        
        if free_space < 1_000_000_000:  # Less than 1GB
            self.log_message(f"Warning: Low disk space - {free_space / 1_000_000_000:.1f}GB remaining")
        
        # Check log file size
        if self.log_file.exists() and self.log_file.stat().st_size > 10_000_000:  # 10MB
            self.rotate_log_file()
    
    def process_scheduled_tasks(self):
        """Verarbeitet geplante Tasks"""
        
        # Check for scheduled research tasks
        # This would integrate with your task scheduling system
        pass
    
    def log_message(self, message):
        """Schreibt Log-Nachricht"""
        
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {message}\n")
                
        except Exception:
            pass  # Silent fail for logging
    
    def rotate_log_file(self):
        """Rotiert Log-Datei"""
        
        try:
            if self.log_file.exists():
                backup_file = self.log_file.with_suffix('.log.old')
                if backup_file.exists():
                    backup_file.unlink()
                self.log_file.rename(backup_file)
                
        except Exception as e:
            pass

# Service Installation Commands
if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(MineExtractorWebService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(MineExtractorWebService)
```

### **Task Scheduler Setup:**

```powershell
# deployment/setup_scheduler.ps1

# MineExtractorWeb Scheduled Tasks Setup

# 1. Daily Health Check
$action = New-ScheduledTaskAction -Execute "python" -Argument "C:\MineExtractorWeb\deployment\health_check.py"
$trigger = New-ScheduledTaskTrigger -Daily -At "06:00"
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries

Register-ScheduledTask -TaskName "MineExtractorWeb-HealthCheck" -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "Daily health check for MineExtractorWeb"

# 2. Weekly Backup
$backupAction = New-ScheduledTaskAction -Execute "python" -Argument "C:\MineExtractorWeb\deployment\backup_system.py"
$backupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "02:00"

Register-ScheduledTask -TaskName "MineExtractorWeb-Backup" -Action $backupAction -Trigger $backupTrigger -Principal $principal -Settings $settings -Description "Weekly backup of MineExtractorWeb data and configuration"

# 3. Log Rotation
$logAction = New-ScheduledTaskAction -Execute "python" -Argument "C:\MineExtractorWeb\deployment\rotate_logs.py"
$logTrigger = New-ScheduledTaskTrigger -Daily -At "23:00"

Register-ScheduledTask -TaskName "MineExtractorWeb-LogRotation" -Action $logAction -Trigger $logTrigger -Principal $principal -Settings $settings -Description "Daily log rotation for MineExtractorWeb"

Write-Host "✅ Scheduled tasks created successfully"
```

---

## 📊 **MONITORING & LOGGING SETUP**

### **Production Logging Configuration:**

```python
# config/logging_config.py
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime

class ProductionLogger:
    """Production-grade Logging Setup"""
    
    def __init__(self, log_dir: Path = None):
        self.log_dir = log_dir or Path("C:/MineExtractorWeb/logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.setup_loggers()
    
    def setup_loggers(self):
        """Setup verschiedene Logger für Production"""
        
        # Main application logger
        self.setup_app_logger()
        
        # API request logger
        self.setup_api_logger()
        
        # Performance logger
        self.setup_performance_logger()
        
        # Error logger
        self.setup_error_logger()
    
    def setup_app_logger(self):
        """Main Application Logger"""
        
        app_logger = logging.getLogger('mineextractor.app')
        app_logger.setLevel(logging.INFO)
        
        # Rotating file handler
        app_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'application.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
        )
        app_handler.setFormatter(formatter)
        app_logger.addHandler(app_handler)
        
        # Console handler for development
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        app_logger.addHandler(console_handler)
    
    def setup_api_logger(self):
        """API Request Logger"""
        
        api_logger = logging.getLogger('mineextractor.api')
        api_logger.setLevel(logging.INFO)
        
        api_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'api_requests.log',
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        
        # Detailed formatter for API calls
        api_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | API: %(api_name)s | Mine: %(mine_name)s | '
            'Response Time: %(response_time)s | Status: %(status)s | %(message)s'
        )
        api_handler.setFormatter(api_formatter)
        api_logger.addHandler(api_handler)
    
    def setup_performance_logger(self):
        """Performance Metrics Logger"""
        
        perf_logger = logging.getLogger('mineextractor.performance')
        perf_logger.setLevel(logging.INFO)
        
        perf_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'performance.log',
            maxBytes=20*1024*1024,  # 20MB
            backupCount=5
        )
        
        perf_formatter = logging.Formatter(
            '%(asctime)s | %(metric_name)s | %(metric_value)s | %(metric_unit)s | %(details)s'
        )
        perf_handler.setFormatter(perf_formatter)
        perf_logger.addHandler(perf_handler)
    
    def setup_error_logger(self):
        """Error & Exception Logger"""
        
        error_logger = logging.getLogger('mineextractor.errors')
        error_logger.setLevel(logging.ERROR)
        
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / 'errors.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10
        )
        
        error_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d | %(message)s'
        )
        error_handler.setFormatter(error_formatter)
        error_logger.addHandler(error_handler)

# Logger Usage Examples
def log_api_request(api_name: str, mine_name: str, response_time: float, 
                   status: str, message: str = ""):
    """Logs API request details"""
    
    logger = logging.getLogger('mineextractor.api')
    logger.info(message, extra={
        'api_name': api_name,
        'mine_name': mine_name,
        'response_time': f"{response_time:.2f}s",
        'status': status
    })

def log_performance_metric(metric_name: str, value: float, unit: str, details: str = ""):
    """Logs performance metric"""
    
    logger = logging.getLogger('mineextractor.performance')
    logger.info("", extra={
        'metric_name': metric_name,
        'metric_value': value,
        'metric_unit': unit,
        'details': details
    })
```

### **System Health Monitoring:**

```python
# deployment/health_monitor.py
import psutil
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
import logging

class SystemHealthMonitor:
    """Monitors system health and performance"""
    
    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path("C:/MineExtractorWeb")
        self.health_log = self.config_dir / "logs" / "health.log"
        self.alerts_file = self.config_dir / "logs" / "alerts.json"
        
        self.logger = logging.getLogger('mineextractor.health')
        
        # Health thresholds
        self.thresholds = {
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_free_gb': 5,
            'response_time_seconds': 300
        }
    
    def check_system_health(self) -> dict:
        """Comprehensive system health check"""
        
        health_data = {
            'timestamp': datetime.now().isoformat(),
            'cpu': self.check_cpu_usage(),
            'memory': self.check_memory_usage(),
            'disk': self.check_disk_usage(),
            'network': self.check_network_connectivity(),
            'application': self.check_application_health(),
            'overall_status': 'healthy'
        }
        
        # Determine overall health status
        issues = []
        for component, data in health_data.items():
            if isinstance(data, dict) and data.get('status') == 'warning':
                issues.append(f"{component}: {data.get('message', 'Unknown issue')}")
            elif isinstance(data, dict) and data.get('status') == 'critical':
                health_data['overall_status'] = 'critical'
                issues.append(f"{component}: {data.get('message', 'Critical issue')}")
        
        if issues and health_data['overall_status'] != 'critical':
            health_data['overall_status'] = 'warning'
        
        health_data['issues'] = issues
        
        # Log health data
        self.log_health_data(health_data)
        
        # Generate alerts if needed
        if health_data['overall_status'] in ['warning', 'critical']:
            self.generate_alert(health_data)
        
        return health_data
    
    def check_cpu_usage(self) -> dict:
        """Check CPU usage"""
        
        cpu_percent = psutil.cpu_percent(interval=1)
        
        if cpu_percent > self.thresholds['cpu_percent']:
            status = 'critical' if cpu_percent > 95 else 'warning'
            message = f"High CPU usage: {cpu_percent:.1f}%"
        else:
            status = 'healthy'
            message = f"CPU usage normal: {cpu_percent:.1f}%"
        
        return {
            'status': status,
            'value': cpu_percent,
            'unit': 'percent',
            'message': message
        }
    
    def check_memory_usage(self) -> dict:
        """Check memory usage"""
        
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        if memory_percent > self.thresholds['memory_percent']:
            status = 'critical' if memory_percent > 95 else 'warning'
            message = f"High memory usage: {memory_percent:.1f}%"
        else:
            status = 'healthy'
            message = f"Memory usage normal: {memory_percent:.1f}%"
        
        return {
            'status': status,
            'value': memory_percent,
            'unit': 'percent',
            'total_gb': round(memory.total / (1024**3), 1),
            'available_gb': round(memory.available / (1024**3), 1),
            'message': message
        }
    
    def check_disk_usage(self) -> dict:
        """Check disk space"""
        
        disk = psutil.disk_usage(self.config_dir)
        free_gb = disk.free / (1024**3)
        used_percent = (disk.used / disk.total) * 100
        
        if free_gb < self.thresholds['disk_free_gb']:
            status = 'critical' if free_gb < 1 else 'warning'
            message = f"Low disk space: {free_gb:.1f}GB free"
        else:
            status = 'healthy'
            message = f"Disk space adequate: {free_gb:.1f}GB free ({used_percent:.1f}% used)"
        
        return {
            'status': status,
            'free_gb': round(free_gb, 1),
            'used_percent': round(used_percent, 1),
            'total_gb': round(disk.total / (1024**3), 1),
            'message': message
        }
    
    def check_network_connectivity(self) -> dict:
        """Check network connectivity to key services"""
        
        import requests
        
        services_to_check = [
            ('Perplexity', 'https://api.perplexity.ai'),
            ('Google', 'https://www.google.com'),
        ]
        
        connectivity_results = []
        all_healthy = True
        
        for service_name, url in services_to_check:
            try:
                start_time = time.time()
                response = requests.get(url, timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    connectivity_results.append({
                        'service': service_name,
                        'status': 'healthy',
                        'response_time': round(response_time, 2)
                    })
                else:
                    connectivity_results.append({
                        'service': service_name,
                        'status': 'warning',
                        'response_time': round(response_time, 2),
                        'http_status': response.status_code
                    })
                    all_healthy = False
                    
            except Exception as e:
                connectivity_results.append({
                    'service': service_name,
                    'status': 'critical',
                    'error': str(e)
                })
                all_healthy = False
        
        return {
            'status': 'healthy' if all_healthy else 'warning',
            'services': connectivity_results,
            'message': 'All services reachable' if all_healthy else 'Some services unreachable'
        }
    
    def check_application_health(self) -> dict:
        """Check MineExtractorWeb application health"""
        
        health_indicators = []
        
        # Check if log files are being written (indicates activity)
        app_log = self.config_dir / "logs" / "application.log"
        if app_log.exists():
            last_modified = datetime.fromtimestamp(app_log.stat().st_mtime)
            time_since_update = datetime.now() - last_modified
            
            if time_since_update > timedelta(hours=24):
                health_indicators.append({
                    'component': 'logging',
                    'status': 'warning',
                    'message': f"No log activity for {time_since_update.total_seconds() / 3600:.1f} hours"
                })
            else:
                health_indicators.append({
                    'component': 'logging',
                    'status': 'healthy',
                    'message': f"Recent log activity: {time_since_update.total_seconds() / 60:.1f} minutes ago"
                })
        
        # Check if output directory has recent files
        output_dir = self.config_dir / "output"
        if output_dir.exists():
            recent_files = [f for f in output_dir.iterdir() 
                          if f.is_file() and 
                          datetime.fromtimestamp(f.stat().st_mtime) > datetime.now() - timedelta(days=7)]
            
            health_indicators.append({
                'component': 'output',
                'status': 'healthy' if recent_files else 'info',
                'message': f"{len(recent_files)} recent output files"
            })
        
        # Overall application health
        critical_issues = [h for h in health_indicators if h['status'] == 'critical']
        warning_issues = [h for h in health_indicators if h['status'] == 'warning']
        
        if critical_issues:
            overall_status = 'critical'
        elif warning_issues:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        return {
            'status': overall_status,
            'components': health_indicators,
            'message': f"Application health: {overall_status}"
        }
    
    def log_health_data(self, health_data: dict):
        """Log health data to file"""
        
        try:
            with open(self.health_log, 'a', encoding='utf-8') as f:
                f.write(f"{json.dumps(health_data)}\n")
                
        except Exception as e:
            self.logger.error(f"Failed to log health data: {e}")
    
    def generate_alert(self, health_data: dict):
        """Generate alert for health issues"""
        
        alert = {
            'timestamp': health_data['timestamp'],
            'severity': health_data['overall_status'],
            'issues': health_data['issues'],
            'summary': f"System health: {health_data['overall_status']}"
        }
        
        try:
            # Load existing alerts
            alerts = []
            if self.alerts_file.exists():
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts = json.load(f)
            
            # Add new alert
            alerts.append(alert)
            
            # Keep only last 100 alerts
            alerts = alerts[-100:]
            
            # Save alerts
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts, f, indent=2)
                
            self.logger.warning(f"Health alert generated: {alert['summary']}")
            
        except Exception as e:
            self.logger.error(f"Failed to generate alert: {e}")

# Health Check Script
if __name__ == "__main__":
    monitor = SystemHealthMonitor()
    health_data = monitor.check_system_health()
    
    print(f"System Health: {health_data['overall_status'].upper()}")
    
    if health_data['issues']:
        print("Issues found:")
        for issue in health_data['issues']:
            print(f"  - {issue}")
    else:
        print("All systems healthy")
```

---

## 🔐 **SECURITY & COMPLIANCE**

### **API Key Security:**

```python
# security/api_key_manager.py
import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from pathlib import Path

class SecureAPIKeyManager:
    """Secure management of API keys"""
    
    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path("C:/MineExtractorWeb/config")
        self.config_dir.mkdir(exist_ok=True)
        
        self.key_file = self.config_dir / "api_keys.encrypted"
        self.salt_file = self.config_dir / "key.salt"
        
        self.cipher_suite = None
        self.master_password = None
    
    def initialize_encryption(self, master_password: str):
        """Initialize encryption with master password"""
        
        self.master_password = master_password
        
        # Generate or load salt
        if self.salt_file.exists():
            salt = self.salt_file.read_bytes()
        else:
            salt = os.urandom(32)
            self.salt_file.write_bytes(salt)
            # Secure the salt file
            os.chmod(self.salt_file, 0o600)
        
        # Derive key from password
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_password.encode()))
        
        self.cipher_suite = Fernet(key)
    
    def store_api_keys(self, api_keys: dict):
        """Store API keys encrypted"""
        
        if not self.cipher_suite:
            raise ValueError("Encryption not initialized")
        
        # Encrypt the API keys
        keys_json = json.dumps(api_keys)
        encrypted_keys = self.cipher_suite.encrypt(keys_json.encode())
        
        # Store encrypted keys
        self.key_file.write_bytes(encrypted_keys)
        
        # Secure the key file
        os.chmod(self.key_file, 0o600)
    
    def load_api_keys(self) -> dict:
        """Load and decrypt API keys"""
        
        if not self.cipher_suite:
            raise ValueError("Encryption not initialized")
        
        if not self.key_file.exists():
            return {}
        
        # Load and decrypt keys
        encrypted_keys = self.key_file.read_bytes()
        decrypted_keys = self.cipher_suite.decrypt(encrypted_keys)
        
        return json.loads(decrypted_keys.decode())
    
    def update_api_key(self, key_name: str, key_value: str):
        """Update a specific API key"""
        
        # Load existing keys
        keys = self.load_api_keys()
        
        # Update specific key
        keys[key_name] = key_value
        
        # Store updated keys
        self.store_api_keys(keys)
    
    def get_api_key(self, key_name: str) -> str:
        """Get a specific API key"""
        
        keys = self.load_api_keys()
        return keys.get(key_name)
    
    def validate_master_password(self, password: str) -> bool:
        """Validate master password by trying to decrypt"""
        
        try:
            temp_manager = SecureAPIKeyManager(self.config_dir)
            temp_manager.initialize_encryption(password)
            temp_manager.load_api_keys()
            return True
        except Exception:
            return False

# Setup script for secure API key storage
def setup_secure_api_keys():
    """Interactive setup for secure API key storage"""
    
    print("🔐 MineExtractorWeb - Secure API Key Setup")
    print("=" * 50)
    
    manager = SecureAPIKeyManager()
    
    # Get master password
    import getpass
    master_password = getpass.getpass("Enter master password for API key encryption: ")
    confirm_password = getpass.getpass("Confirm master password: ")
    
    if master_password != confirm_password:
        print("❌ Passwords don't match!")
        return False
    
    # Initialize encryption
    manager.initialize_encryption(master_password)
    
    # Collect API keys
    api_keys = {}
    
    print("\nEnter your API keys (press Enter to skip):")
    
    key_prompts = {
        'perplexity_api_key': 'Perplexity AI API Key',
        'deepseek_api_key': 'DeepSeek API Key',
        'gemini_api_key': 'Gemini API Key',
        'tavily_api_key': 'Tavily AI API Key',
        'exa_api_key': 'Exa.ai API Key',
        'searchapi_key': 'SearchAPI Key'
    }
    
    for key_name, prompt in key_prompts.items():
        key_value = getpass.getpass(f"{prompt}: ")
        if key_value.strip():
            api_keys[key_name] = key_value.strip()
    
    # Store encrypted keys
    if api_keys:
        manager.store_api_keys(api_keys)
        print(f"\n✅ {len(api_keys)} API keys stored securely")
    else:
        print("\n⚠️ No API keys provided")
    
    print("\n🔒 API keys are encrypted and stored securely")
    print("Remember your master password - it cannot be recovered!")
    
    return True

if __name__ == "__main__":
    setup_secure_api_keys()
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Production Performance Configuration:**

```python
# config/performance_config.py
import multiprocessing
import psutil
from pathlib import Path

class PerformanceOptimizer:
    """Optimizes system performance for production use"""
    
    def __init__(self):
        self.system_info = self.get_system_info()
        self.optimal_config = self.calculate_optimal_config()
    
    def get_system_info(self) -> dict:
        """Get system information for optimization"""
        
        return {
            'cpu_count': multiprocessing.cpu_count(),
            'memory_gb': round(psutil.virtual_memory().total / (1024**3)),
            'cpu_freq_mhz': psutil.cpu_freq().max if psutil.cpu_freq() else 2400,
            'disk_type': 'ssd'  # Assume SSD for modern systems
        }
    
    def calculate_optimal_config(self) -> dict:
        """Calculate optimal configuration based on system specs"""
        
        cpu_count = self.system_info['cpu_count']
        memory_gb = self.system_info['memory_gb']
        
        # Calculate optimal concurrent requests
        if memory_gb >= 32:
            max_concurrent = min(cpu_count * 2, 20)
        elif memory_gb >= 16:
            max_concurrent = min(cpu_count, 10)
        else:
            max_concurrent = min(cpu_count // 2, 5)
        
        # Calculate optimal batch size
        if memory_gb >= 32:
            batch_size = 20
        elif memory_gb >= 16:
            batch_size = 10
        else:
            batch_size = 5
        
        # Calculate optimal timeout based on CPU speed
        base_timeout = 120
        if self.system_info['cpu_freq_mhz'] < 2000:
            timeout_multiplier = 1.5
        elif self.system_info['cpu_freq_mhz'] > 3000:
            timeout_multiplier = 0.8
        else:
            timeout_multiplier = 1.0
        
        optimal_timeout = int(base_timeout * timeout_multiplier)
        
        return {
            'max_concurrent_requests': max_concurrent,
            'batch_size': batch_size,
            'request_timeout': optimal_timeout,
            'enable_caching': True,
            'cache_size_mb': min(memory_gb * 32, 512),  # 32MB per GB, max 512MB
            'worker_threads': min(cpu_count, 8),
            'connection_pool_size': max_concurrent * 2
        }
    
    def generate_performance_config(self) -> str:
        """Generate performance configuration file"""
        
        config_content = f"""# MineExtractorWeb Performance Configuration
# Auto-generated based on system specs
# System: {self.system_info['cpu_count']} CPU cores, {self.system_info['memory_gb']}GB RAM

# Concurrency Settings
MAX_CONCURRENT_REQUESTS={self.optimal_config['max_concurrent_requests']}
BATCH_SIZE={self.optimal_config['batch_size']}
WORKER_THREADS={self.optimal_config['worker_threads']}

# Timeout Settings
REQUEST_TIMEOUT={self.optimal_config['request_timeout']}
CONNECTION_TIMEOUT=30
READ_TIMEOUT=60

# Caching Settings
ENABLE_CACHING={str(self.optimal_config['enable_caching']).lower()}
CACHE_SIZE_MB={self.optimal_config['cache_size_mb']}
CACHE_DURATION_HOURS=24

# Connection Pool Settings
CONNECTION_POOL_SIZE={self.optimal_config['connection_pool_size']}
CONNECTION_POOL_MAX_SIZE={self.optimal_config['connection_pool_size'] * 2}

# Memory Management
MEMORY_LIMIT_MB={self.system_info['memory_gb'] * 1024 // 2}  # Use max 50% of system memory
GARBAGE_COLLECTION_THRESHOLD=1000

# Performance Monitoring
ENABLE_PERFORMANCE_LOGGING=true
PERFORMANCE_LOG_INTERVAL=300  # 5 minutes
ENABLE_METRICS_COLLECTION=true
"""
        
        return config_content
    
    def apply_performance_optimizations(self):
        """Apply system-level performance optimizations"""
        
        import gc
        import sys
        
        # Python-specific optimizations
        gc.set_threshold(700, 10, 10)  # Optimize garbage collection
        
        # Set recursion limit based on available memory
        if self.system_info['memory_gb'] >= 16:
            sys.setrecursionlimit(3000)
        else:
            sys.setrecursionlimit(1500)
        
        # Configure asyncio settings if available
        try:
            import asyncio
            
            # Set optimal event loop policy for Windows
            if sys.platform.startswith('win'):
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
                
        except ImportError:
            pass
        
        print(f"✅ Performance optimizations applied")
        print(f"   - Max concurrent requests: {self.optimal_config['max_concurrent_requests']}")
        print(f"   - Batch size: {self.optimal_config['batch_size']}")
        print(f"   - Request timeout: {self.optimal_config['request_timeout']}s")
        print(f"   - Cache size: {self.optimal_config['cache_size_mb']}MB")

# Performance optimization script
if __name__ == "__main__":
    optimizer = PerformanceOptimizer()
    
    print("🚀 MineExtractorWeb Performance Optimization")
    print("=" * 50)
    print(f"Detected system: {optimizer.system_info}")
    print("\nOptimal configuration:")
    for key, value in optimizer.optimal_config.items():
        print(f"  {key}: {value}")
    
    # Generate and save configuration
    config_content = optimizer.generate_performance_config()
    config_file = Path("C:/MineExtractorWeb/config/performance.env")
    config_file.write_text(config_content, encoding='utf-8')
    
    print(f"\n✅ Performance configuration saved to: {config_file}")
    
    # Apply optimizations
    optimizer.apply_performance_optimizations()
```

---

**Status:** ✅ Production Deployment Guide Complete  
**Deployment Timeline:** 1-2 Tage für Phase 1, 1 Woche für vollständiges Production Setup  
**Security Level:** Enterprise-grade mit verschlüsselten API Keys und umfassendem Monitoring  

*Dieser Deployment Guide ermöglicht ein robustes, skalierbares und sicheres Production Setup des MineExtractorWeb Systems.*