# API Integration Guide - MineExtractorWeb v1.0
**Komplette Anleitung für alle Research-APIs und Scraping-Services**

---

## 🚀 **QUICK START - EMPFOHLENE REIHENFOLGE**

### **Sofortiger Start (Heute):**
1. **Perplexity AI Pro** - Primary Deep Research
2. **Tavily AI** - Government & Regulatory Data

### **Woche 2 Erweiterung:**
3. **Exa.ai** - Semantic Search für schwer findbare Daten
4. **SearchAPI** - Comprehensive Web Search

### **Full Production (Woche 3-4):**
5. **Apify** - Government Database Scraping
6. **ScrapingBee** - JavaScript-Heavy Sites
7. **FireCrawl** - Modern SPA Applications

---

## 🔑 **TIER 1 APIs - PRIMARY RESEARCH**

### **1. Perplexity AI Pro Setup**

#### **Account Erstellung:**
```bash
# 1. <PERSON><PERSON><PERSON> zu https://www.perplexity.ai/pro
# 2. Upgrade zu Pro Account ($20/Monat)
# 3. API-Zugang unter Settings → API
# 4. API Key generieren und sicher speichern
```

#### **API Konfiguration:**
```python
# config/api_keys.py
PERPLEXITY_CONFIG = {
    'api_key': 'pplx-xxxxxxxxxxxxxxxxxxxxx',  # Dein API Key
    'base_url': 'https://api.perplexity.ai/chat/completions',
    'model': 'llama-3.1-sonar-large-128k-online',
    'max_tokens': 2000,
    'temperature': 0.1,
    'search_recency_filter': 'month',
    'search_domain_filter': ['gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca'],
    'return_citations': True
}

# Rate Limits (Pro Plan):
PERPLEXITY_LIMITS = {
    'requests_per_hour': 600,
    'requests_per_day': 6000,
    'concurrent_requests': 5
}
```

#### **Implementation:**
```python
class PerplexityResearcher:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        
    async def research_mine_comprehensive(self, mine_name):
        """Umfassende Minen-Recherche mit Perplexity"""
        
        prompt = f"""Research comprehensive data about {mine_name} mine in Quebec, Canada.

REQUIRED DATA POINTS:
1. OPERATOR: Current owner/operator company (exact legal name)
2. STATUS: Operating status (active/closed/planned/suspended) with year
3. RESTORATION COSTS: Environmental restoration/closure costs in CAD
4. MINE TYPE: Operation type (open-pit/underground/surface mining)
5. COMMODITY: Primary material (gold/copper/nickel/iron ore/lithium/etc.)
6. PRODUCTION: Start/end dates, annual production volumes
7. AREA: Mine site area in hectares or km²
8. COORDINATES: GPS coordinates if available

PRIORITY SOURCES (search these first):
- Quebec MERN: mrnf.gouv.qc.ca, gestim.mines.gouv.qc.ca
- SEDAR+: sedarplus.ca (company filings)
- Company websites and technical reports
- Environmental assessments and permits
- TSX/SEC regulatory filings

FORMAT: Provide specific values with source URLs and dates.
FOCUS: Government and official company sources only."""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "llama-3.1-sonar-large-128k-online",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 2000,
            "return_citations": True,
            "search_domain_filter": ["gov.ca", "sedarplus.ca", "mrnf.gouv.qc.ca"],
            "search_recency_filter": "month"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(self.base_url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return self._parse_perplexity_response(result)
                else:
                    raise Exception(f"Perplexity API error: {response.status}")
```

#### **Test Command:**
```python
# Test Perplexity Integration
async def test_perplexity():
    researcher = PerplexityResearcher("your-api-key")
    result = await researcher.research_mine_comprehensive("Éléonore")
    print(json.dumps(result, indent=2))

# Expected Output:
{
  "operator": "Newmont Corporation",
  "status": "active",
  "restoration_costs": "********",
  "mine_type": "Underground",
  "commodity": "Gold",
  "sources": ["https://mrnf.gouv.qc.ca/...", "https://sedarplus.ca/..."]
}
```

---

### **2. Tavily AI Setup**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://tavily.com/
# 2. Sign up für Developer Account
# 3. Basic Plan ($20/Monat) oder höher
# 4. API Key unter Dashboard generieren
```

#### **API Konfiguration:**
```python
# config/api_keys.py
TAVILY_CONFIG = {
    'api_key': 'tvly-xxxxxxxxxxxxxxxxxxxxx',  # Dein API Key
    'base_url': 'https://api.tavily.com/search',
    'search_depth': 'advanced',
    'max_results': 15,
    'include_answer': True,
    'include_raw_content': True,
    'include_domains': [
        'gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca', 'tsx.com',
        'sec.gov', 'newswire.ca', 'mining.com', 'miningweekly.com'
    ]
}

# Rate Limits (Basic Plan):
TAVILY_LIMITS = {
    'requests_per_month': 1000,
    'requests_per_minute': 10
}
```

#### **Implementation:**
```python
class TavilyResearcher:
    def __init__(self, api_key):
        self.api_key = api_key
        
    async def search_financial_data(self, mine_name):
        """Sucht spezifisch nach Finanzdaten"""
        
        query = f'{mine_name} mine Quebec restoration closure costs CAD financial report environmental bond'
        
        payload = {
            "api_key": self.api_key,
            "query": query,
            "search_depth": "advanced",
            "include_domains": ["gov.ca", "sedarplus.ca", "mrnf.gouv.qc.ca"],
            "max_results": 10,
            "include_answer": True,
            "include_raw_content": True
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post("https://api.tavily.com/search", json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return self._extract_financial_data(result)
                else:
                    raise Exception(f"Tavily API error: {response.status}")
    
    async def search_regulatory_data(self, mine_name):
        """Sucht nach regulatorischen Daten"""
        
        query = f'{mine_name} mine Quebec permit status operator license MERN environmental assessment'
        
        # Ähnliche Implementation wie search_financial_data
        # Fokus auf Permits, Status, Operator
        pass
```

---

### **3. SearchAPI Setup (Alternative zu Google Search)**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://www.searchapi.io/
# 2. Free Trial dann Starter Plan ($29/Monat)
# 3. API Key unter Dashboard
```

#### **Implementation:**
```python
class SearchAPIResearcher:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://www.searchapi.io/api/v1/search"
        
    async def search_comprehensive(self, mine_name):
        """Comprehensive Google Search für Mining Data"""
        
        search_queries = [
            f'"{mine_name}" mine Quebec operator restoration costs',
            f'"{mine_name}" mining Quebec MERN permit status',
            f'"{mine_name}" mine technical report production',
            f'"{mine_name}" Quebec mine environmental assessment'
        ]
        
        all_results = []
        for query in search_queries:
            params = {
                'engine': 'google',
                'q': query,
                'location': 'Canada',
                'google_domain': 'google.ca',
                'num': 10,
                'api_key': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.base_url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        all_results.extend(result.get('organic_results', []))
        
        return self._process_search_results(all_results)
```

---

## 🔍 **TIER 2 APIs - SPECIALIZED SEARCH**

### **4. Exa.ai Setup**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://exa.ai/
# 2. Sign up für Developer Account  
# 3. Starter Plan ($29/Monat)
# 4. API Key generieren
```

#### **Implementation:**
```python
class ExaResearcher:
    def __init__(self, api_key):
        self.api_key = api_key
        
    async def semantic_mining_search(self, mine_name):
        """Semantische Suche für Mining-spezifische Daten"""
        
        headers = {
            'x-api-key': self.api_key,
            'Content-Type': 'application/json'
        }
        
        # Semantic search für technical data
        payload = {
            'query': f'technical specifications and restoration costs for {mine_name} mine Quebec',
            'type': 'neural',
            'use_autoprompt': True,
            'num_results': 10,
            'include_domains': [
                'mining.com', 'miningweekly.com', 'northernminer.com',
                'kitco.com', 's2analytics.com', 'infomine.com'
            ],
            'contents': {
                'text': True,
                'highlights': True,
                'summary': True
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post('https://api.exa.ai/search', 
                                   headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return self._extract_technical_data(result)
```

---

## 🤖 **TIER 3 - MANAGED SCRAPING SERVICES**

### **5. Apify Setup - Government Database Scraping**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://apify.com/
# 2. Sign up für Team Plan ($49/Monat)
# 3. Browse Actors für Mining-relevante Scrapers
# 4. API Token unter Settings
```

#### **Pre-built Mining Scrapers:**
```python
class ApifyMiningScrapers:
    def __init__(self, api_token):
        self.api_token = api_token
        self.base_url = "https://api.apify.com/v2"
        
    # Quebec MERN Database Scraper
    async def scrape_quebec_mern(self, mine_name):
        """Scrapes Quebec Government Mining Database"""
        
        actor_id = "your-custom-quebec-mern-scraper"  # Create custom actor
        
        input_data = {
            "searchTerm": mine_name,
            "includeInactive": True,
            "extractFinancial": True,
            "extractTechnical": True
        }
        
        # Run Apify Actor
        url = f"{self.base_url}/acts/{actor_id}/runs"
        headers = {"Authorization": f"Bearer {self.api_token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=input_data) as response:
                run_info = await response.json()
                run_id = run_info['data']['id']
                
                # Wait for completion and get results
                results_url = f"{self.base_url}/acts/{actor_id}/runs/{run_id}/dataset/items"
                
                # Poll until complete
                while True:
                    async with session.get(results_url, headers=headers) as result_response:
                        if result_response.status == 200:
                            return await result_response.json()
                    await asyncio.sleep(5)
    
    # SEDAR+ Financial Filings Scraper
    async def scrape_sedar_plus(self, company_name):
        """Scrapes Canadian Company Financial Filings"""
        
        actor_id = "sedar-plus-financial-scraper"
        
        input_data = {
            "companyName": company_name,
            "documentTypes": ["Annual Report", "Technical Report", "Material Change"],
            "maxResults": 20
        }
        
        # Similar implementation as quebec_mern
        pass
```

### **6. ScrapingBee Setup - JavaScript Sites**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://scrapingbee.com/
# 2. Freelancer Plan ($29/Monat) oder höher
# 3. API Key unter Dashboard
```

#### **Implementation:**
```python
class ScrapingBeeService:
    def __init__(self, api_key):
        self.api_key = api_key
        
    async def scrape_mining_website(self, url, mine_name):
        """Scrapes mining company websites with JS rendering"""
        
        params = {
            'api_key': self.api_key,
            'url': url,
            'render_js': 'true',
            'premium_proxy': 'true',
            'country_code': 'ca',
            'wait': 3000,
            'extract_rules': {
                'operator': {'selector': '.company-name, .operator'},
                'status': {'selector': '.mine-status, .operational-status'},
                'production': {'selector': '.production-data, .annual-production'},
                'costs': {'selector': '.restoration-cost, .closure-cost'}
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get('https://app.scrapingbee.com/api/v1/', 
                                  params=params) as response:
                if response.status == 200:
                    return await response.json()
```

### **7. FireCrawl Setup - Modern SPAs**

#### **Account Erstellung:**
```bash
# 1. Gehe zu https://firecrawl.dev/
# 2. Starter Plan ($29/Monat)
# 3. API Key generieren
```

#### **Implementation:**
```python
class FireCrawlService:
    def __init__(self, api_key):
        self.api_key = api_key
        
    async def crawl_mining_database(self, base_url, mine_name):
        """Crawls modern mining database SPAs"""
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        crawl_data = {
            'url': base_url,
            'crawlerOptions': {
                'includes': [f'*{mine_name.lower()}*'],
                'excludes': ['*/admin/*', '*/login/*'],
                'maxDepth': 2
            },
            'pageOptions': {
                'includeHtml': False,
                'includeRawHtml': False,
                'extractorOptions': {
                    'mode': 'llm-extraction',
                    'extractionPrompt': f'Extract mining data for {mine_name}: operator, costs, status, production'
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post('https://api.firecrawl.dev/v0/crawl', 
                                   headers=headers, json=crawl_data) as response:
                result = await response.json()
                job_id = result['jobId']
                
                # Poll for completion
                return await self._poll_crawl_results(job_id)
```

---

## 🔧 **FALLBACK SYSTEMS**

### **8. Custom Selenium Scraper**

```python
class CustomSeleniumScraper:
    def __init__(self):
        self.options = webdriver.ChromeOptions()
        self.options.add_argument('--headless')
        self.options.add_argument('--no-sandbox')
        
    async def scrape_government_site(self, mine_name):
        """Custom scraping für spezielle Government Sites"""
        
        driver = webdriver.Chrome(options=self.options)
        
        try:
            # Quebec MERN Site
            driver.get("https://gestim.mines.gouv.qc.ca/MRN_GestimP_Presentation/")
            
            # Search for mine
            search_box = driver.find_element(By.ID, "search-input")
            search_box.send_keys(mine_name)
            search_box.send_keys(Keys.RETURN)
            
            # Wait and extract results
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "result-item"))
            )
            
            results = driver.find_elements(By.CLASS_NAME, "result-item")
            extracted_data = []
            
            for result in results:
                data = {
                    'name': result.find_element(By.CLASS_NAME, "mine-name").text,
                    'operator': result.find_element(By.CLASS_NAME, "operator").text,
                    'status': result.find_element(By.CLASS_NAME, "status").text
                }
                extracted_data.append(data)
            
            return extracted_data
            
        finally:
            driver.quit()
```

---

## 📊 **API COST OPTIMIZATION**

### **Cost-Effective API Usage Strategy:**

```python
class APICostOptimizer:
    """
    Optimiert API-Nutzung für minimale Kosten bei maximaler Datenqualität
    """
    
    def __init__(self):
        self.api_costs = {
            'perplexity': {'cost_per_request': 0.005, 'quality_score': 0.95},
            'tavily': {'cost_per_request': 0.02, 'quality_score': 0.85},
            'exa': {'cost_per_request': 0.03, 'quality_score': 0.80},
            'searchapi': {'cost_per_request': 0.001, 'quality_score': 0.70}
        }
    
    def select_optimal_apis(self, data_requirements, budget_limit):
        """
        Wählt optimale API-Kombination basierend auf Budget und Anforderungen
        """
        if budget_limit >= 0.05:  # High budget
            return ['perplexity', 'tavily', 'exa']
        elif budget_limit >= 0.025:  # Medium budget
            return ['perplexity', 'tavily']
        else:  # Low budget
            return ['perplexity', 'searchapi']
    
    async def execute_cost_optimized_research(self, mine_name, budget_per_mine=0.05):
        """
        Führt kostenoptimierte Recherche durch
        """
        selected_apis = self.select_optimal_apis(['comprehensive'], budget_per_mine)
        
        results = []
        total_cost = 0
        
        for api_name in selected_apis:
            if total_cost >= budget_per_mine:
                break
                
            result = await self._call_api(api_name, mine_name)
            results.append(result)
            total_cost += self.api_costs[api_name]['cost_per_request']
        
        return results, total_cost
```

---

## ⚙️ **KONFIGURATION & SETUP**

### **Environment Variables Setup:**

```bash
# .env file
# Tier 1 APIs
PERPLEXITY_API_KEY=pplx-xxxxxxxxxxxxxxxxxxxxx
TAVILY_API_KEY=tvly-xxxxxxxxxxxxxxxxxxxxx
SEARCHAPI_KEY=xxxxxxxxxxxxxxxxxxxxx

# Tier 2 APIs  
EXA_API_KEY=xxxxxxxxxxxxxxxxxxxxx
SERPER_API_KEY=xxxxxxxxxxxxxxxxxxxxx
BRAVE_API_KEY=xxxxxxxxxxxxxxxxxxxxx

# Tier 3 Scraping Services
APIFY_API_TOKEN=apify_api_xxxxxxxxxxxxxxxxxxxxx
SCRAPINGBEE_API_KEY=xxxxxxxxxxxxxxxxxxxxx
FIRECRAWL_API_KEY=fc-xxxxxxxxxxxxxxxxxxxxx

# Optional Services
PROXYCRAWL_API_KEY=xxxxxxxxxxxxxxxxxxxxx
ZENROWS_API_KEY=xxxxxxxxxxxxxxxxxxxxx
```

### **Unified Configuration Manager:**

```python
class APIConfigManager:
    def __init__(self):
        load_dotenv()
        
        self.configs = {
            'perplexity': {
                'api_key': os.getenv('PERPLEXITY_API_KEY'),
                'enabled': bool(os.getenv('PERPLEXITY_API_KEY')),
                'priority': 1,
                'cost_per_request': 0.005
            },
            'tavily': {
                'api_key': os.getenv('TAVILY_API_KEY'),
                'enabled': bool(os.getenv('TAVILY_API_KEY')),
                'priority': 2,
                'cost_per_request': 0.02
            },
            'exa': {
                'api_key': os.getenv('EXA_API_KEY'),
                'enabled': bool(os.getenv('EXA_API_KEY')),
                'priority': 3,
                'cost_per_request': 0.03
            }
        }
    
    def get_enabled_apis(self):
        """Gibt Liste der verfügbaren APIs zurück"""
        return [name for name, config in self.configs.items() if config['enabled']]
    
    def get_api_config(self, api_name):
        """Gibt Konfiguration für spezifische API zurück"""
        return self.configs.get(api_name, {})
```

---

## 🧪 **TESTING & VALIDATION**

### **API Testing Suite:**

```python
class APITestSuite:
    """
    Comprehensive Testing für alle integrierten APIs
    """
    
    async def test_all_apis(self):
        """Testet alle verfügbaren APIs mit Quebec Test-Minen"""
        
        test_mines = ['Éléonore', 'Canadian Malartic', 'Raglan', 'Casa Berardi']
        
        for mine_name in test_mines:
            print(f"\nTesting APIs for: {mine_name}")
            
            # Test Perplexity
            try:
                result = await self.test_perplexity(mine_name)
                print(f"✅ Perplexity: {len(result.get('data', {}))} fields found")
            except Exception as e:
                print(f"❌ Perplexity: {e}")
            
            # Test Tavily
            try:
                result = await self.test_tavily(mine_name)
                print(f"✅ Tavily: {len(result.get('results', []))} sources found")
            except Exception as e:
                print(f"❌ Tavily: {e}")
            
            # Test weitere APIs...
    
    async def validate_data_quality(self, research_results):
        """Validiert Qualität der API-Ergebnisse"""
        
        quality_metrics = {
            'completeness': 0,
            'accuracy': 0,
            'freshness': 0,
            'source_reliability': 0
        }
        
        # Completeness: Wie viele Felder wurden gefüllt?
        total_fields = 16  # Anzahl Zielfelder im CSV
        filled_fields = sum(1 for v in research_results.values() if v and v.strip())
        quality_metrics['completeness'] = filled_fields / total_fields
        
        # Source Reliability: Bewertung der Quellen
        government_sources = sum(1 for url in research_results.get('sources', []) 
                               if '.gov.' in url)
        total_sources = len(research_results.get('sources', []))
        if total_sources > 0:
            quality_metrics['source_reliability'] = government_sources / total_sources
        
        return quality_metrics
```

---

## 📈 **MONITORING & ANALYTICS**

### **Real-time API Performance Monitoring:**

```python
class APIPerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'response_times': defaultdict(list),
            'success_rates': defaultdict(float),
            'cost_tracking': defaultdict(float),
            'data_quality': defaultdict(list)
        }
    
    async def monitor_api_call(self, api_name, research_func, *args):
        """Überwacht API-Aufrufe mit Performance-Metriken"""
        
        start_time = time.time()
        
        try:
            result = await research_func(*args)
            end_time = time.time()
            
            # Track successful call
            response_time = end_time - start_time
            self.metrics['response_times'][api_name].append(response_time)
            self._update_success_rate(api_name, True)
            
            # Track costs
            cost = self._calculate_api_cost(api_name, result)
            self.metrics['cost_tracking'][api_name] += cost
            
            return result
            
        except Exception as e:
            end_time = time.time()
            self._update_success_rate(api_name, False)
            raise e
    
    def get_performance_report(self):
        """Generiert Performance-Report für alle APIs"""
        
        report = {}
        for api_name in self.metrics['response_times']:
            response_times = self.metrics['response_times'][api_name]
            
            report[api_name] = {
                'avg_response_time': sum(response_times) / len(response_times),
                'success_rate': self.metrics['success_rates'][api_name],
                'total_cost': self.metrics['cost_tracking'][api_name],
                'total_calls': len(response_times)
            }
        
        return report
```

---

**Status:** ✅ API Integration Guide Complete  
**Nächster Schritt:** Implementation Phase 1 mit Perplexity + Tavily  
**Setup Zeit:** 1 Tag für Tier 1 APIs, 1 Woche für vollständiges System  

*Dieser Guide ermöglicht schrittweise Integration aller APIs für maximale Datenabdeckung bei optimalen Kosten.*