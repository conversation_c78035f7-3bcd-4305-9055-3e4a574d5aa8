"""
Web Mining Researcher - Hauptklasse für MineExtractorWeb v1.0
Orchestriert Multi-System Web Research für Quebec Mining Data
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

try:
    from .mine_data_models import MineDataFields, MineResearchResult, BatchResearchResult, SourceMetadata
    from .data_parser import MiningDataParser
    from .perplexity_client import PerplexityClient
    from .tavily_client import TavilyClient
except ImportError:
    from mine_data_models import MineDataFields, MineResearchResult, BatchResearchResult, SourceMetadata
    from data_parser import MiningDataParser
    from perplexity_client import PerplexityClient
    from tavily_client import TavilyClient

logger = logging.getLogger(__name__)

class WebMiningResearcher:
    """
    Hauptklasse für Web-basierte Mining-Daten-Recherche
    Phase 1: Fokus auf Perplexity AI Integration
    Phase 2+: Multi-API Integration (Tavily, Exa, etc.)
    """
    
    def __init__(self, config: Dict[str, str]):
        """
        Initialisiert Web Mining Researcher
        
        Args:
            config: API-Konfiguration {'perplexity_key': 'key', 'tavily_key': 'key', ...}
        """
        self.config = config
        self.parser = MiningDataParser()
        
        # Load settings
        try:
            from config.settings import ProjectSettings
            self.settings = ProjectSettings
        except ImportError:
            self.settings = None
        
        # Initialize API clients
        self.api_clients = {}
        self._setup_api_clients()
        
        # Research statistics
        self.research_stats = {
            'total_researched': 0,
            'successful_researches': 0,
            'failed_researches': 0,
            'total_duration': 0.0,
            'api_usage': {},
            'error_log': []
        }
        
        # Performance settings
        self.batch_size = 5  # Mines per batch for rate limiting
        self.request_delay = 2  # Seconds between requests
        self.max_retries = 3
        
        logger.info(f"WebMiningResearcher initialized with {len(self.api_clients)} API clients")
    
    def _setup_api_clients(self):
        """Initialisiert verfügbare API-Clients basierend auf Konfiguration"""
        
        # Perplexity AI (Primary)
        if 'perplexity_key' in self.config and self.config['perplexity_key']:
            try:
                self.api_clients['perplexity'] = {
                    'client_class': PerplexityClient,
                    'key': self.config['perplexity_key'],
                    'priority': 1,
                    'enabled': True
                }
                logger.info("✅ Perplexity AI client configured")
            except Exception as e:
                logger.warning(f"❌ Perplexity AI setup failed: {e}")
        
        # Tavily AI (Phase 2 Implementation)
        if 'tavily_key' in self.config and self.config['tavily_key']:
            try:
                self.api_clients['tavily'] = {
                    'client_class': TavilyClient,
                    'key': self.config['tavily_key'],
                    'priority': 2,
                    'enabled': True  # Now implemented
                }
                logger.info("✅ Tavily AI client configured")
            except Exception as e:
                logger.warning(f"❌ Tavily AI setup failed: {e}")
        
        # Exa.ai (Future implementation)
        if 'exa_key' in self.config and self.config['exa_key']:
            self.api_clients['exa'] = {
                'client_class': None,  # To be implemented in Phase 2
                'key': self.config['exa_key'],
                'priority': 3,
                'enabled': False  # Not implemented yet
            }
            logger.info("🔄 Exa.ai configured for future implementation")
        
        if not self.api_clients:
            raise ValueError("No API clients configured. Please provide at least 'perplexity_key'.")
    
    async def research_mine_comprehensive(self, mine_name: str, focus_areas: List[str] = None, timeout: int = 120) -> MineResearchResult:
        """
        Führt umfassende Recherche für eine einzelne Mine durch (mit Namensvarianten)
        """
        start_time = time.time()
        logger.info(f"🔍 Starting comprehensive research for: {mine_name}")
        # Namensvarianten generieren
        parser = MiningDataParser()
        variants = set()
        name_orig, name_no_accents = parser._normalize_mine_name(mine_name)
        variants.add(name_orig)
        variants.add(name_no_accents)
        # Varianten mit und ohne 'Mine' am Anfang
        for n in [name_orig, name_no_accents]:
            if not n.lower().startswith('mine '):
                variants.add('Mine ' + n)
            if n.lower().startswith('mine '):
                variants.add(n[5:])
        # Für jede Variante recherchieren und Ergebnisse mergen
        best_result = None
        best_score = -1
        for variant in variants:
            logger.info(f"🔍 Suche für Namensvariante: {variant}")
            result = await self._research_single_variant(variant, focus_areas, timeout)
            if result and result.data_completeness > best_score:
                best_result = result
                best_score = result.data_completeness
        return best_result if best_result else MineResearchResult()

    async def _research_single_variant(self, mine_name: str, focus_areas: List[str], timeout: int) -> MineResearchResult:
        # Original-Logik für eine Variante
        start_time = time.time()
        result = MineResearchResult()
        result.mine_data.name = mine_name
        result.mine_data.id = MineDataFields.generate_id(mine_name)
        
        try:
            # Phase 1: Primary research with best available API
            primary_result = await self._execute_primary_research(mine_name, focus_areas, timeout)
            
            if primary_result['success']:
                # Parse primary results
                mine_data = self.parser.parse_api_response(
                    primary_result, mine_name, primary_result.get('api_source', 'perplexity')
                )
                
                result.mine_data = mine_data
                result.apis_used.append(primary_result.get('api_source', 'unknown'))
                result.confidence_score = primary_result.get('confidence', 0.7)
                
                # Add sources from primary research
                self._add_sources_to_result(result, primary_result)
                
                logger.info(f"✅ Primary research successful for {mine_name}")
                
                # Phase 2: Supplementary research if needed (future implementation)
                if result.data_completeness < 0.8:
                    logger.info(f"🔄 Data completeness {result.data_completeness:.1%} - considering supplementary research")
                    # Future: Run additional APIs for missing data
                
            else:
                # Primary research failed
                result.add_error(f"Primary research failed: {primary_result.get('error', 'Unknown error')}")
                logger.error(f"❌ Primary research failed for {mine_name}")
            
            # Calculate final metrics
            result.research_duration = time.time() - start_time
            
            # Update statistics
            self._update_research_stats(result)
            
            logger.info(f"🏁 Research completed for {mine_name} in {result.research_duration:.1f}s "
                       f"(completion: {result.data_completeness:.1%}, confidence: {result.confidence_score:.1%})")
            
            return result
            
        except asyncio.TimeoutError:
            result.add_error(f"Research timeout after {timeout}s")
            result.research_duration = time.time() - start_time
            logger.error(f"⏰ Research timeout for {mine_name}")
            return result
            
        except Exception as e:
            result.add_error(f"Unexpected error: {str(e)}")
            result.research_duration = time.time() - start_time
            logger.error(f"💥 Unexpected error for {mine_name}: {e}")
            return result
    
    async def _execute_primary_research(self, mine_name: str, focus_areas: List[str], 
                                      timeout: int) -> Dict[str, Any]:
        """
        Führt primäre Recherche mit dem besten verfügbaren API durch
        
        Args:
            mine_name: Name der Mine
            focus_areas: Fokus-Bereiche
            timeout: Timeout
            
        Returns:
            Research-Ergebnis Dictionary
        """
        
        # Get primary API (highest priority enabled API)
        primary_api = self._get_primary_api()
        
        if not primary_api:
            return {
                'success': False,
                'error': 'No primary API available',
                'api_source': 'none'
            }
        
        api_name = primary_api['name']
        
        try:
            if api_name == 'perplexity':
                return await self._research_with_perplexity(mine_name, focus_areas, timeout)
            
            elif api_name == 'tavily':
                # Future implementation
                return {
                    'success': False,
                    'error': 'Tavily API not implemented yet',
                    'api_source': 'tavily'
                }
            
            elif api_name == 'exa':
                # Future implementation
                return {
                    'success': False,
                    'error': 'Exa API not implemented yet',
                    'api_source': 'exa'
                }
            
            else:
                return {
                    'success': False,
                    'error': f'Unknown API: {api_name}',
                    'api_source': api_name
                }
                
        except Exception as e:
            logger.error(f"Primary research error with {api_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'api_source': api_name
            }
    
    async def _research_with_perplexity(self, mine_name: str, focus_areas: List[str], 
                                      timeout: int) -> Dict[str, Any]:
        """
        Führt Recherche mit Perplexity AI durch
        
        Args:
            mine_name: Name der Mine
            focus_areas: Fokus-Bereiche
            timeout: Timeout
            
        Returns:
            Perplexity research results
        """
        
        perplexity_config = self.api_clients['perplexity']
        
        try:
            async with PerplexityClient(perplexity_config['key']) as client:
                
                # Execute research with timeout
                research_task = client.research_mine_comprehensive(mine_name)
                api_result = await asyncio.wait_for(research_task, timeout=timeout)
                
                if api_result['success']:
                    # Enhance result with metadata
                    enhanced_result = api_result.copy()
                    enhanced_result['api_source'] = 'perplexity'
                    enhanced_result['focus_areas'] = focus_areas or []
                    enhanced_result['confidence'] = self._calculate_confidence_score(api_result)
                    
                    # Update API usage stats
                    self._update_api_stats('perplexity', True, api_result.get('response_time', 0))
                    
                    return enhanced_result
                
                else:
                    # API call successful but no data
                    self._update_api_stats('perplexity', False, 0)
                    return {
                        'success': False,
                        'error': 'Perplexity returned no data',
                        'api_source': 'perplexity'
                    }
                    
        except asyncio.TimeoutError:
            self._update_api_stats('perplexity', False, timeout)
            return {
                'success': False,
                'error': f'Perplexity timeout after {timeout}s',
                'api_source': 'perplexity'
            }
            
        except Exception as e:
            self._update_api_stats('perplexity', False, 0)
            return {
                'success': False,
                'error': f'Perplexity error: {str(e)}',
                'api_source': 'perplexity'
            }
    
    async def research_mine_list(self, mine_names: List[str], 
                               progress_callback=None) -> BatchResearchResult:
        """
        Recherchiert Liste von Minen mit Batch-Verarbeitung
        
        Args:
            mine_names: Liste der Mine-Namen
            progress_callback: Optional callback function für Progress Updates
            
        Returns:
            BatchResearchResult mit allen Einzelergebnissen
        """
        
        start_time = time.time()
        logger.info(f"🚀 Starting batch research for {len(mine_names)} mines")
        
        batch_result = BatchResearchResult()
        
        # Process mines in batches for rate limiting
        total_mines = len(mine_names)
        
        for i, mine_name in enumerate(mine_names):
            try:
                # Progress callback
                if progress_callback:
                    progress = (i / total_mines) * 100
                    progress_callback(progress, f"Researching {mine_name}...")
                
                # Research individual mine
                mine_result = await self.research_mine_comprehensive(mine_name)
                batch_result.results.append(mine_result)
                
                # Rate limiting between requests
                if i < total_mines - 1:  # Don't wait after last mine
                    await asyncio.sleep(self.request_delay)
                
                logger.info(f"📊 Progress: {i+1}/{total_mines} ({((i+1)/total_mines)*100:.1f}%)")
                
            except Exception as e:
                # Create error result for failed mine
                error_result = MineResearchResult()
                error_result.mine_data.name = mine_name
                error_result.add_error(f"Batch processing error: {str(e)}")
                batch_result.results.append(error_result)
                
                logger.error(f"❌ Batch error for {mine_name}: {e}")
        
        # Finalize batch results
        batch_result.total_duration = time.time() - start_time
        
        # Final progress callback
        if progress_callback:
            progress_callback(100, "Batch research completed!")
        
        logger.info(f"🏁 Batch research completed: {batch_result.success_count}/{total_mines} successful "
                   f"in {batch_result.total_duration:.1f}s")
        
        return batch_result
    
    async def test_api_connections(self) -> Dict[str, bool]:
        """
        Testet alle konfigurierten API-Verbindungen
        
        Returns:
            Dict mit Testergebnissen für jede API
        """
        
        logger.info("🧪 Testing API connections...")
        test_results = {}
        
        for api_name, api_config in self.api_clients.items():
            if not api_config['enabled']:
                test_results[api_name] = False
                logger.info(f"⏭️  Skipping {api_name} (not enabled)")
                continue
            
            try:
                if api_name == 'perplexity':
                    async with PerplexityClient(api_config['key']) as client:
                        test_results[api_name] = await client.test_connection()
                
                elif api_name == 'tavily':
                    async with TavilyClient(api_config['key']) as client:
                        test_results[api_name] = await client.test_connection()
                
                else:
                    # Future APIs (Exa, etc.)
                    test_results[api_name] = False
                
                status = "✅ PASS" if test_results[api_name] else "❌ FAIL"
                logger.info(f"{status} {api_name.title()} API test")
                
            except Exception as e:
                test_results[api_name] = False
                logger.error(f"❌ FAIL {api_name.title()} API test: {e}")
        
        successful_apis = sum(test_results.values())
        total_apis = len([api for api in self.api_clients.values() if api['enabled']])
        
        logger.info(f"🏁 API testing completed: {successful_apis}/{total_apis} APIs working")
        
        return test_results
    
    def _get_primary_api(self) -> Optional[Dict[str, Any]]:
        """Gibt primäres API zurück (höchste Priorität, enabled)"""
        
        enabled_apis = [(name, config) for name, config in self.api_clients.items() 
                       if config['enabled']]
        
        if not enabled_apis:
            return None
        
        # Sort by priority (lower number = higher priority)
        enabled_apis.sort(key=lambda x: x[1]['priority'])
        
        primary_name, primary_config = enabled_apis[0]
        return {
            'name': primary_name,
            'config': primary_config
        }
    
    def _normalize_mine_name(self, mine_name: str) -> str:
        """Normalisiert Mine-Namen"""
        if self.settings:
            return self.settings.normalize_mine_name(mine_name)
        else:
            return mine_name.strip()
    
    def _calculate_confidence_score(self, api_result: Dict[str, Any]) -> float:
        """Berechnet Confidence Score für API-Ergebnis"""
        
        base_confidence = 0.7  # Base confidence for successful API call
        
        # Adjust based on source count
        source_count = api_result.get('source_count', 0)
        if source_count >= 5:
            base_confidence += 0.2
        elif source_count >= 3:
            base_confidence += 0.1
        elif source_count < 2:
            base_confidence -= 0.2
        
        # Adjust based on response time (faster = more reliable)
        response_time = api_result.get('response_time', 60)
        if response_time < 30:
            base_confidence += 0.05
        elif response_time > 90:
            base_confidence -= 0.1
        
        # Ensure bounds
        return max(0.0, min(1.0, base_confidence))
    
    def _add_sources_to_result(self, result: MineResearchResult, api_result: Dict[str, Any]):
        """Fügt Quellen-Metadaten zum Result hinzu (robust gegen fehlerhafte API-Responses)"""
        citations = api_result.get('citations', [])
        processed_sources = api_result.get('processed_sources', [])

        for citation in citations[:10]:  # Limit to 10 sources
            if isinstance(citation, dict):
                source = SourceMetadata(
                    url=citation.get('url', ''),
                    title=citation.get('title', ''),
                    source_type=citation.get('source_type', 'unknown'),
                    confidence=0.8  # Default confidence for API sources
                )
                result.sources.append(source)
            else:
                logger.warning(f"Skipped invalid citation (not a dict): {citation}")

        # Update primary sources list
        primary_urls = [citation.get('url', '') for citation in citations[:3] if isinstance(citation, dict)]
        result.primary_sources = [url for url in primary_urls if url]
    
    def _update_api_stats(self, api_name: str, success: bool, response_time: float):
        """Aktualisiert API-Nutzungsstatistiken"""
        
        if api_name not in self.research_stats['api_usage']:
            self.research_stats['api_usage'][api_name] = {
                'requests': 0,
                'successes': 0,
                'failures': 0,
                'total_response_time': 0.0,
                'avg_response_time': 0.0
            }
        
        stats = self.research_stats['api_usage'][api_name]
        stats['requests'] += 1
        stats['total_response_time'] += response_time
        
        if success:
            stats['successes'] += 1
        else:
            stats['failures'] += 1
        
        # Update average
        stats['avg_response_time'] = stats['total_response_time'] / stats['requests']
    
    def _update_research_stats(self, result: MineResearchResult):
        """Aktualisiert allgemeine Research-Statistiken"""
        
        self.research_stats['total_researched'] += 1
        self.research_stats['total_duration'] += result.research_duration
        
        if result.validation_status != 'invalid':
            self.research_stats['successful_researches'] += 1
        else:
            self.research_stats['failed_researches'] += 1
            self.research_stats['error_log'].append({
                'mine_name': result.mine_data.name,
                'timestamp': result.research_timestamp,
                'errors': result.errors
            })
    
    def get_research_statistics(self) -> Dict[str, Any]:
        """Gibt umfassende Research-Statistiken zurück"""
        
        stats = self.research_stats.copy()
        
        # Calculate derived metrics
        if stats['total_researched'] > 0:
            stats['success_rate'] = stats['successful_researches'] / stats['total_researched']
            stats['avg_duration_per_mine'] = stats['total_duration'] / stats['total_researched']
        else:
            stats['success_rate'] = 0.0
            stats['avg_duration_per_mine'] = 0.0
        
        # Add parser statistics
        stats['parser_stats'] = self.parser.get_parsing_statistics()
        
        # Add API client information
        stats['configured_apis'] = {
            name: {
                'enabled': config['enabled'],
                'priority': config['priority']
            }
            for name, config in self.api_clients.items()
        }
        
        return stats
    
    def reset_statistics(self):
        """Setzt alle Statistiken zurück"""
        self.research_stats = {
            'total_researched': 0,
            'successful_researches': 0,
            'failed_researches': 0,
            'total_duration': 0.0,
            'api_usage': {},
            'error_log': []
        }
        self.parser.reset_statistics()
        
        logger.info("📊 Statistics reset")

# Convenience functions für einfache Nutzung

async def quick_research(mine_name: str, perplexity_key: str) -> MineDataFields:
    """
    Quick research function für einzelne Mine
    
    Args:
        mine_name: Name der Mine
        perplexity_key: Perplexity API Key
        
    Returns:
        MineDataFields mit Research-Ergebnissen
    """
    
    researcher = WebMiningResearcher({'perplexity_key': perplexity_key})
    result = await researcher.research_mine_comprehensive(mine_name)
    return result.mine_data

async def batch_research(mine_names: List[str], perplexity_key: str) -> List[MineDataFields]:
    """
    Batch research function für Mine-Liste
    
    Args:
        mine_names: Liste der Mine-Namen
        perplexity_key: Perplexity API Key
        
    Returns:
        Liste von MineDataFields
    """
    
    researcher = WebMiningResearcher({'perplexity_key': perplexity_key})
    batch_result = await researcher.research_mine_list(mine_names)
    return [result.mine_data for result in batch_result.results]

def create_researcher_from_config() -> WebMiningResearcher:
    """
    Erstellt Researcher basierend auf Environment-Konfiguration
    
    Returns:
        WebMiningResearcher mit Auto-Konfiguration
    """
    
    try:
        from config.api_keys import APIConfig
        
        config = {}
        validation = APIConfig.validate_config()
        
        if validation['perplexity']:
            config['perplexity_key'] = APIConfig.PERPLEXITY_API_KEY
        
        if validation['tavily']:
            config['tavily_key'] = APIConfig.TAVILY_API_KEY
        
        if validation['exa']:
            config['exa_key'] = APIConfig.EXA_API_KEY
        
        if not config:
            raise ValueError("No API keys configured. Please set environment variables.")
        
        return WebMiningResearcher(config)
        
    except ImportError:
        raise ValueError("Configuration not available. Please provide API keys manually.")

if __name__ == "__main__":
    # Test Web Mining Researcher
    async def test_researcher():
        print("🧪 Testing WebMiningResearcher...")
        
        # Test with placeholder config
        try:
            config = {'perplexity_key': 'test-key'}
            researcher = WebMiningResearcher(config)
            
            print(f"✅ Researcher initialized with {len(researcher.api_clients)} API clients")
            
            # Test API connection would require real key
            print("⚠️  Skipping connection test (requires real API key)")
            
            # Test statistics
            stats = researcher.get_research_statistics()
            print(f"📊 Initial stats: {stats['total_researched']} researched")
            
        except Exception as e:
            print(f"❌ Researcher test failed: {e}")
    
    # Run test
    asyncio.run(test_researcher())
    print("✅ Web researcher test completed!")
