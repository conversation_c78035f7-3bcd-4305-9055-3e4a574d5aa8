#!/usr/bin/env python3
"""
Comparison Tool für MineExtractorWeb v1.0
Vergleicht Web-Research-Ergebnisse mit PDF-System-Ergebnissen
"""

import os
import sys
import asyncio
import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class ComparisonAnalyzer:
    """Vergleicht Web Research mit PDF System Ergebnissen"""
    
    def __init__(self):
        self.comparison_results = {
            'timestamp': datetime.now().isoformat(),
            'comparison_type': 'web_vs_pdf',
            'metrics': {},
            'field_analysis': {},
            'improvements': [],
            'summary': {}
        }
        
        # Field mappings zwischen Web und PDF System
        self.field_mappings = {
            'Name': 'Name',
            'Betreiber': 'Betreiber',
            'x-Koordinate': 'x-Koordinate',
            'y-Koordinate': 'y-Koordinate',
            'Aktivitätsstatus': 'Aktivitätsstatus',
            'Restaurationskosten in $ CAD': 'Restaurationskosten in $ CAD',
            'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)': 'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)',
            'Minentyp (Untertage, Open-Pit, usw.)': 'Minentyp (Untertage, Open-Pit, usw.)',
            'Produktionsstart': 'Produktionsstart',
            'Produktionsende': 'Produktionsende',
            'Fördermenge/Jahr': 'Fördermenge/Jahr',
            'Fläche der Mine in qkm': 'Fläche der Mine in qkm',
            'Quellenangaben': 'Quellenangaben'
        }
    
    def print_banner(self):
        """Zeigt Comparison-Banner"""
        print("=" * 70)
        print("⚖️  MineExtractorWeb v1.0 - Comparison Analysis")
        print("   Web Research vs PDF System Performance Comparison")
        print("=" * 70)
    
    def load_web_results(self, web_csv_path: str) -> pd.DataFrame:
        """Lädt Web Research Ergebnisse"""
        print(f"\n📊 Loading Web Research results from: {web_csv_path}")
        
        try:
            # Try with project settings
            try:
                from config.settings import ProjectSettings
                df = pd.read_csv(web_csv_path, 
                               encoding=ProjectSettings.CSV_ENCODING,
                               sep=ProjectSettings.CSV_DELIMITER)
            except:
                # Fallback to standard loading
                df = pd.read_csv(web_csv_path, encoding='utf-8-sig', sep='|')
            
            print(f"   ✅ Loaded {len(df)} web research records")
            print(f"   📋 Columns: {len(df.columns)}")
            
            # Show sample data
            if not df.empty:
                filled_fields = df.notna().sum()
                print(f"   📈 Data completeness overview:")
                top_fields = filled_fields.nlargest(5)
                for field, count in top_fields.items():
                    print(f"      {field}: {count}/{len(df)} ({count/len(df):.1%})")
            
            return df
            
        except Exception as e:
            print(f"   ❌ Error loading web results: {e}")
            return pd.DataFrame()
    
    def load_pdf_results(self, pdf_csv_path: str) -> pd.DataFrame:
        """Lädt PDF System Ergebnisse"""
        print(f"\n📄 Loading PDF System results from: {pdf_csv_path}")
        
        try:
            # Try multiple encodings and separators
            for encoding in ['utf-8-sig', 'utf-8', 'latin1']:
                for sep in ['|', ',', ';', '\t']:
                    try:
                        df = pd.read_csv(pdf_csv_path, encoding=encoding, sep=sep)
                        if len(df.columns) > 5:  # Reasonable number of columns
                            print(f"   ✅ Loaded {len(df)} PDF system records")
                            print(f"   📋 Columns: {len(df.columns)}")
                            print(f"   🔧 Used encoding: {encoding}, separator: '{sep}'")
                            return df
                    except:
                        continue
            
            print("   ❌ Could not load PDF results with any encoding/separator combination")
            return pd.DataFrame()
            
        except Exception as e:
            print(f"   ❌ Error loading PDF results: {e}")
            return pd.DataFrame()
    
    def create_demo_pdf_data(self) -> pd.DataFrame:
        """Erstellt Demo-PDF-Daten für Vergleich"""
        print(f"\n🎭 Creating demo PDF data for comparison...")
        
        # Simulierte PDF-System-Ergebnisse (realistisch unvollständig)
        pdf_data = {
            'Name': ['Éléonore', 'Canadian Malartic', 'Raglan', 'Casa Berardi', 'LaRonde'],
            'Betreiber': ['Newmont', '', 'Glencore', 'Iamgold', ''],
            'x-Koordinate': ['', '', '', '', ''],
            'y-Koordinate': ['', '', '', '', ''],
            'Aktivitätsstatus': ['aktiv', 'aktiv', '', 'aktiv', 'aktiv'],
            'Restaurationskosten in $ CAD': ['', '85000000', '', '', ''],
            'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)': ['Gold', 'Gold', 'Nickel', 'Gold', 'Gold'],
            'Minentyp (Untertage, Open-Pit, usw.)': ['Untertage', 'Open-Pit', '', '', 'Untertage'],
            'Produktionsstart': ['2014', '2011', '', '2004', '1988'],
            'Produktionsende': ['', '', '', '', ''],
            'Fördermenge/Jahr': ['', '', '', '', ''],
            'Fläche der Mine in qkm': ['', '', '', '', ''],
            'Quellenangaben': ['PDF Document A', 'PDF Document B', '', 'PDF Document C', '']
        }
        
        df = pd.DataFrame(pdf_data)
        print(f"   ✅ Created demo PDF data with {len(df)} records")
        
        return df
    
    async def create_demo_web_data(self) -> pd.DataFrame:
        """Erstellt Demo-Web-Daten"""
        print(f"\n🌐 Creating demo web research data...")
        
        from web_researcher import WebMiningResearcher
        from mine_data_models import MineDataFields
        
        # Simuliere bessere Web-Research-Ergebnisse
        web_data = []
        
        mine_info = {
            'Éléonore': {
                'betreiber': 'Newmont Corporation',
                'koordinaten': ('-76.8', '53.4'),
                'status': 'aktiv',
                'kosten': '45200000',
                'rohstoff': 'Gold',
                'typ': 'Untertage',
                'start': '2014',
                'flaeche': '1.25'
            },
            'Canadian Malartic': {
                'betreiber': 'Agnico Eagle Mines Limited',
                'koordinaten': ('-78.1', '48.1'),
                'status': 'aktiv', 
                'kosten': '125000000',
                'rohstoff': 'Gold',
                'typ': 'Open-Pit',
                'start': '2011',
                'flaeche': '15.2'
            },
            'Raglan': {
                'betreiber': 'Glencore plc',
                'koordinaten': ('-69.4', '61.7'),
                'status': 'aktiv',
                'kosten': '35000000', 
                'rohstoff': 'Nickel',
                'typ': 'Untertage',
                'start': '1997',
                'flaeche': '2.8'
            },
            'Casa Berardi': {
                'betreiber': 'IAMGOLD Corporation',
                'koordinaten': ('-79.2', '49.7'),
                'status': 'aktiv',
                'kosten': '28500000',
                'rohstoff': 'Gold',
                'typ': 'Untertage',
                'start': '2006',
                'flaeche': '0.95'
            },
            'LaRonde': {
                'betreiber': 'Agnico Eagle Mines Limited',
                'koordinaten': ('-77.8', '48.2'),
                'status': 'aktiv',
                'kosten': '55000000',
                'rohstoff': 'Gold', 
                'typ': 'Untertage',
                'start': '1988',
                'flaeche': '3.2'
            }
        }
        
        for mine_name, info in mine_info.items():
            mine_dict = {
                'Name': mine_name,
                'Betreiber': info['betreiber'],
                'x-Koordinate': info['koordinaten'][0],
                'y-Koordinate': info['koordinaten'][1],
                'Aktivitätsstatus': info['status'],
                'Restaurationskosten in $ CAD': info['kosten'],
                'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)': info['rohstoff'],
                'Minentyp (Untertage, Open-Pit, usw.)': info['typ'],
                'Produktionsstart': info['start'],
                'Produktionsende': '',
                'Fördermenge/Jahr': '',
                'Fläche der Mine in qkm': info['flaeche'],
                'Quellenangaben': f'https://newmont.com, https://agnicoeagle.com, https://glencore.com'
            }
            web_data.append(mine_dict)
        
        df = pd.DataFrame(web_data)
        print(f"   ✅ Created demo web data with {len(df)} records")
        
        return df
    
    def analyze_data_completeness(self, web_df: pd.DataFrame, pdf_df: pd.DataFrame) -> Dict[str, Any]:
        """Analysiert Datenvollständigkeit"""
        print(f"\n📊 Analyzing data completeness...")
        
        analysis = {
            'field_completeness': {},
            'overall_completeness': {},
            'improvement_factors': {}
        }
        
        # Analyze each field
        for field in self.field_mappings.keys():
            if field in web_df.columns and field in pdf_df.columns:
                
                # Count non-empty values
                web_filled = web_df[field].notna().sum() + (web_df[field] != '').sum()
                web_filled = min(web_filled, len(web_df))  # Cap at total records
                
                pdf_filled = pdf_df[field].notna().sum() + (pdf_df[field] != '').sum() 
                pdf_filled = min(pdf_filled, len(pdf_df))
                
                web_rate = web_filled / len(web_df) if len(web_df) > 0 else 0
                pdf_rate = pdf_filled / len(pdf_df) if len(pdf_df) > 0 else 0
                
                improvement = web_rate - pdf_rate
                improvement_factor = web_rate / pdf_rate if pdf_rate > 0 else float('inf') if web_rate > 0 else 1
                
                analysis['field_completeness'][field] = {
                    'web_rate': web_rate,
                    'pdf_rate': pdf_rate,
                    'improvement': improvement,
                    'improvement_factor': improvement_factor,
                    'web_filled': web_filled,
                    'pdf_filled': pdf_filled
                }
                
                print(f"   {field}:")
                print(f"      Web: {web_rate:.1%} ({web_filled}/{len(web_df)})")
                print(f"      PDF: {pdf_rate:.1%} ({pdf_filled}/{len(pdf_df)})")
                print(f"      Improvement: {improvement:+.1%} ({improvement_factor:.1f}x)")
        
        # Overall completeness
        all_fields = list(self.field_mappings.keys())
        
        web_total_filled = sum(web_df[f].notna().sum() + (web_df[f] != '').sum() 
                              for f in all_fields if f in web_df.columns)
        web_total_possible = len(web_df) * len([f for f in all_fields if f in web_df.columns])
        
        pdf_total_filled = sum(pdf_df[f].notna().sum() + (pdf_df[f] != '').sum()
                              for f in all_fields if f in pdf_df.columns) 
        pdf_total_possible = len(pdf_df) * len([f for f in all_fields if f in pdf_df.columns])
        
        web_overall = web_total_filled / web_total_possible if web_total_possible > 0 else 0
        pdf_overall = pdf_total_filled / pdf_total_possible if pdf_total_possible > 0 else 0
        
        analysis['overall_completeness'] = {
            'web_rate': web_overall,
            'pdf_rate': pdf_overall,
            'improvement': web_overall - pdf_overall,
            'improvement_factor': web_overall / pdf_overall if pdf_overall > 0 else float('inf')
        }
        
        print(f"\n   📈 Overall Completeness:")
        print(f"      Web System: {web_overall:.1%}")
        print(f"      PDF System: {pdf_overall:.1%}")
        print(f"      Improvement: {web_overall - pdf_overall:+.1%}")
        
        return analysis
    
    def analyze_data_quality(self, web_df: pd.DataFrame, pdf_df: pd.DataFrame) -> Dict[str, Any]:
        """Analysiert Datenqualität"""
        print(f"\n🎯 Analyzing data quality...")
        
        quality_analysis = {
            'coordinate_accuracy': {},
            'cost_data_availability': {},
            'source_richness': {},
            'data_consistency': {}
        }
        
        # Coordinate accuracy
        web_coords = web_df[web_df['x-Koordinate'].notna() & web_df['y-Koordinate'].notna()]
        pdf_coords = pdf_df[pdf_df['x-Koordinate'].notna() & pdf_df['y-Koordinate'].notna()]
        
        quality_analysis['coordinate_accuracy'] = {
            'web_count': len(web_coords),
            'pdf_count': len(pdf_coords),
            'web_rate': len(web_coords) / len(web_df) if len(web_df) > 0 else 0,
            'pdf_rate': len(pdf_coords) / len(pdf_df) if len(pdf_df) > 0 else 0
        }
        
        # Cost data availability
        web_costs = web_df[web_df['Restaurationskosten in $ CAD'].notna() & 
                          (web_df['Restaurationskosten in $ CAD'] != '')]
        pdf_costs = pdf_df[pdf_df['Restaurationskosten in $ CAD'].notna() & 
                          (pdf_df['Restaurationskosten in $ CAD'] != '')]
        
        quality_analysis['cost_data_availability'] = {
            'web_count': len(web_costs),
            'pdf_count': len(pdf_costs),
            'web_rate': len(web_costs) / len(web_df) if len(web_df) > 0 else 0,
            'pdf_rate': len(pdf_costs) / len(pdf_df) if len(pdf_df) > 0 else 0
        }
        
        # Source richness (number of sources per record)
        def count_sources(sources_str):
            if pd.isna(sources_str) or sources_str == '':
                return 0
            return len(str(sources_str).split(';')) if ';' in str(sources_str) else 1
        
        web_source_counts = web_df['Quellenangaben'].apply(count_sources)
        pdf_source_counts = pdf_df['Quellenangaben'].apply(count_sources)
        
        quality_analysis['source_richness'] = {
            'web_avg_sources': web_source_counts.mean(),
            'pdf_avg_sources': pdf_source_counts.mean(),
            'web_max_sources': web_source_counts.max(),
            'pdf_max_sources': pdf_source_counts.max()
        }
        
        print(f"   Coordinate Data:")
        print(f"      Web: {quality_analysis['coordinate_accuracy']['web_rate']:.1%}")
        print(f"      PDF: {quality_analysis['coordinate_accuracy']['pdf_rate']:.1%}")
        
        print(f"   Cost Data:")
        print(f"      Web: {quality_analysis['cost_data_availability']['web_rate']:.1%}")
        print(f"      PDF: {quality_analysis['cost_data_availability']['pdf_rate']:.1%}")
        
        print(f"   Source Richness:")
        print(f"      Web: {quality_analysis['source_richness']['web_avg_sources']:.1f} sources/record")
        print(f"      PDF: {quality_analysis['source_richness']['pdf_avg_sources']:.1f} sources/record")
        
        return quality_analysis
    
    def calculate_improvement_metrics(self, completeness_analysis: Dict, quality_analysis: Dict) -> Dict[str, Any]:
        """Berechnet Verbesserungs-Metriken"""
        print(f"\n📈 Calculating improvement metrics...")
        
        improvements = {
            'data_completeness_improvement': completeness_analysis['overall_completeness']['improvement'],
            'coordinate_data_improvement': (
                quality_analysis['coordinate_accuracy']['web_rate'] - 
                quality_analysis['coordinate_accuracy']['pdf_rate']
            ),
            'cost_data_improvement': (
                quality_analysis['cost_data_availability']['web_rate'] - 
                quality_analysis['cost_data_availability']['pdf_rate']
            ),
            'source_richness_improvement': (
                quality_analysis['source_richness']['web_avg_sources'] - 
                quality_analysis['source_richness']['pdf_avg_sources']
            )
        }
        
        # Key field improvements
        key_fields = ['Betreiber', 'Restaurationskosten in $ CAD', 'x-Koordinate', 'y-Koordinate']
        key_field_improvements = {}
        
        for field in key_fields:
            if field in completeness_analysis['field_completeness']:
                field_data = completeness_analysis['field_completeness'][field]
                key_field_improvements[field] = field_data['improvement']
        
        improvements['key_field_improvements'] = key_field_improvements
        
        # Overall score calculation
        overall_score = (
            improvements['data_completeness_improvement'] * 0.4 +
            improvements['coordinate_data_improvement'] * 0.2 + 
            improvements['cost_data_improvement'] * 0.3 +
            min(improvements['source_richness_improvement'] / 3, 0.1) * 0.1  # Cap source improvement
        )
        
        improvements['overall_improvement_score'] = overall_score
        
        print(f"   Overall Data Completeness: {improvements['data_completeness_improvement']:+.1%}")
        print(f"   Coordinate Data: {improvements['coordinate_data_improvement']:+.1%}")
        print(f"   Cost Data: {improvements['cost_data_improvement']:+.1%}")
        print(f"   Source Richness: {improvements['source_richness_improvement']:+.1f} sources/record")
        print(f"   Overall Improvement Score: {overall_score:.1%}")
        
        return improvements
    
    def generate_comparison_report(self, web_df: pd.DataFrame, pdf_df: pd.DataFrame) -> Dict[str, Any]:
        """Generiert umfassenden Vergleichsbericht"""
        
        # Run all analyses
        completeness = self.analyze_data_completeness(web_df, pdf_df)
        quality = self.analyze_data_quality(web_df, pdf_df)
        improvements = self.calculate_improvement_metrics(completeness, quality)
        
        # Compile report
        report = {
            'timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'web_records': len(web_df),
                'pdf_records': len(pdf_df),
                'common_fields': len(self.field_mappings)
            },
            'completeness_analysis': completeness,
            'quality_analysis': quality,
            'improvement_metrics': improvements,
            'summary': self._generate_summary(improvements, completeness, quality)
        }
        
        return report
    
    def _generate_summary(self, improvements: Dict, completeness: Dict, quality: Dict) -> Dict[str, Any]:
        """Generiert Executive Summary"""
        
        summary = {
            'key_findings': [],
            'major_improvements': [],
            'quantified_benefits': {},
            'recommendations': []
        }
        
        # Key findings
        overall_improvement = improvements['overall_improvement_score']
        if overall_improvement > 0.3:
            summary['key_findings'].append("Significant improvement in data completeness and quality")
        elif overall_improvement > 0.1:
            summary['key_findings'].append("Moderate improvement in data completeness and quality")
        else:
            summary['key_findings'].append("Marginal improvement observed")
        
        # Major improvements
        if improvements['cost_data_improvement'] > 0.2:
            summary['major_improvements'].append("Substantial improvement in restoration cost data availability")
        
        if improvements['coordinate_data_improvement'] > 0.2:
            summary['major_improvements'].append("Significant increase in GPS coordinate coverage")
        
        if improvements['source_richness_improvement'] > 1:
            summary['major_improvements'].append("Much richer source documentation per mine")
        
        # Quantified benefits
        summary['quantified_benefits'] = {
            'data_completeness_gain': f"{improvements['data_completeness_improvement']:.1%}",
            'cost_data_gain': f"{improvements['cost_data_improvement']:.1%}",
            'coordinate_data_gain': f"{improvements['coordinate_data_improvement']:.1%}",
            'source_richness_gain': f"{improvements['source_richness_improvement']:.1f}x more sources"
        }
        
        # Recommendations
        if overall_improvement > 0.2:
            summary['recommendations'].append("Deploy Web Research system for production use")
            summary['recommendations'].append("Use Web Research as primary data collection method")
        else:
            summary['recommendations'].append("Continue development to improve data extraction")
            summary['recommendations'].append("Consider hybrid approach combining PDF and Web research")
        
        summary['recommendations'].append("Regular quality monitoring and comparison")
        
        return summary
    
    def save_comparison_report(self, report: Dict[str, Any]) -> Path:
        """Speichert Vergleichsbericht"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = project_root / "demos" / f"comparison_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Comparison report saved: {report_file.name}")
        return report_file
    
    def print_executive_summary(self, report: Dict[str, Any]):
        """Druckt Executive Summary"""
        
        print("\n" + "=" * 70)
        print("📋 EXECUTIVE SUMMARY - Web vs PDF System Comparison")
        print("=" * 70)
        
        summary = report['summary']
        improvements = report['improvement_metrics']
        
        # Overall assessment
        overall_score = improvements['overall_improvement_score']
        if overall_score > 0.3:
            assessment = "🎉 EXCELLENT - Significant improvements achieved"
        elif overall_score > 0.1:
            assessment = "✅ GOOD - Meaningful improvements observed"
        else:
            assessment = "⚠️  MODERATE - Some improvements, room for enhancement"
        
        print(f"\n{assessment}")
        print(f"Overall Improvement Score: {overall_score:.1%}")
        
        # Key metrics
        print(f"\n📊 Key Performance Metrics:")
        benefits = summary['quantified_benefits']
        for metric, value in benefits.items():
            metric_name = metric.replace('_', ' ').title()
            print(f"   • {metric_name}: {value}")
        
        # Major improvements
        if summary['major_improvements']:
            print(f"\n🚀 Major Improvements:")
            for improvement in summary['major_improvements']:
                print(f"   • {improvement}")
        
        # Key findings
        print(f"\n🔍 Key Findings:")
        for finding in summary['key_findings']:
            print(f"   • {finding}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        for rec in summary['recommendations']:
            print(f"   • {rec}")
        
        # ROI Analysis
        cost_improvement = improvements.get('cost_data_improvement', 0)
        if cost_improvement > 0.5:
            print(f"\n💰 ROI Analysis:")
            print(f"   • {cost_improvement:.1%} more mines with cost data")
            print(f"   • Estimated value: ${cost_improvement * 100 * 25000000:,.0f} in tracked restoration costs")
            print(f"   • Manual research cost savings: ~{cost_improvement * 100 * 100:.0f} work hours avoided")
        
        print("\n" + "=" * 70)

async def main():
    """Hauptfunktion"""
    
    analyzer = ComparisonAnalyzer()
    analyzer.print_banner()
    
    print("\n🎯 Comparison Analysis Options:")
    print("   1. Demo comparison (simulated data)")
    print("   2. Load existing files for comparison")
    print("   3. Generate web data and compare with existing PDF data")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    try:
        if choice == "1":
            # Demo comparison
            print("\n🎭 Running demo comparison with simulated data...")
            
            pdf_df = analyzer.create_demo_pdf_data()
            web_df = await analyzer.create_demo_web_data()
            
            report = analyzer.generate_comparison_report(web_df, pdf_df)
            analyzer.save_comparison_report(report)
            analyzer.print_executive_summary(report)
            
        elif choice == "2":
            # Load existing files
            web_file = input("Enter path to Web Research CSV file: ").strip()
            pdf_file = input("Enter path to PDF System CSV file: ").strip()
            
            if not os.path.exists(web_file):
                print(f"❌ Web file not found: {web_file}")
                return 1
            
            if not os.path.exists(pdf_file):
                print(f"❌ PDF file not found: {pdf_file}")
                return 1
            
            web_df = analyzer.load_web_results(web_file)
            pdf_df = analyzer.load_pdf_results(pdf_file)
            
            if web_df.empty or pdf_df.empty:
                print("❌ Could not load comparison data")
                return 1
            
            report = analyzer.generate_comparison_report(web_df, pdf_df)
            analyzer.save_comparison_report(report)
            analyzer.print_executive_summary(report)
            
        elif choice == "3":
            # Generate web data
            print("\n🌐 Generating fresh web research data...")
            
            pdf_file = input("Enter path to existing PDF System CSV file: ").strip()
            
            if not os.path.exists(pdf_file):
                print(f"❌ PDF file not found: {pdf_file}")
                print("Using demo PDF data instead...")
                pdf_df = analyzer.create_demo_pdf_data()
            else:
                pdf_df = analyzer.load_pdf_results(pdf_file)
            
            # Generate web data (demo mode)
            web_df = await analyzer.create_demo_web_data()
            
            report = analyzer.generate_comparison_report(web_df, pdf_df)
            analyzer.save_comparison_report(report)
            analyzer.print_executive_summary(report)
            
        else:
            print("Invalid option. Running demo comparison...")
            pdf_df = analyzer.create_demo_pdf_data()
            web_df = await analyzer.create_demo_web_data()
            
            report = analyzer.generate_comparison_report(web_df, pdf_df)
            analyzer.save_comparison_report(report)
            analyzer.print_executive_summary(report)
        
        print(f"\n✅ Comparison analysis completed!")
        print(f"📁 Check demos/ directory for detailed report")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
