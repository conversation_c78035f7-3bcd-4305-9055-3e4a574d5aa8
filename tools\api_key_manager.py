#!/usr/bin/env python3
"""
API Key Manager für MineExtractorWeb v1.0
Sichere Verwaltung und Validation von API Keys
"""

import os
import sys
import getpass
import json
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
import base64

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class APIKeyManager:
    """Sichere Verwaltung von API Keys"""
    
    def __init__(self):
        self.env_file = project_root / ".env"
        self.backup_file = project_root / ".env.backup"
        
        self.api_providers = {
            'PERPLEXITY_API_KEY': {
                'name': 'Perplexity AI',
                'required': True,
                'url': 'https://www.perplexity.ai/',
                'cost': '$20/month',
                'description': 'Primary research API for deep mining data analysis',
                'test_endpoint': 'https://api.perplexity.ai/chat/completions'
            },
            'TAVILY_API_KEY': {
                'name': 'Tavily AI',
                'required': False,
                'url': 'https://tavily.com/',
                'cost': '$20/month',
                'description': 'Government and regulatory data specialist',
                'test_endpoint': 'https://api.tavily.com/search'
            },
            'EXA_API_KEY': {
                'name': 'Exa.ai',
                'required': False,
                'url': 'https://exa.ai/',
                'cost': '$29/month',
                'description': 'Semantic search for hard-to-find mining data',
                'test_endpoint': 'https://api.exa.ai/search'
            },
            'APIFY_API_KEY': {
                'name': 'Apify',
                'required': False,
                'url': 'https://apify.com/',
                'cost': '$49/month',
                'description': 'Government database scraping platform',
                'test_endpoint': 'https://api.apify.com'
            },
            'SCRAPINGBEE_API_KEY': {
                'name': 'ScrapingBee',
                'required': False,
                'url': 'https://www.scrapingbee.com/',
                'cost': '$29/month',
                'description': 'JavaScript-heavy sites scraping service',
                'test_endpoint': 'https://app.scrapingbee.com/api/v1'
            }
        }
    
    def print_banner(self):
        """Zeigt API Key Manager Banner"""
        print("=" * 70)
        print("🔑 MineExtractorWeb v1.0 - API Key Manager")
        print("   Secure API Key Management & Validation")
        print("=" * 70)
    
    def show_api_overview(self):
        """Zeigt API-Übersicht"""
        print("\n📋 Available API Providers:")
        print("-" * 50)
        
        for key_name, info in self.api_providers.items():
            status = "REQUIRED" if info['required'] else "OPTIONAL"
            print(f"\n🔹 {info['name']} ({status})")
            print(f"   URL: {info['url']}")
            print(f"   Cost: {info['cost']}")
            print(f"   Purpose: {info['description']}")
    
    def check_current_status(self) -> Dict[str, bool]:
        """Prüft aktuellen Status aller API Keys"""
        print("\n🔍 Checking current API key status...")
        
        status = {}
        
        if not self.env_file.exists():
            print("   ❌ .env file not found")
            return status
        
        # Read current .env file
        env_content = {}
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        env_content[key.strip()] = value.strip()
        except Exception as e:
            print(f"   ❌ Error reading .env file: {e}")
            return status
        
        # Check each API key
        for key_name, info in self.api_providers.items():
            if key_name in env_content:
                value = env_content[key_name]
                is_configured = value and not value.startswith('your_') and len(value) > 10
                status[key_name] = is_configured
                
                status_icon = "✅" if is_configured else "❌"
                status_text = "Configured" if is_configured else "Not configured"
                
                print(f"   {status_icon} {info['name']}: {status_text}")
            else:
                status[key_name] = False
                print(f"   ❌ {info['name']}: Missing from .env")
        
        # Summary
        configured_count = sum(status.values())
        required_count = sum(1 for info in self.api_providers.values() if info['required'])
        required_configured = sum(1 for key, info in self.api_providers.items() 
                                if info['required'] and status.get(key, False))
        
        print(f"\n📊 Summary:")
        print(f"   Total configured: {configured_count}/{len(self.api_providers)}")
        print(f"   Required configured: {required_configured}/{required_count}")
        
        if required_configured == required_count:
            print("   ✅ All required APIs configured - system ready!")
        else:
            print("   ⚠️  Missing required APIs - system not fully functional")
        
        return status
    
    def interactive_setup(self):
        """Interaktive API Key Konfiguration"""
        print("\n🔧 Interactive API Key Setup")
        print("=" * 40)
        
        # Backup existing .env if it exists
        if self.env_file.exists():
            backup_choice = input("Backup existing .env file? (y/n): ").lower().strip()
            if backup_choice == 'y':
                self.backup_env_file()
        
        # Load existing content
        existing_content = {}
        if self.env_file.exists():
            try:
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    existing_content = dict(line.strip().split('=', 1) 
                                          for line in f 
                                          if '=' in line and not line.startswith('#'))
            except:
                existing_content = {}
        
        new_content = existing_content.copy()
        
        # Configure each API
        for key_name, info in self.api_providers.items():
            print(f"\n🔹 {info['name']} Configuration")
            print(f"   Purpose: {info['description']}")
            print(f"   Cost: {info['cost']}")
            print(f"   URL: {info['url']}")
            
            current_value = existing_content.get(key_name, '')
            has_current = current_value and not current_value.startswith('your_')
            
            if has_current:
                print(f"   Current: ***{current_value[-8:]} (configured)")
                update_choice = input("   Update this key? (y/n): ").lower().strip()
                if update_choice != 'y':
                    continue
            
            if info['required']:
                print("   🚨 REQUIRED - System needs this API to function")
                
            # Get API key
            while True:
                if info['required']:
                    api_key = getpass.getpass(f"   Enter {info['name']} API key: ").strip()
                else:
                    api_key = getpass.getpass(f"   Enter {info['name']} API key (optional, press Enter to skip): ").strip()
                
                if not api_key:
                    if info['required']:
                        print("   ❌ Required API key cannot be empty")
                        continue
                    else:
                        print("   ⏭️  Skipping optional API")
                        break
                
                # Basic validation
                if len(api_key) < 10:
                    print("   ❌ API key seems too short (minimum 10 characters)")
                    continue
                
                if api_key.startswith('your_'):
                    print("   ❌ Please enter your actual API key, not the placeholder")
                    continue
                
                # Test API key if possible
                test_choice = input("   🧪 Test API key now? (y/n): ").lower().strip()
                if test_choice == 'y':
                    if self.test_api_key(key_name, api_key, info):
                        print("   ✅ API key validated successfully!")
                        new_content[key_name] = api_key
                        break
                    else:
                        print("   ❌ API key validation failed")
                        retry = input("   Try again? (y/n): ").lower().strip()
                        if retry != 'y':
                            break
                else:
                    new_content[key_name] = api_key
                    print("   💾 API key saved (not tested)")
                    break
        
        # Save updated .env file
        self.save_env_file(new_content)
        print("\n✅ API key configuration completed!")
        
        # Show final status
        self.check_current_status()
    
    def test_api_key(self, key_name: str, api_key: str, info: Dict) -> bool:
        """Testet einen API Key"""
        print(f"   🧪 Testing {info['name']} API key...")
        
        if key_name == 'PERPLEXITY_API_KEY':
            return self.test_perplexity_key(api_key)
        elif key_name == 'TAVILY_API_KEY':
            return self.test_tavily_key(api_key)
        elif key_name == 'EXA_API_KEY':
            return self.test_exa_key(api_key)
        else:
            print(f"      ⚠️  Testing not implemented for {info['name']}")
            return True  # Assume valid if we can't test
    
    def test_perplexity_key(self, api_key: str) -> bool:
        """Testet Perplexity API Key"""
        try:
            import aiohttp
            import asyncio
            
            async def test():
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [{"role": "user", "content": "Test connection"}],
                    "max_tokens": 10
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "https://api.perplexity.ai/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=10)
                    ) as response:
                        return response.status == 200
            
            return asyncio.run(test())
            
        except Exception as e:
            print(f"      Error testing Perplexity API: {e}")
            return False
    
    def test_tavily_key(self, api_key: str) -> bool:
        """Testet Tavily API Key"""
        try:
            import requests
            
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(
                "https://api.tavily.com/search",
                headers=headers,
                params={"q": "test", "max_results": 1},
                timeout=10
            )
            
            return response.status_code in [200, 400]  # 400 might be expected for test query
            
        except Exception as e:
            print(f"      Error testing Tavily API: {e}")
            return False
    
    def test_exa_key(self, api_key: str) -> bool:
        """Testet Exa API Key"""
        try:
            import requests
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                "https://api.exa.ai/search",
                headers=headers,
                json={"query": "test", "num_results": 1},
                timeout=10
            )
            
            return response.status_code in [200, 400]  # 400 might be expected for test query
            
        except Exception as e:
            print(f"      Error testing Exa API: {e}")
            return False
    
    def backup_env_file(self):
        """Erstellt Backup der .env Datei"""
        if self.env_file.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = project_root / f".env.backup_{timestamp}"
            
            try:
                import shutil
                shutil.copy2(self.env_file, backup_file)
                print(f"   ✅ Backup created: {backup_file.name}")
            except Exception as e:
                print(f"   ❌ Backup failed: {e}")
    
    def save_env_file(self, content: Dict[str, str]):
        """Speichert .env Datei"""
        try:
            # Create .env content
            lines = []
            lines.append("# MineExtractorWeb v1.0 - API Configuration")
            lines.append(f"# Generated: {datetime.now().isoformat()}")
            lines.append("")
            
            # Add API keys
            for key_name, info in self.api_providers.items():
                lines.append(f"# {info['name']} - {info['description']}")
                lines.append(f"# Cost: {info['cost']} - URL: {info['url']}")
                
                if key_name in content:
                    lines.append(f"{key_name}={content[key_name]}")
                else:
                    lines.append(f"{key_name}=your_{key_name.lower()}_here")
                
                lines.append("")
            
            # Add other settings
            lines.append("# Additional Settings")
            lines.append("LOG_LEVEL=INFO")
            lines.append("DEBUG_MODE=False")
            lines.append("MAX_CONCURRENT_REQUESTS=3")
            lines.append("REQUEST_TIMEOUT=120")
            
            # Write file
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            print(f"   ✅ Configuration saved to: {self.env_file.name}")
            
        except Exception as e:
            print(f"   ❌ Error saving .env file: {e}")
    
    def export_configuration(self) -> Optional[Path]:
        """Exportiert Konfiguration (ohne API Keys) für Sharing"""
        print("\n📤 Exporting configuration template...")
        
        try:
            config_template = {
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'api_providers': {},
                'settings': {
                    'LOG_LEVEL': 'INFO',
                    'DEBUG_MODE': False,
                    'MAX_CONCURRENT_REQUESTS': 3,
                    'REQUEST_TIMEOUT': 120
                }
            }
            
            # Add API provider info (without keys)
            for key_name, info in self.api_providers.items():
                config_template['api_providers'][key_name] = {
                    'name': info['name'],
                    'required': info['required'],
                    'url': info['url'],
                    'cost': info['cost'],
                    'description': info['description']
                }
            
            # Save template
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_file = project_root / f"config_template_{timestamp}.json"
            
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(config_template, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ Configuration template exported: {template_file.name}")
            print("   💡 This file can be shared safely (no API keys included)")
            
            return template_file
            
        except Exception as e:
            print(f"   ❌ Export failed: {e}")
            return None
    
    def show_cost_calculator(self):
        """Zeigt Kosten-Kalkulator"""
        print("\n💰 API Cost Calculator")
        print("=" * 30)
        
        # Get usage estimates
        mines_per_month = input("Estimated mines to research per month: ").strip()
        try:
            mines_per_month = int(mines_per_month)
        except:
            mines_per_month = 100  # Default
        
        print(f"\n📊 Cost Analysis for {mines_per_month} mines/month:")
        
        # Calculate costs
        scenarios = {
            'Basic (Perplexity only)': {
                'apis': ['PERPLEXITY_API_KEY'],
                'monthly_cost': 20
            },
            'Standard (Perplexity + Tavily)': {
                'apis': ['PERPLEXITY_API_KEY', 'TAVILY_API_KEY'],
                'monthly_cost': 40
            },
            'Premium (All Research APIs)': {
                'apis': ['PERPLEXITY_API_KEY', 'TAVILY_API_KEY', 'EXA_API_KEY'],
                'monthly_cost': 69
            },
            'Enterprise (All APIs)': {
                'apis': list(self.api_providers.keys()),
                'monthly_cost': 176
            }
        }
        
        for scenario_name, scenario in scenarios.items():
            monthly_cost = scenario['monthly_cost']
            cost_per_mine = monthly_cost / mines_per_month if mines_per_month > 0 else 0
            annual_cost = monthly_cost * 12
            
            print(f"\n🔹 {scenario_name}:")
            print(f"   Monthly: ${monthly_cost}")
            print(f"   Annual: ${annual_cost}")
            print(f"   Per mine: ${cost_per_mine:.2f}")
            print(f"   APIs: {len(scenario['apis'])}")
        
        # ROI calculation
        manual_cost_per_mine = 100  # Estimated manual research cost
        print(f"\n📈 ROI Analysis:")
        print(f"   Manual research cost: ${manual_cost_per_mine}/mine")
        print(f"   Manual monthly cost: ${manual_cost_per_mine * mines_per_month:,}")
        
        for scenario_name, scenario in scenarios.items():
            monthly_cost = scenario['monthly_cost']
            savings = (manual_cost_per_mine * mines_per_month) - monthly_cost
            roi_percentage = (savings / monthly_cost) * 100 if monthly_cost > 0 else 0
            
            print(f"   {scenario_name} savings: ${savings:,}/month ({roi_percentage:.0f}% ROI)")
    
    def run_interactive_manager(self):
        """Führt interaktiven API Key Manager aus"""
        
        self.print_banner()
        
        while True:
            print("\n📋 API Key Manager Options:")
            print("   1. 📊 Show current status")
            print("   2. 🔧 Interactive setup")
            print("   3. 🧪 Test existing keys")
            print("   4. 📋 Show API overview")
            print("   5. 💰 Cost calculator")
            print("   6. 📤 Export configuration template")
            print("   7. 🔄 Create .env backup")
            print("   8. ❌ Exit")
            
            choice = input("\nSelect option (1-8): ").strip()
            
            if choice == "1":
                self.check_current_status()
            
            elif choice == "2":
                self.interactive_setup()
            
            elif choice == "3":
                self.test_all_keys()
            
            elif choice == "4":
                self.show_api_overview()
            
            elif choice == "5":
                self.show_cost_calculator()
            
            elif choice == "6":
                self.export_configuration()
            
            elif choice == "7":
                self.backup_env_file()
            
            elif choice == "8":
                print("\n👋 Goodbye!")
                break
            
            else:
                print("\n❌ Invalid option. Please try again.")
    
    def test_all_keys(self):
        """Testet alle konfigurierten API Keys"""
        print("\n🧪 Testing all configured API keys...")
        
        status = self.check_current_status()
        
        if not any(status.values()):
            print("   ⚠️  No API keys configured to test")
            return
        
        # Load current keys
        if not self.env_file.exists():
            print("   ❌ .env file not found")
            return
        
        env_content = {}
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        env_content[key.strip()] = value.strip()
        except Exception as e:
            print(f"   ❌ Error reading .env file: {e}")
            return
        
        # Test each configured key
        for key_name, is_configured in status.items():
            if is_configured and key_name in env_content:
                info = self.api_providers[key_name]
                api_key = env_content[key_name]
                
                print(f"\n   Testing {info['name']}...")
                success = self.test_api_key(key_name, api_key, info)
                
                if success:
                    print(f"      ✅ {info['name']} API key is valid")
                else:
                    print(f"      ❌ {info['name']} API key test failed")

def main():
    """Hauptfunktion"""
    
    manager = APIKeyManager()
    
    try:
        manager.run_interactive_manager()
        return 0
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
