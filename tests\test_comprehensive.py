"""
Test Suite für MineExtractorWeb v1.0
Comprehensive Testing für alle Hauptkomponenten
"""

import unittest
import asyncio
import os
import sys
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class TestMineDataModels(unittest.TestCase):
    """Tests für Mine Data Models"""
    
    def setUp(self):
        from mine_data_models import MineDataFields, MineResearchResult
        self.MineDataFields = MineDataFields
        self.MineResearchResult = MineResearchResult
    
    def test_mine_data_fields_creation(self):
        """Test MineDataFields creation and basic functionality"""
        
        mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv",
            restaurationskosten_cad="1000000"
        )
        
        self.assertEqual(mine.name, "Test Mine")
        self.assertEqual(mine.betreiber, "Test Company")
        self.assertEqual(mine.aktivitaetsstatus, "aktiv")
        self.assertEqual(mine.restaurationskosten_cad, "1000000")
        
        # Test auto-generated ID
        self.assertTrue(mine.id)
        self.assertIsInstance(mine.id, str)
    
    def test_mine_data_completion_rate(self):
        """Test completion rate calculation"""
        
        # Empty mine
        empty_mine = self.MineDataFields()
        self.assertEqual(empty_mine.get_completion_rate(), 0.0)
        
        # Partially filled mine
        partial_mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv"
        )
        completion_rate = partial_mine.get_completion_rate()
        self.assertGreater(completion_rate, 0.0)
        self.assertLess(completion_rate, 1.0)
    
    def test_mine_data_validation(self):
        """Test mine data validation"""
        
        # Valid mine
        valid_mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv",
            restaurationskosten_cad="1000000"
        )
        self.assertTrue(valid_mine.is_valid_mine_data())
        
        # Invalid mine (only name)
        invalid_mine = self.MineDataFields(name="Test Mine")
        self.assertFalse(invalid_mine.is_valid_mine_data())
    
    def test_csv_conversion(self):
        """Test CSV conversion"""
        
        mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv"
        )
        
        csv_dict = mine.to_dict()
        self.assertIn('Name', csv_dict)
        self.assertIn('Betreiber', csv_dict)
        self.assertIn('Aktivitätsstatus', csv_dict)
        self.assertEqual(csv_dict['Name'], "Test Mine")
        self.assertEqual(csv_dict['Betreiber'], "Test Company")
    
    def test_research_result(self):
        """Test MineResearchResult functionality"""
        
        mine = self.MineDataFields(name="Test Mine", betreiber="Test Company")
        result = self.MineResearchResult(mine_data=mine)
        
        # Test source addition
        result.add_source("https://example.com", "Test Source", "government", 0.9)
        self.assertEqual(len(result.sources), 1)
        self.assertEqual(result.sources[0].url, "https://example.com")
        
        # Test error logging
        result.add_error("Test error")
        self.assertEqual(len(result.errors), 1)
        self.assertIn("Test error", result.errors[0])

class TestDataParser(unittest.TestCase):
    """Tests für Data Parser"""
    
    def setUp(self):
        from data_parser import MiningDataParser
        self.parser = MiningDataParser()
    
    def test_operator_extraction(self):
        """Test operator extraction"""
        
        test_content = """
        The Éléonore mine is operated by Newmont Corporation.
        The company has been operating the mine since 2014.
        """
        
        operator = self.parser._extract_operator(test_content)
        self.assertIn("Newmont", operator)
    
    def test_status_extraction(self):
        """Test status extraction"""
        
        # Active mine
        active_content = "The mine is currently active and in production."
        status = self.parser._extract_status(active_content)
        self.assertEqual(status, "aktiv")
        
        # Closed mine
        closed_content = "The mine was closed in 2020 and is no longer operational."
        status = self.parser._extract_status(closed_content)
        self.assertEqual(status, "geschlossen")
    
    def test_restoration_costs_extraction(self):
        """Test restoration costs extraction"""
        
        test_cases = [
            ("Restoration costs are estimated at $45.2 million CAD", "45200000"),
            ("Environmental closure costs: CAD $12.5M", "12500000"),
            ("Decommissioning costs of 25 million CAD", "25000000")
        ]
        
        for content, expected in test_cases:
            cost = self.parser._extract_restoration_costs(content)
            self.assertEqual(cost, expected, f"Failed for content: {content}")
    
    def test_coordinates_extraction(self):
        """Test GPS coordinates extraction"""
        
        test_content = "The mine is located at coordinates 53.4°N, 76.8°W in Quebec."
        coordinates = self.parser._extract_coordinates(test_content)
        
        self.assertEqual(len(coordinates), 2)
        self.assertTrue(coordinates[0])  # x-coordinate (longitude)
        self.assertTrue(coordinates[1])  # y-coordinate (latitude)
    
    def test_commodity_extraction(self):
        """Test commodity extraction"""
        
        gold_content = "This is a gold mine producing gold ore."
        commodity = self.parser._extract_commodity(gold_content)
        self.assertEqual(commodity, "Gold")
        
        copper_content = "The mine extracts copper and copper ore."
        commodity = self.parser._extract_commodity(copper_content)
        self.assertEqual(commodity, "Kupfer")
    
    def test_complete_parsing(self):
        """Test complete parsing workflow"""
        
        sample_response = {
            'content': """
            Éléonore mine is operated by Newmont Corporation. The mine is currently active.
            Environmental restoration costs are estimated at $45.2 million CAD as of 2023.
            This is an underground gold mine with production starting in 2014.
            The mine covers approximately 125 hectares.
            Coordinates: 53.4°N, 76.8°W
            """,
            'citations': [
                {'url': 'https://example.com', 'title': 'Test Source'}
            ]
        }
        
        mine_data = self.parser.parse_api_response(sample_response, "Éléonore", "test")
        
        self.assertEqual(mine_data.name, "Éléonore")
        self.assertIn("Newmont", mine_data.betreiber)
        self.assertEqual(mine_data.aktivitaetsstatus, "aktiv")
        self.assertEqual(mine_data.restaurationskosten_cad, "45200000")
        self.assertEqual(mine_data.rohstoffabbau, "Gold")
        self.assertEqual(mine_data.minentyp, "Untertage")

class TestPerplexityClient(unittest.TestCase):
    """Tests für Perplexity Client"""
    
    def setUp(self):
        with patch.dict(os.environ, {'PERPLEXITY_API_KEY': 'test-key'}):
            from perplexity_client import PerplexityClient
            self.PerplexityClient = PerplexityClient
    
    def test_client_initialization(self):
        """Test client initialization"""
        
        client = self.PerplexityClient("test-key")
        self.assertEqual(client.api_key, "test-key")
        self.assertTrue(client.base_url)
        self.assertTrue(client.model)
    
    def test_payload_building(self):
        """Test API payload building"""
        
        client = self.PerplexityClient("test-key")
        prompt = "Test prompt"
        payload = client._build_payload(prompt)
        
        self.assertIn('model', payload)
        self.assertIn('messages', payload)
        self.assertIn('temperature', payload)
        self.assertEqual(payload['messages'][0]['content'], prompt)
    
    @patch('aiohttp.ClientSession.post')
    async def test_api_request_success(self, mock_post):
        """Test successful API request"""
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            'choices': [{'message': {'content': 'Test response'}}],
            'citations': [],
            'usage': {'total_tokens': 100}
        }
        mock_post.return_value.__aenter__.return_value = mock_response
        
        client = self.PerplexityClient("test-key")
        
        async with client:
            result = await client.research_mine_comprehensive("Test Mine")
            
            self.assertTrue(result['success'])
            self.assertEqual(result['mine_name'], "Test Mine")
            self.assertIn('content', result)

class TestWebResearcher(unittest.TestCase):
    """Tests für Web Mining Researcher"""
    
    def setUp(self):
        from web_researcher import WebMiningResearcher
        self.WebMiningResearcher = WebMiningResearcher
        
        # Mock configuration
        self.config = {'perplexity_key': 'test-key'}
    
    def test_researcher_initialization(self):
        """Test researcher initialization"""
        
        researcher = self.WebMiningResearcher(self.config)
        self.assertEqual(researcher.config, self.config)
        self.assertTrue(researcher.api_clients)
        self.assertIn('perplexity', researcher.api_clients)
    
    def test_api_client_setup(self):
        """Test API client setup"""
        
        researcher = self.WebMiningResearcher(self.config)
        
        # Should have perplexity configured
        self.assertIn('perplexity', researcher.api_clients)
        self.assertTrue(researcher.api_clients['perplexity']['enabled'])
        self.assertEqual(researcher.api_clients['perplexity']['key'], 'test-key')
    
    def test_mine_name_normalization(self):
        """Test mine name normalization"""
        
        researcher = self.WebMiningResearcher(self.config)
        
        # Test known mappings
        normalized = researcher._normalize_mine_name("eleonore")
        self.assertEqual(normalized, "Éléonore")
        
        normalized = researcher._normalize_mine_name("canadian malartic")
        self.assertEqual(normalized, "Canadian Malartic")
        
        # Test unknown name (should return as-is)
        normalized = researcher._normalize_mine_name("Unknown Mine")
        self.assertEqual(normalized, "Unknown Mine")
    
    @patch('src.perplexity_client.PerplexityClient')
    async def test_research_comprehensive(self, mock_client_class):
        """Test comprehensive research"""
        
        # Mock PerplexityClient
        mock_client = AsyncMock()
        mock_client.research_mine_comprehensive.return_value = {
            'success': True,
            'content': 'Test mining data',
            'citations': [],
            'confidence': 0.8,
            'response_time': 30.0
        }
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        researcher = self.WebMiningResearcher(self.config)
        result = await researcher.research_mine_comprehensive("Test Mine")
        
        self.assertIsNotNone(result)
        self.assertEqual(result.mine_data.name, "Test Mine")
        self.assertIn('perplexity', result.apis_used)

class TestIntegration(unittest.TestCase):
    """Integration Tests"""
    
    def test_import_all_modules(self):
        """Test that all modules can be imported without errors"""
        
        modules_to_test = [
            'mine_data_models',
            'data_parser', 
            'web_researcher'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
            except ImportError as e:
                self.fail(f"Failed to import {module_name}: {e}")
    
    def test_configuration_loading(self):
        """Test configuration loading"""
        
        try:
            from config.settings import ProjectSettings
            from config.api_keys import APIConfig
            
            # Test that settings load without errors
            self.assertTrue(hasattr(ProjectSettings, 'PROJECT_NAME'))
            self.assertTrue(hasattr(ProjectSettings, 'CSV_HEADERS'))
            
            # Test API config
            self.assertTrue(hasattr(APIConfig, 'PERPLEXITY_API_KEY'))
            
        except ImportError as e:
            self.fail(f"Failed to load configuration: {e}")
    
    def test_end_to_end_workflow(self):
        """Test end-to-end workflow with mocked components"""
        
        # This would test the complete workflow from API call to CSV output
        # Using mocked responses to avoid actual API calls
        pass

class TestUtilities(unittest.TestCase):
    """Tests für Utility Functions"""
    
    def test_quick_parse_function(self):
        """Test quick parse utility function"""
        
        from data_parser import quick_parse
        
        sample_content = """
        Test Mine is operated by Test Company.
        The mine is currently active.
        Restoration costs are $10 million CAD.
        """
        
        result = quick_parse(sample_content, "Test Mine")
        
        self.assertEqual(result.name, "Test Mine")
        self.assertIn("Test Company", result.betreiber)
        self.assertEqual(result.aktivitaetsstatus, "aktiv")

# Test Runner
def run_tests():
    """Führt alle Tests aus"""
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestMineDataModels,
        TestDataParser,
        TestPerplexityClient,
        TestWebResearcher,
        TestIntegration,
        TestUtilities
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

async def run_async_tests():
    """Führt asynchrone Tests aus"""
    
    print("🧪 Running async tests...")
    
    # Test async components
    async_test_classes = [TestPerplexityClient, TestWebResearcher]
    
    for test_class in async_test_classes:
        print(f"Testing {test_class.__name__}...")
        
        # Create instance and run async tests
        test_instance = test_class()
        test_instance.setUp()
        
        # Find async test methods
        async_methods = [method for method in dir(test_instance) 
                        if method.startswith('test_') and 
                        asyncio.iscoroutinefunction(getattr(test_instance, method))]
        
        for method_name in async_methods:
            try:
                method = getattr(test_instance, method_name)
                await method()
                print(f"  ✅ {method_name}")
            except Exception as e:
                print(f"  ❌ {method_name}: {e}")

if __name__ == "__main__":
    print("🧪 MineExtractorWeb Test Suite")
    print("=" * 50)
    
    # Run synchronous tests
    print("Running synchronous tests...")
    sync_success = run_tests()
    
    # Run asynchronous tests
    print("\nRunning asynchronous tests...")
    asyncio.run(run_async_tests())
    
    print("\n" + "=" * 50)
    if sync_success:
        print("✅ All synchronous tests passed!")
    else:
        print("❌ Some synchronous tests failed!")
    
    print("🏁 Test suite completed!")
