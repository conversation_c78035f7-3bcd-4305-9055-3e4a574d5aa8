"""
GUI Integration für MineExtractorWeb v2.0 - Enhanced Research
Erweitert bestehende MineExtractor GUI um Enhanced Web-Research-Funktionalität
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import threading
from typing import List, Dict, Callable, Optional
import pandas as pd
from datetime import datetime
import os
import sys
import json

# Flexible imports
try:
    from .web_researcher import WebMiningResearcher
    from .mine_data_models import MineDataFields, BatchResearchResult, MineResearchResult
    from .enhanced_web_researcher import EnhancedMiningResearcher
except ImportError as e:
    self.log(f"❌ ERROR: Primary import of EnhancedMiningResearcher failed: {e}", "error")
    self.log("Attempting fallback imports...", "warning")
    from web_researcher import WebMiningResearcher
    from mine_data_models import MineDataFields, BatchResearchResult, MineResearchResult
    try:
        from enhanced_web_researcher import EnhancedMiningResearcher
    except ImportError as e_fallback:
        self.log(f"❌ ERROR: Fallback import of EnhancedMiningResearcher also failed: {e_fallback}", "error")
        EnhancedMiningResearcher = None
# Nach dem Import überprüfen und loggen
#if EnhancedMiningResearcher:
#    self.log("✅ EnhancedMiningResearcher wurde erfolgreich geladen.", "info")
#else:
#    self.log("❌ EnhancedMiningResearcher konnte NICHT geladen werden. System wird auf Basic Research zurückfallen.", "error")

class EnhancedWebResearchGUI:
    """
    Enhanced Web Research GUI mit mehrstufiger Research-Engine
    """
    
    def __init__(self, parent_gui=None, parent_notebook=None):
        """
        Initialisiert Enhanced GUI
        """
        
        self.parent_gui = parent_gui
        self.parent_notebook = parent_notebook
        
        # Research engines
        self.web_researcher = None
        self.enhanced_researcher = None
        self.research_thread = None
        self.is_researching = False
        
        # Load settings
        try:
            from config.settings import ProjectSettings
            self.settings = ProjectSettings
        except ImportError:
            self.settings = None
        
        # Results storage
        self.current_results = None
        self.last_batch_result = None
        
        # GUI State Variables
        self.gui_vars_initialized = False
        
        # Setup GUI
        if parent_notebook:
            self._setup_gui_variables()
            self.setup_web_research_tab()
        else:
            self.setup_standalone_window()
    
    def _setup_gui_variables(self):
        """Initialisiert alle tkinter Variables"""
        
        # API Configuration
        self.api_key_perplexity = tk.StringVar()
        self.api_key_tavily = tk.StringVar()
        self.api_key_exa = tk.StringVar()
        
        # Load API keys from configuration
        self._load_api_keys_from_config()
        
        # Research Settings
        self.research_timeout = tk.StringVar(value="120")
        self.batch_delay = tk.StringVar(value="2")
        
        # Enhanced Research Options
        self.use_enhanced_research = tk.BooleanVar(value=True)
        self.max_phases = tk.StringVar(value="5")
        self.enable_cross_validation = tk.BooleanVar(value=True)
        
        # File paths
        self.output_file_web = tk.StringVar()
        self._set_default_output_path()
        
        # Progress tracking
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="✅ Ready for Enhanced Web Research")
        self.mines_processed = tk.StringVar(value="0 / 0")
        
        # Options
        self.save_detailed_json = tk.BooleanVar(value=True)
        self.save_sources_report = tk.BooleanVar(value=True)
        self.auto_open_results = tk.BooleanVar(value=True)
        self.focus_financial_data = tk.BooleanVar(value=True)
        
        self.template_file = tk.StringVar()
        # Default: Quebec-Vorlage
        self.template_file.set("C:/Temp/Minen/MineSitesQuebec_Vorlage.csv")
        self.num_mines_from_template = tk.StringVar(value="")  # leer = alle
    
    def _load_api_keys_from_config(self):
        """Lädt API Keys automatisch aus der Konfiguration"""
        try:
            from config.api_keys import APIConfig
            
            # Load API keys from config
            if APIConfig.PERPLEXITY_API_KEY:
                self.api_key_perplexity.set(APIConfig.PERPLEXITY_API_KEY)
                
            if APIConfig.TAVILY_API_KEY:
                self.api_key_tavily.set(APIConfig.TAVILY_API_KEY)
                
            if APIConfig.EXA_API_KEY:
                self.api_key_exa.set(APIConfig.EXA_API_KEY)
            
            # Auto-test APIs if keys are loaded (delayed to allow GUI setup)
            if hasattr(self, 'root') or (self.parent_gui and hasattr(self.parent_gui, 'root')):
                # Delay auto-test to allow GUI components to be created
                if hasattr(self, 'root'):
                    self.root.after(1000, self._auto_test_apis)
                elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                    self.parent_gui.root.after(1000, self._auto_test_apis)
            
            # Log successful loading
            keys_loaded = []
            if APIConfig.PERPLEXITY_API_KEY: keys_loaded.append("Perplexity")
            if APIConfig.TAVILY_API_KEY: keys_loaded.append("Tavily")
            if APIConfig.EXA_API_KEY: keys_loaded.append("Exa")
            
            if keys_loaded:
                keys_str = ", ".join(keys_loaded)
                if hasattr(self, 'log_text'):  # Only log if log_text exists
                    self.log(f"🔑 Loaded API keys: {keys_str}", "info")
            
        except ImportError as e:
            if hasattr(self, 'log_text'):
                self.log(f"⚠️ Could not load API configuration: {e}", "warning")
        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log(f"❌ Error loading API keys: {e}", "error")
    
    def setup_standalone_window(self):
        """Erstellt Enhanced Standalone-Fenster"""
        
        self.root = tk.Tk()
        self.root.title("MineExtractorWeb v2.0 - Enhanced Research")
        self.root.geometry("1200x900")
        
        # Setup GUI variables after root exists
        self._setup_gui_variables()
        self.gui_vars_initialized = True
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create enhanced web research tab
        self.setup_web_research_tab()
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Zentriert Fenster auf Bildschirm"""
        if hasattr(self, 'root'):
            self.root.update_idletasks()
            x = (self.root.winfo_screenwidth() - self.root.winfo_width()) // 2
            y = (self.root.winfo_screenheight() - self.root.winfo_height()) // 2
            self.root.geometry(f"+{x}+{y}")
    
    def setup_web_research_tab(self):
        """Erstellt Enhanced Web Research Tab"""
        
        # Determine parent notebook
        notebook = self.parent_notebook if self.parent_notebook else self.notebook
        
        # Create Enhanced Web Research Tab
        self.web_tab = ttk.Frame(notebook)
        self.test_tab = ttk.Frame(notebook)
        notebook.add(self.web_tab, text="🔍 Enhanced Research")
        notebook.add(self.test_tab, text="🧪 Tests")
        
        # Create scrollable frame
        self.setup_scrollable_content()
        
        # Test-Tab initialisieren
        self.setup_test_tab()
    
    def setup_scrollable_content(self):
        """Erstellt scrollbaren Content-Bereich"""
        
        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(self.web_tab, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.web_tab, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scrollable elements
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # Setup main content
        self.setup_main_content()
    
    def setup_main_content(self):
        """Erstellt Enhanced Hauptinhalt"""
        
        main_frame = self.scrollable_frame
        main_frame.columnconfigure(1, weight=1)
        
        current_row = 0
        
        # Enhanced Title Section
        current_row = self.setup_enhanced_title_section(main_frame, current_row)
        
        # API Configuration Section
        current_row = self.setup_api_config_section(main_frame, current_row)
        
        # Enhanced Options Section
        current_row = self.setup_enhanced_options_section(main_frame, current_row)
        
        # Input Section
        current_row = self.setup_input_section(main_frame, current_row)
        
        # Control Buttons
        current_row = self.setup_enhanced_control_buttons(main_frame, current_row)
        
        # Progress Section
        current_row = self.setup_enhanced_progress_section(main_frame, current_row)
        
        # Results Section
        current_row = self.setup_enhanced_results_section(main_frame, current_row)
    
    def setup_enhanced_title_section(self, parent, row):
        """Erstellt Enhanced Titel-Bereich"""
        
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(1, weight=1)
        
        # Enhanced title
        title_label = ttk.Label(title_frame, 
                               text="🔍 MineExtractorWeb v2.0 - Enhanced Research", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 5))
        
        # Enhanced subtitle
        subtitle_label = ttk.Label(title_frame,
                                  text="Multi-Phase Deep Mining Data Research • Source Documentation • Confidence Scoring",
                                  font=("Arial", 11), foreground="blue")
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # Enhanced features info
        features_label = ttk.Label(title_frame,
                                  text="✅ 5-Phase Research • 📚 Source Attribution • 🎯 Cross-Validation • 📊 Confidence Scoring",
                                  font=("Arial", 10), foreground="green")
        features_label.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        # Status indicator
        self.api_status_summary = ttk.Label(title_frame, text="❌ APIs nicht konfiguriert", 
                                           foreground="red", font=("Arial", 10, "bold"))
        self.api_status_summary.grid(row=3, column=0, columnspan=3)
        
        return row + 1
    
    def setup_api_config_section(self, parent, row):
        """Erstellt API-Konfiguration Section"""
        
        api_frame = ttk.LabelFrame(parent, text="🔑 API Configuration for Enhanced Research", padding="15")
        api_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        api_frame.columnconfigure(1, weight=1)
        
        # Perplexity API (Required for Enhanced Research)
        ttk.Label(api_frame, text="Perplexity AI (Required):", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        self.perplexity_entry = ttk.Entry(api_frame, textvariable=self.api_key_perplexity, 
                                         show="*", width=50, font=("Consolas", 9))
        self.perplexity_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.perplexity_test_btn = ttk.Button(api_frame, text="Test", 
                                            command=lambda: self.test_api_key('perplexity'))
        self.perplexity_test_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Status indicators
        self.perplexity_status = ttk.Label(api_frame, text="❌ Not configured", foreground="red")
        self.perplexity_status.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Tavily AI (Enhanced Government Data)
        ttk.Label(api_frame, text="Tavily AI (Enhanced Gov Data):", font=("Arial", 10)).grid(
            row=2, column=0, sticky=tk.W, pady=5)
        self.tavily_entry = ttk.Entry(api_frame, textvariable=self.api_key_tavily, 
                                     show="*", width=50, font=("Consolas", 9))
        self.tavily_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.tavily_test_btn = ttk.Button(api_frame, text="Test", 
                                        command=lambda: self.test_api_key('tavily'))
        self.tavily_test_btn.grid(row=2, column=2, padx=5, pady=5)
        
        # Tavily status indicator
        self.tavily_status = ttk.Label(api_frame, text="❌ Not configured", foreground="red")
        self.tavily_status.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Enhanced Research Info
        info_text = ("💡 Enhanced Research: Perplexity AI is required for multi-phase research. Tavily AI adds government database access.")
        ttk.Label(api_frame, text=info_text, foreground="gray", font=("Arial", 9)).grid(
            row=4, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        return row + 1
    
    def setup_enhanced_options_section(self, parent, row):
        """Erstellt Enhanced Options Section"""
        
        options_frame = ttk.LabelFrame(parent, text="🔍 Enhanced Research Options", padding="15")
        options_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        options_frame.columnconfigure(1, weight=1)
        
        # Enhanced Research Toggle
        ttk.Checkbutton(options_frame, text="🚀 Use Enhanced Multi-Phase Research", 
                       variable=self.use_enhanced_research,
                       command=self._toggle_enhanced_options).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # Enhanced Options (only visible when enhanced research is enabled)
        self.enhanced_options_frame = ttk.Frame(options_frame)
        self.enhanced_options_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(self.enhanced_options_frame, text="Research Phases:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.enhanced_options_frame, textvariable=self.max_phases, width=5).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.enhanced_options_frame, text="(1-5)").grid(row=0, column=2, sticky=tk.W, pady=2)
        
        ttk.Checkbutton(self.enhanced_options_frame, text="📊 Enable Cross-Validation", 
                       variable=self.enable_cross_validation).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        ttk.Checkbutton(self.enhanced_options_frame, text="📚 Save Sources Report", 
                       variable=self.save_sources_report).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        # Basic Research Settings
        basic_frame = ttk.Frame(options_frame)
        basic_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        basic_frame.columnconfigure(1, weight=1)
        
        ttk.Label(basic_frame, text="Timeout (s):").grid(row=0, column=0, sticky=tk.W, padx=(20, 5), pady=5)
        ttk.Entry(basic_frame, textvariable=self.research_timeout, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(basic_frame, text="Batch Delay (s):").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        ttk.Entry(basic_frame, textvariable=self.batch_delay, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Output file
        ttk.Label(basic_frame, text="Output CSV:").grid(row=1, column=0, sticky=tk.W, pady=5)
        output_entry = ttk.Entry(basic_frame, textvariable=self.output_file_web, width=60)
        output_entry.grid(row=1, column=1, columnspan=4, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(basic_frame, text="📂", command=self.browse_output_file).grid(row=1, column=5, pady=5)
        
        # Additional Options
        checkbox_frame = ttk.Frame(options_frame)
        checkbox_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        ttk.Checkbutton(checkbox_frame, text="💰 Focus on Financial Data", 
                       variable=self.focus_financial_data).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="💾 Save Detailed JSON", 
                       variable=self.save_detailed_json).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="📁 Auto-Open Results", 
                       variable=self.auto_open_results).pack(side=tk.LEFT)
        
        return row + 1
    
    def _toggle_enhanced_options(self):
        """Toggle Enhanced Options Visibility"""
        if self.use_enhanced_research.get():
            self.enhanced_options_frame.grid()
        else:
            self.enhanced_options_frame.grid_remove()
    
    def setup_input_section(self, parent, row):
        """Erstellt Mine Input Section"""
        
        input_frame = ttk.LabelFrame(parent, text="📝 Mine Names Input", padding="15")
        input_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        # Instructions
        instructions = """Enhanced Research: Enter mine names (one per line or comma-separated):
Examples: Éléonore, Canadian Malartic, Raglan, Casa Berardi, LaRonde, Mont Wright"""
        ttk.Label(input_frame, text=instructions, foreground="gray", font=("Arial", 9)).grid(
            row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Text Input with scrollbar
        text_frame = ttk.Frame(input_frame)
        text_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        text_frame.columnconfigure(0, weight=1)
        
        self.mine_input_text = tk.Text(text_frame, height=10, width=70, wrap=tk.WORD, 
                                      font=("Arial", 10))
        self.mine_input_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        input_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                                       command=self.mine_input_text.yview)
        input_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.mine_input_text.configure(yscrollcommand=input_scrollbar.set)
        
        # Quick fill buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="📋 Quebec Top 10", 
                  command=self.fill_quebec_top_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🥇 Gold Mines", 
                  command=self.fill_gold_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 Test Mines", 
                  command=self.fill_test_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ Clear", 
                  command=self.clear_mine_input).pack(side=tk.LEFT, padx=5)
        
        # Vorlage-Datei Auswahl
        ttk.Label(input_frame, text="Vorlage:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.template_file, width=50).grid(row=row, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(input_frame, text="📂", command=self.browse_template_file).grid(row=row, column=2, pady=5)
        row += 1
        # Anzahl Minen aus Vorlage
        ttk.Label(input_frame, text="Anzahl Minen aus Vorlage (leer = alle):").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(input_frame, textvariable=self.num_mines_from_template, width=10).grid(row=row, column=1, sticky=tk.W, padx=5, pady=5)
        row += 1
        # Button: Alle Minen aus Vorlage übernehmen
        ttk.Button(input_frame, text="Alle Minen aus Vorlage suchen", command=self.fill_mines_from_template).grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=5)
        row += 1
        
        return row + 1
    
    def setup_enhanced_control_buttons(self, parent, row):
        """Erstellt Enhanced Control Buttons"""
        
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        # Main action buttons
        self.start_button = ttk.Button(button_frame, text="🚀 Start Enhanced Research", 
                                      command=self.start_enhanced_research,
                                      style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Research", 
                                     command=self.stop_research, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Enhanced result buttons
        self.open_results_button = ttk.Button(button_frame, text="📁 Open Results", 
                                             command=self.open_results, state=tk.DISABLED)
        self.open_results_button.pack(side=tk.LEFT, padx=10)
        
        self.export_json_button = ttk.Button(button_frame, text="📊 Export Enhanced JSON", 
                                           command=self.export_enhanced_json, state=tk.DISABLED)
        self.export_json_button.pack(side=tk.LEFT, padx=5)
        
        self.view_sources_button = ttk.Button(button_frame, text="📚 View Sources", 
                                            command=self.view_sources_report, state=tk.DISABLED)
        self.view_sources_button.pack(side=tk.LEFT, padx=5)
        
        return row + 1
    
    def setup_enhanced_progress_section(self, parent, row):
        """Erstellt Enhanced Progress Section"""
        
        progress_frame = ttk.LabelFrame(parent, text="📊 Enhanced Research Progress", padding="15")
        progress_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=500, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Status labels
        status_info_frame = ttk.Frame(progress_frame)
        status_info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        status_info_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_info_frame, textvariable=self.status_var, 
                                     font=("Arial", 11, "bold"))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.mines_label = ttk.Label(status_info_frame, textvariable=self.mines_processed, 
                                    font=("Arial", 10))
        self.mines_label.grid(row=0, column=1, sticky=tk.E)
        
        # Enhanced metrics
        self.metrics_frame = ttk.Frame(progress_frame)
        self.metrics_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=5)
        
        self.data_quality_label = ttk.Label(self.metrics_frame, text="Data Quality: --", 
                                           font=("Arial", 9))
        self.data_quality_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.sources_count_label = ttk.Label(self.metrics_frame, text="Sources: --", 
                                            font=("Arial", 9))
        self.sources_count_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.confidence_label = ttk.Label(self.metrics_frame, text="Avg Confidence: --", 
                                         font=("Arial", 9))
        self.confidence_label.pack(side=tk.LEFT)

        # Log-Textfeld (ScrolledText)
        self.log_text = scrolledtext.ScrolledText(progress_frame, height=10, wrap=tk.WORD, font=("Consolas", 10))
        self.log_text.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        # Tag-Konfiguration für Farben
        self.log_text.tag_config("info", foreground="#333333")
        self.log_text.tag_config("success", foreground="#228B22")
        self.log_text.tag_config("error", foreground="#B22222")
        self.log_text.tag_config("warning", foreground="#DAA520")
        self.log_text.tag_config("enhanced", foreground="#1E90FF")

        # Enhanced log control buttons
        log_button_frame = ttk.Frame(progress_frame)
        log_button_frame.grid(row=5, column=0, pady=(10, 0))
        
        ttk.Button(log_button_frame, text="🗑️ Clear Log",
                   command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="📋 Copy Log",
                   command=self.copy_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="💾 Save Log",
                   command=self.save_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="📊 Show Statistics",
                   command=self.show_research_statistics).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="🐞 Show Debug Log",
                   command=self.show_debug_log).pack(side=tk.LEFT, padx=5)
        
        # Initial enhanced log message
        self.log("🔍 MineExtractorWeb v2.0 Enhanced Research Engine initialized", "enhanced")
        self.log("💡 Configure API keys and enable Enhanced Research for multi-phase analysis", "info")
        
        return row + 1
    
    def setup_enhanced_results_section(self, parent, row):
        """Platzhalter für zukünftige Ergebnis-Section (aktuell leer, verhindert Absturz)"""
        # Hier könnte später eine Ergebnis- oder Statistik-Section eingefügt werden
        # Aktuell nur ein leerer Frame zur Layout-Konsistenz
        results_frame = ttk.Frame(parent)
        results_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        return row + 1
    
    # ====================
    # Enhanced Functionality Methods
    # ====================
    
    def start_enhanced_research(self):
        """Startet Enhanced Multi-Phase Research (erzwingt Enhanced-Engine)"""
        # Validation
        if not self._validate_enhanced_inputs():
            return
        # UI Updates
        self.is_researching = True
        self._update_ui_for_research_start()
        # Parse mine names
        mine_names = self._parse_mine_names()
        # Erzwinge Enhanced-Engine
        self.use_enhanced_research.set(True)
        self.log(f"🚀 [FORCE] Enhanced Multi-Phase Research Engine wird erzwungen!", "enhanced")
        self.progress_var.set(0)
        self.mines_processed.set(f"0 / {len(mine_names)}")
        self.research_thread = threading.Thread(
            target=self._enhanced_research_thread, args=(mine_names,), daemon=True)
        self.research_thread.start()
    
    def _validate_enhanced_inputs(self) -> bool:
        """Validiert Enhanced Research Eingaben"""
        
        # API Key check
        if not self.api_key_perplexity.get().strip():
            messagebox.showerror("Error", "Perplexity API Key is required for Enhanced Research")
            return False
        
        # Enhanced research specific checks
        if self.use_enhanced_research.get():
            try:
                max_phases = int(self.max_phases.get())
                if max_phases < 1 or max_phases > 5:
                    messagebox.showerror("Error", "Research phases must be between 1 and 5")
                    return False
            except ValueError:
                messagebox.showerror("Error", "Invalid number of research phases")
                return False
        
        # Mine names check
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        if not mine_text:
            messagebox.showerror("Error", "Please enter mine names")
            return False
        
        return True
    
    def _enhanced_research_thread(self, mine_names: List[str]):
        """Enhanced Research in separatem Thread"""
        
        try:
            # Create config
            config = {
                'perplexity_key': self.api_key_perplexity.get().strip()
            }
            
            if self.api_key_tavily.get().strip():
                config['tavily_key'] = self.api_key_tavily.get().strip()
            
            timeout = int(self.research_timeout.get() or 120)
            batch_delay = float(self.batch_delay.get() or 2)
            
            # Run enhanced research
            if self.use_enhanced_research.get() and EnhancedMiningResearcher:
                batch_result = asyncio.run(self._run_enhanced_research(mine_names, config, timeout, batch_delay))
            else:
                batch_result = asyncio.run(self._run_basic_research(mine_names, config, timeout, batch_delay))
            
            # Save results
            self._save_enhanced_results(batch_result)
            
            # Success callback
            self._schedule_ui_update(lambda: self._research_completed_success(batch_result))
            
        except Exception as e:
            # Error callback
            error_msg = str(e)
            self._schedule_ui_update(lambda: self._research_completed_error(error_msg))
    
    async def _run_enhanced_research(self, mine_names: List[str], config: Dict[str, str], 
                                   timeout: int, batch_delay: float):
        """Führt Enhanced Multi-Phase Research durch"""
        
        self._schedule_ui_update(lambda: self.log("🔍 Initializing Enhanced Research Engine...", "enhanced"))
        
        async with EnhancedMiningResearcher(config) as enhanced_researcher:
            
            results = []
            total_mines = len(mine_names)
            
            for i, mine_name in enumerate(mine_names):
                if not self.is_researching:
                    break
                
                # Enhanced progress callback
                def enhanced_progress_callback(phase_progress, phase_status):
                    overall_progress = ((i / total_mines) * 100) + (phase_progress / total_mines)
                    status = f"Mine {i+1}/{total_mines}: {phase_status}"
                    self._schedule_ui_update(lambda: self._update_enhanced_progress(overall_progress, status))
                
                self._schedule_ui_update(lambda mn=mine_name: self.log(f"🔍 Starting enhanced research for {mn}...", "enhanced"))
                
                # Run enhanced research
                enhanced_result = await enhanced_researcher.research_mine_comprehensive(
                    mine_name
                )
                
                # Convert to compatible format
                compatible_result = self._convert_enhanced_result(enhanced_result)
                results.append(compatible_result)
                
                # Log enhanced results
                self._schedule_ui_update(
                    lambda r=enhanced_result: self.log(
                        f"✅ Enhanced research completed for {r.mine_name}: "
                        f"{len(r.data_points)} data points, "
                        f"{r.data_completeness_score:.1%} completeness, "
                        f"{len(r.all_sources)} sources", "success"
                    )
                )
                
                # Rate limiting
                if i < len(mine_names) - 1:
                    await asyncio.sleep(batch_delay)
            
            # Create enhanced batch result
            from .mine_data_models import BatchResearchResult
            
            batch_result = BatchResearchResult(
                results=results,
                batch_timestamp=datetime.now().isoformat(),
                total_duration=sum(r.research_duration for r in results),
                success_count=len([r for r in results if r.validation_status == "success"]),
                failure_count=len([r for r in results if r.validation_status != "success"])
            )
            
            self.last_enhanced_result = results[-1] if results else None
            
            return batch_result
    
    async def _run_basic_research(self, mine_names: List[str], config: Dict[str, str], 
                                timeout: int, batch_delay: float):
        """Fallback Basic Research"""
        
        self._schedule_ui_update(lambda: self.log("⚠️ Using basic research engine", "warning"))
        
        researcher = WebMiningResearcher(config)
        researcher.request_delay = batch_delay
        
        def progress_callback(progress: float, status: str):
            self._schedule_ui_update(lambda: self._update_enhanced_progress(progress, status))
        
        batch_result = await researcher.research_mine_list(mine_names, progress_callback)
        return batch_result
    
    def _convert_enhanced_result(self, enhanced_result):
        """Konvertiert Enhanced Result in kompatibles Format"""
        
        try:
            from .mine_data_models import MineDataFields, MineResearchResult
            from .mine_data_models import SourceMetadata
            
            # Create mine data from enhanced data points
            mine_data = MineDataFields()
            
            # Map enhanced fields to original fields
            field_mapping = {
                'mine_operator': 'betreiber',
                'production_start_year': 'produktionsbeginn',
                'production_end_year': 'produktionsende', 
                'annual_production_tonnes': 'foerdermenge_jahr',
                'mine_area_km2': 'flaeche_qkm',
                'restoration_costs_cad': 'restaurationskosten_cad',
                'commodity_type': 'rohstoffabbau',
                'mine_status': 'status',
                'coordinates_lat': 'koordinaten_lat',
                'coordinates_lon': 'koordinaten_lon',
                'mine_type': 'mine_typ'
            }
            
            # Convert enhanced data points
            for enhanced_field, original_field in field_mapping.items():
                if enhanced_field in enhanced_result.data_points:
                    data_point = enhanced_result.data_points[enhanced_field]
                    setattr(mine_data, original_field, data_point.value)
            
            mine_data.name = enhanced_result.mine_name
            
            # Create enhanced sources list
            sources = []
            for source in enhanced_result.all_sources:
                # Fallback für dict-Quellen (falls aus JSON geladen)
                if isinstance(source, dict):
                    sources.append(SourceMetadata(
                        url=source.get('url', ''),
                        title=source.get('title', ''),
                        date_accessed=source.get('date_accessed', ''),
                        source_type=source.get('content_type', ''),
                        confidence=source.get('reliability_score', 0.0)
                    ))
                else:
                    sources.append(SourceMetadata(
                        url=getattr(source, 'url', ''),
                        title=getattr(source, 'title', ''),
                        date_accessed=getattr(source, 'date_accessed', ''),
                        source_type=getattr(source, 'content_type', ''),
                        confidence=getattr(source, 'reliability_score', 0.0)
                    ))
            
            # Create compatible result with enhanced metadata
            compatible_result = MineResearchResult(
                mine_data=mine_data,
                sources=sources,
                research_duration=enhanced_result.total_research_time,
                data_completeness=enhanced_result.data_completeness_score,
                confidence_score=enhanced_result.data_completeness_score,
                validation_status="success" if enhanced_result.data_completeness_score > 0.3 else "partial"
            )
            # Metadaten als Attribut für die GUI speichern (nicht im Konstruktor)
            compatible_result.research_metadata = {
                'enhanced_research': True,
                'phases_completed': enhanced_result.research_phases_completed,
                'data_points_found': len(enhanced_result.data_points),
                'sources_found': len(enhanced_result.all_sources),
                'completeness_score': enhanced_result.data_completeness_score,
                'enhanced_result': enhanced_result,  # Store full enhanced result
                'debug_log': getattr(enhanced_result, 'debug_log', [])
            }
            
            return compatible_result
            
        except Exception as e:
            self.log(f"❌ Error converting enhanced result: {e}", "error")
            return None
    
    def _update_enhanced_progress(self, progress: float, status: str):
        """Aktualisiert Enhanced Progress"""
        
        self.progress_var.set(progress)
        self.status_var.set(status)
        
        # Update mines processed count
        if "(" in status and "/" in status:
            try:
                parts = status.split("(")[1].split(")")[0]
                self.mines_processed.set(parts)
            except:
                pass
    
    def _save_enhanced_results(self, batch_result):
        """Speichert Enhanced Results"""
        
        try:
            # Save main CSV
            self._save_enhanced_csv(batch_result)
            
            # Save detailed JSON if requested
            if self.save_detailed_json.get():
                self._save_enhanced_json(batch_result)
            
            # Save sources report if requested
            if self.save_sources_report.get():
                self._save_sources_report(batch_result)
            
            self.last_batch_result = batch_result
            
        except Exception as e:
            self.log(f"❌ Error saving enhanced results: {e}", "error")
            raise
    
    def _save_enhanced_csv(self, batch_result):
        """Speichert Enhanced CSV mit zusätzlichen Feldern"""
        
        output_path = self.output_file_web.get()
        
        rows = []
        for result in batch_result.results:
            row = result.mine_data.to_dict()
            
            # Add enhanced metadata if available
            if hasattr(result, 'research_metadata') and 'enhanced_research' in result.research_metadata:
                metadata = result.research_metadata
                row['data_completeness_score'] = f"{metadata.get('completeness_score', 0):.1%}"
                row['sources_count'] = metadata.get('sources_found', 0)
                row['phases_completed'] = len(metadata.get('phases_completed', []))
                row['research_type'] = 'Enhanced' if metadata.get('enhanced_research') else 'Basic'
            else:
                row['research_type'] = 'Basic'
            
            rows.append(row)
        
        df = pd.DataFrame(rows)
        
        # Save with enhanced encoding
        df.to_csv(output_path, index=False, encoding='utf-8-sig', sep='|')
        self.log(f"💾 Enhanced CSV results saved to: {output_path}", "success")
    
    def _save_enhanced_json(self, batch_result):
        """Speichert detaillierte Enhanced JSON"""
        
        output_path = self.output_file_web.get()
        json_path = output_path.replace('.csv', '_enhanced_detailed.json')
        
        enhanced_data = {
            'batch_info': {
                'timestamp': batch_result.batch_timestamp,
                'total_duration': batch_result.total_duration,
                'success_count': batch_result.success_count,
                'failure_count': batch_result.failure_count,
                'enhanced_research': True
            },
            'results': []
        }
        
        for result in batch_result.results:
            result_data = {
                'mine_data': result.mine_data.to_dict(),
                'sources': result.sources,
                'research_duration': result.research_duration,
                'data_completeness': result.data_completeness,
                'confidence_score': result.confidence_score,
                'validation_status': result.validation_status
            }
            
            # Add enhanced metadata if available
            if hasattr(result, 'research_metadata') and 'enhanced_result' in result.research_metadata:
                enhanced_result = result.research_metadata['enhanced_result']
                result_data['enhanced_details'] = {
                    'phases_completed': enhanced_result.research_phases_completed,
                    'data_points': {k: {
                        'value': v.value,
                        'confidence': v.confidence,
                        'sources_count': len(v.sources)
                    } for k, v in enhanced_result.data_points.items()},
                    'sources_by_type': {},
                    'debug_log': getattr(enhanced_result, 'debug_log', [])
                }
                
                # Group sources by type
                for source in enhanced_result.all_sources:
                    source_type = source.content_type
                    if source_type not in result_data['enhanced_details']['sources_by_type']:
                        result_data['enhanced_details']['sources_by_type'][source_type] = []
                    
                    result_data['enhanced_details']['sources_by_type'][source_type].append({
                        'url': source.url,
                        'title': source.title,
                        'reliability_score': source.reliability_score
                    })
            
            enhanced_data['results'].append(result_data)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, indent=2, ensure_ascii=False)
        
        self.log(f"📊 Enhanced JSON saved to: {json_path}", "success")
    
    def _save_sources_report(self, batch_result):
        """Erstellt detaillierten Sources Report"""
        
        output_path = self.output_file_web.get()
        sources_path = output_path.replace('.csv', '_sources_report.html')
        
        # Create HTML sources report
        html_content = self._generate_sources_html_report(batch_result)
        
        with open(sources_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.log(f"📚 Sources report saved to: {sources_path}", "success")
    
    def _generate_sources_html_report(self, batch_result) -> str:
        """Generiert HTML Sources Report"""
        
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Mining Research - Sources Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .mine { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; }
        .source { margin: 10px 0; padding: 10px; background: #f9f9f9; }
        .reliability-high { border-left: 4px solid #28a745; }
        .reliability-medium { border-left: 4px solid #ffc107; }
        .reliability-low { border-left: 4px solid #dc3545; }
        .metadata { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>🔍 Enhanced Mining Research - Sources Report</h1>
    <p>Generated: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
"""
        
        for result in batch_result.results:
            html += f"""
    <div class="mine">
        <h2>{result.mine_data.name}</h2>
        <div class="metadata">
            Sources: {len(result.sources)} | 
            Data Completeness: {result.data_completeness:.1%} | 
            Research Duration: {result.research_duration:.1f}s
        </div>
"""
            
            for i, source in enumerate(result.sources, 1):
                reliability = source.get('reliability_score', 0.5)
                reliability_class = 'reliability-high' if reliability > 0.8 else 'reliability-medium' if reliability > 0.6 else 'reliability-low'
                
                html += f"""
        <div class="source {reliability_class}">
            <strong>Source {i}: {source.get('title', 'Untitled')}</strong><br>
            <a href="{source.get('url', '#')}" target="_blank">{source.get('url', '')}</a><br>
            <div class="metadata">
                Type: {source.get('content_type', 'unknown')} | 
                Reliability: {reliability:.1%} | 
                Accessed: {source.get('date_accessed', 'unknown')}
            </div>
        </div>
"""
            
            html += "    </div>"
        
        html += """
</body>
</html>
"""
        
        return html
    
    # ====================
    # UI Event Handlers (Enhanced)
    # ====================
    
    def test_api_key(self, api_name: str):
        """Testet API Key (Enhanced)"""
        
        if api_name == 'perplexity':
            api_key = self.api_key_perplexity.get().strip()
            status_label = self.perplexity_status
        elif api_name == 'tavily':
            api_key = self.api_key_tavily.get().strip()
            status_label = self.tavily_status
        else:
            self.log(f"⚠️ {api_name.title()} API testing not implemented yet", "warning")
            return
        
        if not api_key:
            messagebox.showerror("Error", f"Please enter {api_name.title()} API Key")
            return
        
        self.log(f"🧪 Testing {api_name.title()} API for Enhanced Research...", "info")
        status_label.config(text="🔄 Testing...", foreground="orange")
        
        # Run test in separate thread
        threading.Thread(target=self._test_api_thread, 
                        args=(api_name, api_key, status_label), daemon=True).start()
    
    def _test_api_thread(self, api_name: str, api_key: str, status_label):
        """API Test in separatem Thread"""
        try:
            if api_name == 'perplexity':
                from .perplexity_client import test_api_key
                success = asyncio.run(test_api_key(api_key))
            elif api_name == 'tavily':
                from .tavily_client import test_api_key
                success = asyncio.run(test_api_key(api_key))
            else:
                success = False
            # Update GUI from main thread
            try:
                self._schedule_ui_update(lambda: self._api_test_completed(api_name, success, status_label))
            except RuntimeError as e:
                print(f"❌ Fehler beim Aktualisieren der GUI nach API-Test: {e}")
        except Exception as e:
            try:
                self._schedule_ui_update(lambda: self._api_test_failed(api_name, str(e), status_label))
            except RuntimeError as e2:
                print(f"❌ Fehler beim Aktualisieren der GUI nach API-Test-Fehler: {e2}")
    
    def _api_test_completed(self, api_name: str, success: bool, status_label):
        """Enhanced API Test abgeschlossen"""
        
        if success:
            status_label.config(text="✅ Ready for Enhanced Research", foreground="green")
            self.log(f"✅ {api_name.title()} API ready for Enhanced Research!", "success")
            self.update_api_status_summary()
        else:
            status_label.config(text="❌ Connection failed", foreground="red")
            self.log(f"❌ {api_name.title()} API test failed", "error")
    
    def _api_test_failed(self, api_name: str, error: str, status_label):
        """API Test fehlgeschlagen"""
        
        status_label.config(text="❌ Test failed", foreground="red")
        self.log(f"❌ {api_name.title()} API test failed: {error}", "error")
    
    def update_api_status_summary(self):
        """Aktualisiert Enhanced API Status Summary"""
        
        perplexity_configured = bool(self.api_key_perplexity.get().strip())
        tavily_configured = bool(self.api_key_tavily.get().strip())
        
        if perplexity_configured:
            if tavily_configured:
                self.api_status_summary.config(text="✅ Ready for Full Enhanced Research (Perplexity + Tavily)", foreground="green")
            else:
                self.api_status_summary.config(text="✅ Ready for Enhanced Research (Perplexity)", foreground="green")
        else:
            self.api_status_summary.config(text="❌ Please configure Perplexity API key for Enhanced Research", foreground="red")
    
    def _auto_test_apis(self):
        """Testet automatisch alle konfigurierten APIs beim Start"""
        try:
            # Only auto-test if GUI components exist
            if not hasattr(self, 'perplexity_status') or not hasattr(self, 'tavily_status'):
                return
            
            # Test Perplexity if configured
            if self.api_key_perplexity.get().strip():
                self.log("🧪 Auto-testing Perplexity API for Enhanced Research...", "info")
                threading.Thread(target=self._test_api_thread, 
                               args=('perplexity', self.api_key_perplexity.get().strip(), self.perplexity_status), 
                               daemon=True).start()
            
            # Test Tavily if configured
            if self.api_key_tavily.get().strip():
                self.log("🧪 Auto-testing Tavily API for Enhanced Research...", "info")
                threading.Thread(target=self._test_api_thread, 
                               args=('tavily', self.api_key_tavily.get().strip(), self.tavily_status), 
                               daemon=True).start()
            
            # Update status summary after delay
            if hasattr(self, 'root'):
                self.root.after(5000, self.update_api_status_summary)
            elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                self.parent_gui.root.after(5000, self.update_api_status_summary)
                
        except Exception as e:
            self.log(f"❌ Auto-test failed: {e}", "error")
    
    # ====================
    # Enhanced Result Actions
    # ====================
    
    def export_enhanced_json(self):
        """Exportiert Enhanced JSON Results"""
        
        if not self.last_batch_result:
            messagebox.showwarning("No Results", "No enhanced research results available to export")
            return
        
        try:
            self._save_enhanced_json(self.last_batch_result)
            messagebox.showinfo("Export Successful", "Enhanced JSON results exported successfully!")
        except Exception as e:
            messagebox.showerror("Export Failed", f"Failed to export enhanced JSON: {e}")
    
    def view_sources_report(self):
        """Zeigt Sources Report an"""
        
        if not self.last_batch_result:
            messagebox.showwarning("No Results", "No research results available")
            return
        
        try:
            output_path = self.output_file_web.get()
            sources_path = output_path.replace('.csv', '_sources_report.html')
            
            if os.path.exists(sources_path):
                if sys.platform.startswith('win'):
                    os.startfile(sources_path)
                elif sys.platform.startswith('darwin'):
                    os.system(f'open "{sources_path}"')
                else:
                    os.system(f'xdg-open "{sources_path}"')
                
                self.log(f"📚 Opened sources report: {os.path.basename(sources_path)}", "info")
            else:
                # Generate report if it doesn't exist
                self._save_sources_report(self.last_batch_result)
                self.view_sources_report()
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open sources report: {e}")
    
    def show_research_statistics(self):
        """Zeigt Enhanced Research Statistics"""
        
        if not self.last_batch_result:
            messagebox.showwarning("No Results", "No research results available")
            return
        
        # Create statistics window
        stats_window = tk.Toplevel(self.root if hasattr(self, 'root') else self.parent_gui.root)
        stats_window.title("Enhanced Research Statistics")
        stats_window.geometry("600x500")
        
        # Statistics content
        stats_text = scrolledtext.ScrolledText(stats_window, wrap=tk.WORD, font=("Consolas", 10))
        stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Generate statistics
        stats_content = self._generate_enhanced_statistics()
        stats_text.insert(1.0, stats_content)
        stats_text.config(state=tk.DISABLED)
    
    def _generate_enhanced_statistics(self) -> str:
        """Generiert Enhanced Statistics"""
        
        if not self.last_batch_result:
            return "No research results available."
        
        results = self.last_batch_result.results
        enhanced_results = [r for r in results if hasattr(r, 'research_metadata') and 'enhanced_research' in r.research_metadata]
        
        stats = f"""
🔍 Enhanced Mining Research Statistics
{'='*50}

📊 General Statistics:
• Total Mines Researched: {len(results)}
• Enhanced Research Results: {len(enhanced_results)}
• Basic Research Results: {len(results) - len(enhanced_results)}
• Success Rate: {self.last_batch_result.success_count}/{len(results)} ({self.last_batch_result.success_count/len(results)*100:.1f}%)
• Total Research Time: {self.last_batch_result.total_duration:.1f} seconds
• Average Time per Mine: {self.last_batch_result.total_duration/len(results):.1f} seconds

📈 Data Quality Metrics:
"""
        
        if enhanced_results:
            avg_completeness = sum(r.data_completeness for r in enhanced_results) / len(enhanced_results)
            avg_confidence = sum(r.confidence_score for r in enhanced_results) / len(enhanced_results)
            total_sources = sum(len(r.sources) for r in enhanced_results)
            
            stats += f"""• Average Data Completeness: {avg_completeness:.1%}
• Average Confidence Score: {avg_confidence:.1%}
• Total Sources Found: {total_sources}
• Average Sources per Mine: {total_sources/len(enhanced_results):.1f}

📚 Source Type Distribution:
"""
            
            source_types = {}
            for result in enhanced_results:
                for source in result.sources:
                    source_type = source.get('content_type', 'unknown')
                    source_types[source_type] = source_types.get(source_type, 0) + 1
            
            for source_type, count in sorted(source_types.items(), key=lambda x: x[1], reverse=True):
                stats += f"• {source_type.replace('_', ' ').title()}: {count} sources\n"
            
            # Enhanced research specific statistics
            if any('enhanced_result' in r.research_metadata for r in enhanced_results):
                stats += f"\n🔍 Enhanced Research Details:\n"
                
                phases_completed = []
                data_points_found = []
                
                for result in enhanced_results:
                    if 'enhanced_result' in result.research_metadata:
                        enhanced_result = result.research_metadata['enhanced_result']
                        phases_completed.append(len(enhanced_result.research_phases_completed))
                        data_points_found.append(len(enhanced_result.data_points))
                
                if phases_completed:
                    avg_phases = sum(phases_completed) / len(phases_completed)
                    avg_data_points = sum(data_points_found) / len(data_points_found)
                    
                    stats += f"• Average Research Phases Completed: {avg_phases:.1f}/5\n"
                    stats += f"• Average Data Points Found: {avg_data_points:.1f}\n"
        
        else:
            stats += "No enhanced research results available.\n"
        
        return stats
    
    # ====================
    # Common Methods (Enhanced Versions)
    # ====================
    
    def _set_default_output_path(self):
        """Setzt Enhanced Standard-Ausgabepfad"""
        if self.settings:
            default_path = self.settings.get_default_output_path()
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_path = f"enhanced_research_results_{timestamp}.csv"
        
        self.output_file_web.set(str(default_path))
    
    def fill_quebec_top_mines(self):
        """Füllt mit Quebec Top-Minen für Enhanced Research"""
        
        top_mines = [
            "Éléonore",
            "Canadian Malartic", 
            "Raglan",
            "Casa Berardi",
            "LaRonde",
            "Mont Wright",
            "Lac Tio",
            "Troilus",
            "Niobec",
            "Beaufor"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(top_mines))
        self.log(f"📝 Loaded {len(top_mines)} Quebec top mines for Enhanced Research", "info")
    
    def fill_gold_mines(self):
        """Füllt mit Gold-Minen"""
        
        gold_mines = [
            "Éléonore",
            "Canadian Malartic",
            "Casa Berardi", 
            "LaRonde",
            "Goldex",
            "Beaufor"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(gold_mines))
        self.log(f"📝 Loaded {len(gold_mines)} Quebec gold mines", "info")
    
    def fill_test_mines(self):
        """Füllt mit Test-Minen für Enhanced Research"""
        
        test_mines = [
            "Éléonore",
            "Canadian Malartic",
            "Raglan"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(test_mines))
        self.log(f"📝 Loaded {len(test_mines)} test mines for Enhanced Research", "info")
    
    def clear_mine_input(self):
        """Leert Eingabefeld"""
        
        self.mine_input_text.delete(1.0, tk.END)
        self.log("🗑️ Mine input cleared", "info")
    
    def browse_output_file(self):
        """Datei-Browser für Enhanced Output"""
        
        filename = filedialog.asksaveasfilename(
            title="Save Enhanced Research Results",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialname=os.path.basename(self.output_file_web.get())
        )
        
        if filename:
            self.output_file_web.set(filename)
            self.log(f"💾 Enhanced output file: {os.path.basename(filename)}", "info")
    
    def stop_research(self):
        """Stoppt Enhanced Research"""
        
        self.is_researching = False
        self.log("⏹️ Stopping enhanced research...", "warning")
        self.status_var.set("⏹️ Stopping research...")
    
    def open_results(self):
        """Öffnet Enhanced Results"""
        
        output_path = self.output_file_web.get()
        if os.path.exists(output_path):
            try:
                if sys.platform.startswith('win'):
                    os.startfile(output_path)
                elif sys.platform.startswith('darwin'):
                    os.system(f'open "{output_path}"')
                else:
                    os.system(f'xdg-open "{output_path}"')
                
                self.log(f"📁 Opened enhanced results: {os.path.basename(output_path)}", "info")
            except Exception as e:
                messagebox.showinfo("File Location", f"Enhanced results saved to:\n{output_path}")
        else:
            messagebox.showerror("File Not Found", f"Enhanced results file not found:\n{output_path}")
    
    def _parse_mine_names(self) -> List[str]:
        """Parst Mine-Namen für Enhanced Research"""
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        # Split by newlines or commas
        if '\n' in mine_text:
            mine_names = [name.strip() for name in mine_text.split('\n')]
        else:
            mine_names = [name.strip() for name in mine_text.split(',')]
        # Filter empty names
        mine_names = [name for name in mine_names if name]
        return mine_names
    
    def _update_ui_for_research_start(self):
        """Updates UI für Enhanced Research Start"""
        
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.open_results_button.config(state=tk.DISABLED)
        self.export_json_button.config(state=tk.DISABLED)
        self.view_sources_button.config(state=tk.DISABLED)
        
        if self.use_enhanced_research.get():
            self.status_var.set("🚀 Starting Enhanced Multi-Phase Research...")
        else:
            self.status_var.set("🔍 Starting Basic Research...")
    
    def _research_completed_success(self, batch_result):
        """Enhanced Research erfolgreich abgeschlossen"""
        
        self.is_researching = False
        self._update_ui_for_research_end()
        
        self.progress_var.set(100)
        self.status_var.set("✅ Enhanced Research completed!")
        self.mines_processed.set(f"{len(batch_result.results)} / {len(batch_result.results)}")
        
        # Enhanced logging
        enhanced_count = len([r for r in batch_result.results if hasattr(r, 'research_metadata') and 'enhanced_research' in r.research_metadata])
        
        self.log(f"🎉 Enhanced Research completed successfully!", "enhanced")
        self.log(f"📊 Results: {batch_result.success_count}/{len(batch_result.results)} mines successful", "success")
        if enhanced_count > 0:
            self.log(f"🔍 Enhanced Research: {enhanced_count} mines with multi-phase analysis", "enhanced")
        self.log(f"⏱️ Total time: {batch_result.total_duration:.1f}s", "info")
        
        # Update enhanced metrics
        if enhanced_count > 0:
            enhanced_results = [r for r in batch_result.results if hasattr(r, 'research_metadata') and 'enhanced_research' in r.research_metadata]
            avg_completeness = sum(r.data_completeness for r in enhanced_results) / len(enhanced_results)
            total_sources = sum(len(r.sources) for r in enhanced_results)
            
            self.data_quality_label.config(text=f"Data Quality: {avg_completeness:.1%}")
            self.sources_count_label.config(text=f"Sources: {total_sources}")
            self.confidence_label.config(text=f"Avg Confidence: {avg_completeness:.1%}")
        
        # Auto-open results if requested
        if self.auto_open_results.get():
            self.open_results()
        
        # Show enhanced completion message
        messagebox.showinfo("Enhanced Research Completed", 
                           f"Enhanced Research completed!\n\n"
                           f"✅ Successful: {batch_result.success_count}/{len(batch_result.results)} mines\n"
                           f"🔍 Enhanced Results: {enhanced_count}\n"
                           f"⏱️ Duration: {batch_result.total_duration:.1f} seconds\n\n"
                           f"Results saved to:\n{os.path.basename(self.output_file_web.get())}")
    
    def _research_completed_error(self, error: str):
        """Enhanced Research mit Fehler abgebrochen"""
        
        self.is_researching = False
        self._update_ui_for_research_end()
        
        self.progress_var.set(0)
        self.status_var.set("❌ Enhanced Research failed")
        
        self.log(f"❌ Enhanced Research failed: {error}", "error")
        messagebox.showerror("Enhanced Research Failed", f"Error: {error}")
    
    def _update_ui_for_research_end(self):
        """Updates UI für Enhanced Research Ende"""
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.open_results_button.config(state=tk.NORMAL)
        self.export_json_button.config(state=tk.NORMAL)
        self.view_sources_button.config(state=tk.NORMAL)
    
    def _schedule_ui_update(self, update_func: Callable):
        """Plant UI-Update im Hauptthread"""
        
        if hasattr(self, 'root'):
            self.root.after(0, update_func)
        elif self.parent_gui and hasattr(self.parent_gui, 'root'):
            self.parent_gui.root.after(0, update_func)
    
    def log(self, message: str, level: str = "info"):
        """Enhanced Logging mit mehr Kategorien"""
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # Insert with appropriate tag for coloring
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)
        
        # Update GUI
        self._update_gui()
    
    def _update_gui(self):
        """Aktualisiert Enhanced GUI"""
        
        if hasattr(self, 'root'):
            self.root.update_idletasks()
        elif self.parent_gui and hasattr(self.parent_gui, 'root'):
            self.parent_gui.root.update_idletasks()
    
    def clear_log(self):
        """Leert Enhanced Log"""
        
        self.log_text.delete(1.0, tk.END)
        self.log("📝 Enhanced log cleared", "info")
    
    def copy_log(self):
        """Kopiert Enhanced Log"""
        
        log_content = self.log_text.get(1.0, tk.END)
        root = self.root if hasattr(self, 'root') else self.parent_gui.root
        root.clipboard_clear()
        root.clipboard_append(log_content)
        self.log("📋 Enhanced log copied to clipboard", "info")
    
    def save_log(self):
        """Speichert Enhanced Log"""
        
        filename = filedialog.asksaveasfilename(
            title="Save Enhanced Research Log",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                
                self.log(f"💾 Enhanced log saved to: {os.path.basename(filename)}", "success")
                messagebox.showinfo("Success", "Enhanced log saved successfully!")
            
            except Exception as e:
                self.log(f"❌ Failed to save enhanced log: {e}", "error")
                messagebox.showerror("Error", f"Failed to save log: {e}")
    
    def run_standalone(self):
        """Startet Enhanced Standalone GUI"""
        
        if hasattr(self, 'root'):
            print("🔍 Starting Enhanced MineExtractorWeb GUI...")
            self.root.mainloop()
        else:
            print("❌ No standalone window available")

    def browse_template_file(self):
        filename = filedialog.askopenfilename(
            title="Vorlage auswählen",
            filetypes=[("CSV files", "*.csv"), ("Alle Dateien", "*.*")],
            initialfile=self.template_file.get()
        )
        if filename:
            self.template_file.set(filename)
            self.log(f"📝 Neue Vorlage gewählt: {os.path.basename(filename)}", "info")

    def fill_mines_from_template(self):
        import pandas as pd
        try:
            # Versuche verschiedene Trennzeichen
            for sep in [';', '|', ',']:
                try:
                    df = pd.read_csv(self.template_file.get(), sep=sep)
                    if len(df.columns) > 1:
                        break
                except Exception:
                    continue
            # Extrahiere Minennamen
            if 'Name' in df.columns:
                names = df['Name'].dropna().astype(str).tolist()
            else:
                # Versuche zweite Spalte (Index 1)
                if len(df.columns) > 1:
                    names = df.iloc[:,1].dropna().astype(str).tolist()
                else:
                    # Fallback: erste Spalte
                    names = df.iloc[:,0].dropna().astype(str).tolist()
            # Filtere leere oder zu kurze Namen
            names = [n.strip() for n in names if n.strip() and len(n.strip()) > 1]
            n = self.num_mines_from_template.get()
            if n.strip().isdigit():
                names = names[:int(n.strip())]
            self.mine_input_text.delete(1.0, tk.END)
            self.mine_input_text.insert(1.0, "\n".join(names))
            self.log(f"📝 {len(names)} Minen aus Vorlage übernommen", "info")
        except Exception as e:
            self.log(f"❌ Fehler beim Lesen der Vorlage: {e}", "error")

    def setup_test_tab(self):
        # Test-Tab mit Buttons für alle Tests
        frame = self.test_tab
        ttk.Label(frame, text="Testfunktionen", font=("Arial", 14, "bold")).pack(pady=10)
        ttk.Button(frame, text="Alle Tests ausführen", command=self.run_all_tests).pack(pady=5)
        ttk.Button(frame, text="API-Verbindung testen", command=self.run_api_tests).pack(pady=5)
        ttk.Button(frame, text="Parser-Tests", command=self.run_parser_tests).pack(pady=5)
        ttk.Button(frame, text="Performance-Test", command=self.run_performance_test).pack(pady=5)
        self.test_log = scrolledtext.ScrolledText(frame, height=15, wrap=tk.WORD, font=("Consolas", 9), background="#f8f9fa", foreground="#333333")
        self.test_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def run_all_tests(self):
        self.test_log.insert(tk.END, "[TEST] Starte alle Tests...\n")
        self.run_api_tests()
        self.run_parser_tests()
        self.run_performance_test()
        self.test_log.insert(tk.END, "[TEST] Alle Tests abgeschlossen.\n")

    def run_api_tests(self):
        self.test_log.insert(tk.END, "[TEST] API-Verbindung wird getestet...\n")
        # Hier können die echten API-Tests integriert werden
        self.test_log.insert(tk.END, "[TEST] API-Test abgeschlossen.\n")

    def run_parser_tests(self):
        self.test_log.insert(tk.END, "[TEST] Parser-Test wird durchgeführt...\n")
        # Hier können Parser-Tests integriert werden
        self.test_log.insert(tk.END, "[TEST] Parser-Test abgeschlossen.\n")

    def run_performance_test(self):
        self.test_log.insert(tk.END, "[TEST] Performance-Test wird durchgeführt...\n")
        # Hier können Performance-Tests integriert werden
        self.test_log.insert(tk.END, "[TEST] Performance-Test abgeschlossen.\n")

    def show_debug_log(self):
        """Zeigt das Debug-Log der letzten Enhanced-Research-Session als Popup an."""
        if not hasattr(self, 'last_enhanced_result') or not self.last_enhanced_result:
            messagebox.showinfo("Debug Log", "Kein Debug-Log verfügbar.")
            return
        debug_log = getattr(self.last_enhanced_result, 'debug_log', None)
        if not debug_log:
            messagebox.showinfo("Debug Log", "Kein Debug-Log verfügbar.")
            return
        log_text = json.dumps(debug_log, indent=2, ensure_ascii=False)
        win = tk.Toplevel()
        win.title("Debug Log - Enhanced Research")
        txt = scrolledtext.ScrolledText(win, width=100, height=30, wrap=tk.WORD)
        txt.insert(tk.END, log_text)
        txt.pack(fill=tk.BOTH, expand=True)
        txt.config(state=tk.DISABLED)
        ttk.Button(win, text="Schließen", command=win.destroy).pack(pady=5)


# Enhanced Integration Functions
def create_enhanced_standalone_gui():
    """
    Erstellt Enhanced Standalone GUI
    
    Returns:
        EnhancedWebResearchGUI Instanz
    """
    
    print("🖥️ Creating Enhanced Web Research GUI...")
    
    try:
        enhanced_gui = EnhancedWebResearchGUI()
        print("✅ Enhanced GUI created successfully!")
        return enhanced_gui
        
    except Exception as e:
        print(f"❌ Enhanced GUI creation failed: {e}")
        raise


# Compatibility alias
def create_standalone_gui():
    """Compatibility function - creates enhanced GUI"""
    return create_enhanced_standalone_gui()


if __name__ == "__main__":
    # Test Enhanced GUI
    gui = create_enhanced_standalone_gui()
    gui.run_standalone()
