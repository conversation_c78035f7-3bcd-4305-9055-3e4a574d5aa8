# MineExtractorWeb v1.0 - Environment Configuration Template
# Copy this file to .env and add your actual API keys

# =============================================================================
# REQUIRED API KEYS
# =============================================================================

# Perplexity AI (Primary Research API) - REQUIRED for Phase 1
# Get your API key from: https://www.perplexity.ai/
# Cost: ~$20/month for Pro subscription
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# =============================================================================
# OPTIONAL API KEYS (Phase 2+ Implementation)
# =============================================================================

# Tavily AI (Government & Regulatory Data) - OPTIONAL
# Get your API key from: https://tavily.com/
# Cost: ~$20/month for Pro plan
TAVILY_API_KEY=your_tavily_api_key_here

# Exa.ai (Semantic Search) - OPTIONAL  
# Get your API key from: https://exa.ai/
# Cost: ~$29/month for Plus plan
EXA_API_KEY=your_exa_api_key_here

# =============================================================================
# SCRAPING SERVICES (Phase 3+ Implementation)
# =============================================================================

# Apify (Government Database Scraping) - OPTIONAL
# Get your API key from: https://apify.com/
# Cost: ~$49/month for Team plan
APIFY_API_KEY=your_apify_api_key_here

# ScrapingBee (JavaScript-Heavy Sites) - OPTIONAL
# Get your API key from: https://www.scrapingbee.com/
# Cost: ~$29/month for Startup plan
SCRAPINGBEE_API_KEY=your_scrapingbee_api_key_here

# FireCrawl (Modern SPA Scraping) - OPTIONAL
# Get your API key from: https://firecrawl.dev/
# Cost: ~$29/month for Pro plan
FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Logging Configuration
LOG_LEVEL=INFO
DEBUG_MODE=False

# Performance Settings
MAX_CONCURRENT_REQUESTS=3
REQUEST_TIMEOUT=120
BATCH_SIZE=5
RATE_LIMIT_DELAY=2

# Output Settings
DEFAULT_CSV_ENCODING=utf-8-sig
DEFAULT_CSV_DELIMITER=|
AUTO_BACKUP_RESULTS=True

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Cache Settings
ENABLE_RESPONSE_CACHE=True
CACHE_DURATION_HOURS=24
CACHE_DIRECTORY=cache

# Monitoring & Analytics
ENABLE_PERFORMANCE_MONITORING=True
ENABLE_ERROR_REPORTING=True
STATISTICS_RETENTION_DAYS=30

# Security Settings
ENABLE_API_KEY_ROTATION=False
ENCRYPT_STORED_CREDENTIALS=True

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Testing Configuration
ENABLE_TEST_MODE=False
USE_MOCK_APIS=False
TEST_DATA_DIRECTORY=tests/data

# Development Features
ENABLE_DEBUG_LOGGING=False
SAVE_RAW_RESPONSES=False
ENABLE_PROFILING=False

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================

# PDF MineExtractor Integration
EXISTING_PDF_SYSTEM_PATH=
AUTO_MERGE_WITH_PDF_RESULTS=False
BACKUP_PDF_RESULTS=True

# External Database Integration
DATABASE_URL=
ENABLE_DATABASE_SYNC=False

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env in the project root directory
# 2. Add your API keys (at minimum, PERPLEXITY_API_KEY is required)
# 3. Customize settings as needed
# 4. Test configuration with: python main.py --test-apis
# 5. Start the application with: python main.py

# =============================================================================
# API KEY SETUP GUIDE
# =============================================================================

# PERPLEXITY AI (Required):
# 1. Visit https://www.perplexity.ai/
# 2. Sign up for Pro subscription ($20/month)
# 3. Go to Settings > API
# 4. Generate new API key
# 5. Add key to PERPLEXITY_API_KEY above

# TAVILY AI (Optional):
# 1. Visit https://tavily.com/
# 2. Sign up for account
# 3. Choose Pro plan ($20/month)
# 4. Get API key from dashboard
# 5. Add key to TAVILY_API_KEY above

# EXA.AI (Optional):
# 1. Visit https://exa.ai/
# 2. Sign up for account  
# 3. Choose Plus plan ($29/month)
# 4. Get API key from settings
# 5. Add key to EXA_API_KEY above

# =============================================================================
# COST ESTIMATION
# =============================================================================

# Minimum Setup (Phase 1):
# - Perplexity AI Pro: $20/month
# - Total: $20/month for 100+ mine researches

# Recommended Setup (Phase 2):
# - Perplexity AI: $20/month
# - Tavily AI: $20/month  
# - Exa.ai: $29/month
# - Total: $69/month for comprehensive research

# Full Setup (Phase 3+):
# - All above APIs: $69/month
# - Apify: $49/month
# - ScrapingBee: $29/month
# - FireCrawl: $29/month
# - Total: $176/month for maximum coverage

# ROI Analysis:
# Manual research: ~2 hours × $50/hour = $100 per mine
# Automated system: $20-176/month for unlimited mines
# Break-even: 1-2 mines per month

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit .env file to version control
# - Keep API keys secure and rotate regularly
# - Use environment variables in production
# - Monitor API usage and billing
# - Enable two-factor authentication on API provider accounts

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common Issues:
# 1. "API key not found" - Check .env file location and key names
# 2. "Rate limit exceeded" - Increase RATE_LIMIT_DELAY value
# 3. "Timeout errors" - Increase REQUEST_TIMEOUT value
# 4. "Missing dependencies" - Run: pip install -r requirements.txt

# Support:
# - Check logs/ directory for detailed error information
# - Run: python main.py --config to verify setup
# - Run: python main.py --test-apis to test connections
