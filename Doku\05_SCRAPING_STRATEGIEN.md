# Scraping Strategien - MineExtractorWeb v1.0
**Spezialisierte Strategien für Government & Industry Data Extraction**

---

## 🎯 **SCRAPING OVERVIEW**

Scraping-Strategien ergänzen die API-basierte Research um direkte Extraktion aus strukturierten Datenquellen, insbesondere Regierungsdatenbanken und Industrie-Portalen, die nicht über APIs verfügbar sind.

### **Zielquellen-Kategorien:**
- **Government Databases:** Quebec MERN, SEDAR+, NRCAN
- **Industry Portals:** Mining Weekly, S&P Global, Northern Miner
- **Company Websites:** Operator-spezifische Daten
- **Financial Platforms:** TSX, SEC Filings

---

## 🏛️ **GOVERNMENT DATA SCRAPING**

### **1. Quebec MERN Database Scraping**

#### **Target: https://gestim.mines.gouv.qc.ca/**

```python
class QuebecMERNScraper:
    """
    Spezialisierter Scraper für Quebec Ministère des Ressources naturelles
    Extrahiert: Permits, Status, Coordinates, Environmental Data
    """
    
    def __init__(self, scraping_service='apify'):
        self.scraping_service = scraping_service
        self.base_url = "https://gestim.mines.gouv.qc.ca/MRN_GestimP_Presentation/"
        
        # Known data structures
        self.data_mappings = {
            'permits': {
                'selector': '.permit-info',
                'fields': ['permit_number', 'status', 'issue_date', 'expiry_date']
            },
            'mining_rights': {
                'selector': '.mining-rights-table tr',
                'fields': ['title_holder', 'area_hectares', 'commodity']
            },
            'environmental': {
                'selector': '.environmental-section',
                'fields': ['restoration_plan', 'bond_amount', 'compliance_status']
            }
        }
    
    async def scrape_mine_data(self, mine_name: str) -> Dict:
        """
        Hauptmethode für Quebec MERN Scraping
        """
        
        logger.info(f"Scraping Quebec MERN for: {mine_name}")
        
        try:
            # Step 1: Search for mine
            search_results = await self._search_mine_in_gestim(mine_name)
            
            if not search_results:
                logger.warning(f"No results found in MERN for {mine_name}")
                return {}
            
            # Step 2: Extract detailed data for each result
            detailed_data = {}
            for result in search_results[:3]:  # Top 3 results
                mine_data = await self._extract_mine_details(result['detail_url'])
                if mine_data:
                    detailed_data.update(mine_data)
            
            return detailed_data
            
        except Exception as e:
            logger.error(f"Quebec MERN scraping failed for {mine_name}: {e}")
            return {}
    
    async def _search_mine_in_gestim(self, mine_name: str) -> List[Dict]:
        """
        Durchsucht GESTIM-Database nach Mine
        """
        
        if self.scraping_service == 'apify':
            return await self._apify_gestim_search(mine_name)
        elif self.scraping_service == 'selenium':
            return await self._selenium_gestim_search(mine_name)
        else:
            return await self._scrapingbee_gestim_search(mine_name)
    
    async def _apify_gestim_search(self, mine_name: str) -> List[Dict]:
        """
        Apify-basierte GESTIM Suche
        """
        
        apify_input = {
            "searchTerm": mine_name,
            "maxResults": 10,
            "includeDetails": True,
            "extractStructuredData": True,
            "customSelectors": {
                "mineTitle": ".mine-title, .site-name",
                "operator": ".operator-name, .company",
                "status": ".status, .mine-status",
                "location": ".location, .coordinates",
                "permits": ".permit-table tr"
            }
        }
        
        # Execute Apify actor
        actor_run = await self._execute_apify_actor("quebec-mern-scraper", apify_input)
        
        return self._parse_apify_results(actor_run)
    
    async def _selenium_gestim_search(self, mine_name: str) -> List[Dict]:
        """
        Selenium-basierte GESTIM Suche (Fallback)
        """
        
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        
        try:
            # Navigate to GESTIM
            driver.get(self.base_url)
            
            # Wait for search form
            search_box = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "search-input"))
            )
            
            # Perform search
            search_box.clear()
            search_box.send_keys(mine_name)
            
            # Submit search
            search_button = driver.find_element(By.CLASS_NAME, "search-submit")
            search_button.click()
            
            # Wait for results
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-results"))
            )
            
            # Extract search results
            results = []
            result_elements = driver.find_elements(By.CLASS_NAME, "result-item")
            
            for element in result_elements:
                try:
                    result = {
                        'title': element.find_element(By.CLASS_NAME, "result-title").text,
                        'description': element.find_element(By.CLASS_NAME, "result-description").text,
                        'detail_url': element.find_element(By.TAG_NAME, "a").get_attribute("href")
                    }
                    results.append(result)
                except Exception as e:
                    logger.warning(f"Failed to extract result element: {e}")
                    continue
            
            return results
            
        finally:
            driver.quit()
    
    async def _extract_mine_details(self, detail_url: str) -> Dict:
        """
        Extrahiert detaillierte Minen-Daten von spezifischer GESTIM-Seite
        """
        
        detailed_data = {}
        
        try:
            # Scrape detail page
            page_content = await self._scrape_page_content(detail_url)
            
            # Extract structured data
            for data_type, mapping in self.data_mappings.items():
                extracted = self._extract_structured_data(page_content, mapping)
                if extracted:
                    detailed_data[data_type] = extracted
            
            # Extract key mining fields
            detailed_data.update({
                'operator': self._extract_operator_from_content(page_content),
                'restoration_costs': self._extract_restoration_costs(page_content),
                'coordinates': self._extract_coordinates_from_mern(page_content),
                'mine_status': self._extract_mine_status(page_content)
            })
            
        except Exception as e:
            logger.error(f"Failed to extract details from {detail_url}: {e}")
        
        return detailed_data
```

### **2. SEDAR+ Financial Data Scraping**

#### **Target: https://sedarplus.ca/**

```python
class SEDARPlusFinancialScraper:
    """
    SEDAR+ Canadian Company Filings Scraper
    Extrahiert: Financial Reports, Technical Reports, Material Changes
    """
    
    def __init__(self):
        self.base_url = "https://sedarplus.ca/"
        self.document_types = {
            'annual_report': 'Annual information form',
            'technical_report': 'Technical report',
            'material_change': 'Material change report',
            'financial_statements': 'Financial statements'
        }
    
    async def scrape_company_filings(self, company_name: str) -> Dict:
        """
        Scraped Unternehmens-Filings für Mining-relevante Informationen
        """
        
        logger.info(f"Scraping SEDAR+ for company: {company_name}")
        
        try:
            # Step 1: Search for company
            company_results = await self._search_company(company_name)
            
            if not company_results:
                return {}
            
            # Step 2: Get recent filings
            recent_filings = await self._get_recent_filings(
                company_results[0]['company_id'], 
                months=24
            )
            
            # Step 3: Extract mining-specific data from filings
            extracted_data = {}
            for filing in recent_filings:
                filing_data = await self._extract_mining_data_from_filing(filing)
                if filing_data:
                    extracted_data.update(filing_data)
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"SEDAR+ scraping failed for {company_name}: {e}")
            return {}
    
    async def _search_company(self, company_name: str) -> List[Dict]:
        """
        Sucht Unternehmen in SEDAR+ Database
        """
        
        search_payload = {
            "companyName": company_name,
            "maxResults": 10,
            "includeSubsidiaries": True
        }
        
        # Use ScrapingBee for JavaScript-heavy SEDAR+ site
        scraping_result = await self._scrapingbee_search(
            f"{self.base_url}search",
            search_payload
        )
        
        return self._parse_company_search_results(scraping_result)
    
    async def _extract_mining_data_from_filing(self, filing: Dict) -> Dict:
        """
        Extrahiert Mining-spezifische Daten aus Filing-Dokument
        """
        
        filing_content = await self._download_filing_content(filing['document_url'])
        
        if not filing_content:
            return {}
        
        extracted = {}
        
        # Extract restoration costs
        restoration_costs = self._extract_restoration_costs_from_filing(filing_content)
        if restoration_costs:
            extracted['restoration_costs'] = restoration_costs
        
        # Extract mine operational status
        operational_status = self._extract_operational_status(filing_content)
        if operational_status:
            extracted['operational_status'] = operational_status
        
        # Extract production data
        production_data = self._extract_production_data(filing_content)
        if production_data:
            extracted['production_data'] = production_data
        
        # Extract mine locations and properties
        mine_properties = self._extract_mine_properties(filing_content)
        if mine_properties:
            extracted['mine_properties'] = mine_properties
        
        return extracted
    
    def _extract_restoration_costs_from_filing(self, content: str) -> Optional[str]:
        """
        Extrahiert Restaurations-/Closure-Kosten aus Filing-Text
        """
        
        # Enhanced patterns for financial filings
        cost_patterns = [
            r'asset\s+retirement\s+obligations?\s*:?\s*\$?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)?',
            r'decommissioning\s+(?:costs?|liabilities?)\s*:?\s*\$?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)?',
            r'restoration\s+(?:costs?|provisions?)\s*:?\s*\$?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)?',
            r'closure\s+(?:costs?|provisions?)\s*:?\s*\$?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)?',
            r'environmental\s+(?:rehabilitation|remediation)\s*:?\s*\$?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)?'
        ]
        
        for pattern in cost_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # Convert to full number if millions
                try:
                    cost_value = float(match.replace(',', ''))
                    if 'million' in pattern.lower() or 'M' in pattern:
                        cost_value *= 1_000_000
                    
                    # Validate reasonable range
                    if 100_000 <= cost_value <= 10_000_000_000:
                        return str(int(cost_value))
                except ValueError:
                    continue
        
        return None
```

### **3. Industry Database Scraping**

#### **Mining Industry Portals**

```python
class IndustryPortalScraper:
    """
    Scraper für Mining Industry Websites
    Targets: Mining Weekly, Northern Miner, S&P Global Market Intelligence
    """
    
    def __init__(self):
        self.industry_sources = {
            'mining_weekly': {
                'base_url': 'https://www.miningweekly.com/',
                'search_endpoint': 'search',
                'data_focus': ['news', 'production_updates', 'company_reports']
            },
            'northern_miner': {
                'base_url': 'https://www.northernminer.com/',
                'search_endpoint': 'search',
                'data_focus': ['technical_analysis', 'exploration_updates', 'mine_profiles']
            },
            's_p_global': {
                'base_url': 'https://www.spglobal.com/marketintelligence/',
                'search_endpoint': 'search',
                'data_focus': ['financial_analysis', 'market_data', 'company_profiles']
            }
        }
    
    async def scrape_industry_data(self, mine_name: str, company_name: str = None) -> Dict:
        """
        Scraped Industry-Daten für spezifische Mine/Unternehmen
        """
        
        industry_data = {}
        
        for source_name, source_config in self.industry_sources.items():
            try:
                source_data = await self._scrape_industry_source(
                    source_name, source_config, mine_name, company_name
                )
                
                if source_data:
                    industry_data[source_name] = source_data
                    
            except Exception as e:
                logger.error(f"Industry scraping failed for {source_name}: {e}")
                continue
        
        return industry_data
    
    async def _scrape_industry_source(self, source_name: str, config: Dict, 
                                    mine_name: str, company_name: str) -> Dict:
        """
        Scraped spezifische Industry Source
        """
        
        # Construct search queries
        search_queries = [mine_name]
        if company_name:
            search_queries.append(company_name)
            search_queries.append(f"{mine_name} {company_name}")
        
        all_results = {}
        
        for query in search_queries:
            try:
                # Execute search
                search_results = await self._execute_industry_search(
                    config['base_url'], query
                )
                
                # Extract relevant data
                for result in search_results:
                    extracted_data = await self._extract_industry_article_data(
                        result, mine_name, source_name
                    )
                    
                    if extracted_data:
                        all_results.update(extracted_data)
                        
            except Exception as e:
                logger.warning(f"Search failed for query '{query}' on {source_name}: {e}")
                continue
        
        return all_results
    
    async def _extract_industry_article_data(self, article_result: Dict, 
                                           mine_name: str, source_name: str) -> Dict:
        """
        Extrahiert Mining-relevante Daten aus Industry Article
        """
        
        article_url = article_result.get('url')
        if not article_url:
            return {}
        
        # Download article content
        article_content = await self._download_article_content(article_url)
        
        if not article_content:
            return {}
        
        extracted_data = {}
        
        # Extract based on source specialty
        if source_name == 'mining_weekly':
            extracted_data.update(self._extract_production_updates(article_content))
            
        elif source_name == 'northern_miner':
            extracted_data.update(self._extract_technical_analysis(article_content))
            
        elif source_name == 's_p_global':
            extracted_data.update(self._extract_financial_analysis(article_content))
        
        # Add metadata
        if extracted_data:
            extracted_data['source_url'] = article_url
            extracted_data['source_date'] = article_result.get('publish_date')
            extracted_data['source_title'] = article_result.get('title')
        
        return extracted_data
```

---

## 🏢 **COMPANY WEBSITE SCRAPING**

### **Operator-Specific Data Extraction**

```python
class CompanyWebsiteScraper:
    """
    Scraped Mining Company Websites für operator-spezifische Daten
    Adaptiert sich automatisch an verschiedene Website-Strukturen
    """
    
    def __init__(self):
        # Known mining company website patterns
        self.company_patterns = {
            'newmont': {
                'base_url': 'https://www.newmont.com/',
                'operations_page': 'operations',
                'selectors': {
                    'mine_status': '.operation-status',
                    'production_data': '.production-stats',
                    'sustainability': '.sustainability-section'
                }
            },
            'agnico_eagle': {
                'base_url': 'https://www.agnicoeagle.com/',
                'operations_page': 'operations',
                'selectors': {
                    'mine_info': '.mine-profile',
                    'financial_data': '.financial-highlights'
                }
            },
            'iamgold': {
                'base_url': 'https://www.iamgold.com/',
                'operations_page': 'operations-and-development',
                'selectors': {
                    'operations': '.operation-card',
                    'sustainability': '.esg-section'
                }
            }
        }
    
    async def scrape_company_mine_data(self, company_name: str, mine_name: str) -> Dict:
        """
        Scraped Company Website für Mine-spezifische Daten
        """
        
        # Normalize company name
        company_key = self._normalize_company_name(company_name)
        
        if company_key not in self.company_patterns:
            # Generic company scraping
            return await self._generic_company_scraping(company_name, mine_name)
        
        # Specialized company scraping
        return await self._specialized_company_scraping(company_key, mine_name)
    
    async def _specialized_company_scraping(self, company_key: str, mine_name: str) -> Dict:
        """
        Spezialisiertes Scraping für bekannte Unternehmen
        """
        
        config = self.company_patterns[company_key]
        
        try:
            # Navigate to operations page
            operations_url = f"{config['base_url']}{config['operations_page']}"
            
            # Scrape operations page
            operations_content = await self._scrape_with_js_rendering(operations_url)
            
            # Find mine-specific section
            mine_section = self._find_mine_section(operations_content, mine_name)
            
            if not mine_section:
                logger.warning(f"Mine {mine_name} not found on {company_key} website")
                return {}
            
            # Extract mine data using company-specific selectors
            extracted_data = {}
            for data_type, selector in config['selectors'].items():
                data = self._extract_with_selector(mine_section, selector)
                if data:
                    extracted_data[data_type] = data
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Specialized scraping failed for {company_key}: {e}")
            return {}
    
    async def _generic_company_scraping(self, company_name: str, mine_name: str) -> Dict:
        """
        Generisches Company Scraping für unbekannte Unternehmen
        """
        
        try:
            # Search for company website
            company_url = await self._find_company_website(company_name)
            
            if not company_url:
                return {}
            
            # Scrape main website
            website_content = await self._scrape_with_js_rendering(company_url)
            
            # Look for operations/mines page
            operations_links = self._find_operations_links(website_content, company_url)
            
            for link in operations_links:
                try:
                    page_content = await self._scrape_with_js_rendering(link)
                    
                    # Check if mine is mentioned on this page
                    if self._mine_mentioned_on_page(page_content, mine_name):
                        # Extract relevant data
                        extracted_data = self._extract_mine_data_generic(page_content, mine_name)
                        if extracted_data:
                            extracted_data['source_url'] = link
                            return extracted_data
                            
                except Exception as e:
                    logger.warning(f"Failed to scrape {link}: {e}")
                    continue
            
            return {}
            
        except Exception as e:
            logger.error(f"Generic company scraping failed for {company_name}: {e}")
            return {}
    
    def _extract_mine_data_generic(self, content: str, mine_name: str) -> Dict:
        """
        Extrahiert Mine-Daten mit generischen Patterns
        """
        
        extracted = {}
        
        # Generic patterns for common data
        patterns = {
            'annual_production': [
                rf'{re.escape(mine_name)}.*?(\d+(?:,\d+)?)\s*(?:tonnes?|tons?|t)\s*(?:per\s+year|annually)',
                rf'annual\s+production.*?{re.escape(mine_name)}.*?(\d+(?:,\d+)?)\s*(?:tonnes?|tons?|t)'
            ],
            'mine_life': [
                rf'{re.escape(mine_name)}.*?mine\s+life.*?(\d+)\s*years?',
                rf'life\s+of\s+mine.*?{re.escape(mine_name)}.*?(\d+)\s*years?'
            ],
            'reserves': [
                rf'{re.escape(mine_name)}.*?reserves?.*?(\d+(?:\.\d+)?)\s*(?:million|M)\s*(?:tonnes?|tons?|oz)',
                rf'proven\s+(?:and\s+probable\s+)?reserves?.*?{re.escape(mine_name)}.*?(\d+(?:\.\d+)?)'
            ]
        }
        
        for data_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted[data_type] = matches[0]
                    break
        
        return extracted
```

---

## 🛡️ **ANTI-DETECTION & COMPLIANCE**

### **Ethical Scraping Framework**

```python
class EthicalScrapingFramework:
    """
    Stellt sicher, dass alle Scraping-Aktivitäten ethical und legal sind
    Implementiert Rate Limiting, Robots.txt Compliance, etc.
    """
    
    def __init__(self):
        self.rate_limits = {
            'government': 2,  # 2 seconds between requests
            'company': 3,     # 3 seconds between requests  
            'industry': 1,    # 1 second between requests
        }
        
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        
        self.session_managers = {}
    
    async def ethical_request(self, url: str, source_type: str = 'industry') -> Optional[str]:
        """
        Führt ethischen HTTP-Request durch mit Rate Limiting und Compliance
        """
        
        # Check robots.txt compliance
        if not await self._check_robots_txt_compliance(url):
            logger.warning(f"robots.txt prohibits scraping {url}")
            return None
        
        # Apply rate limiting
        await self._apply_rate_limit(source_type)
        
        # Use session with rotation
        session = self._get_session_for_domain(url)
        
        try:
            async with session.get(url, timeout=30) as response:
                if response.status == 200:
                    return await response.text()
                elif response.status == 429:  # Rate limited
                    logger.warning(f"Rate limited on {url}, backing off")
                    await asyncio.sleep(60)  # Wait 1 minute
                    return None
                else:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"Request failed for {url}: {e}")
            return None
    
    async def _check_robots_txt_compliance(self, url: str) -> bool:
        """
        Prüft robots.txt Compliance für URL
        """
        
        try:
            from urllib.robotparser import RobotFileParser
            from urllib.parse import urljoin, urlparse
            
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            robot_parser = RobotFileParser()
            robot_parser.set_url(robots_url)
            robot_parser.read()
            
            # Check if our user agent can fetch this URL
            user_agent = 'MineExtractorBot/1.0'
            return robot_parser.can_fetch(user_agent, url)
            
        except Exception:
            # If we can't check robots.txt, be conservative and allow
            return True
    
    async def _apply_rate_limit(self, source_type: str):
        """
        Appliziert Rate Limiting basierend auf Source Type
        """
        
        delay = self.rate_limits.get(source_type, 2)
        
        # Check last request time for this source type
        last_request_key = f"last_request_{source_type}"
        
        if hasattr(self, last_request_key):
            last_request = getattr(self, last_request_key)
            elapsed = time.time() - last_request
            
            if elapsed < delay:
                wait_time = delay - elapsed
                await asyncio.sleep(wait_time)
        
        # Update last request time
        setattr(self, last_request_key, time.time())
    
    def _get_session_for_domain(self, url: str) -> aiohttp.ClientSession:
        """
        Erstellt/wiederverwendet Session für spezifische Domain mit Rotation
        """
        
        from urllib.parse import urlparse
        domain = urlparse(url).netloc
        
        if domain not in self.session_managers:
            # Create new session with random user agent
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            timeout = aiohttp.ClientTimeout(total=30)
            connector = aiohttp.TCPConnector(limit=2)  # Limit concurrent connections
            
            self.session_managers[domain] = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout,
                connector=connector
            )
        
        return self.session_managers[domain]
```

---

## 📊 **SCRAPING PERFORMANCE MONITORING**

### **Performance & Success Tracking**

```python
class ScrapingPerformanceMonitor:
    """
    Überwacht Scraping-Performance und Success Rates
    Optimiert Strategien basierend auf historischen Daten
    """
    
    def __init__(self):
        self.performance_metrics = defaultdict(lambda: {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0,
            'last_success': None,
            'blocked_count': 0,
            'data_quality_scores': []
        })
    
    def log_scraping_attempt(self, source: str, url: str, success: bool, 
                           response_time: float, data_quality: float = None):
        """
        Protokolliert Scraping-Versuch für Performance-Analyse
        """
        
        metrics = self.performance_metrics[source]
        
        metrics['total_requests'] += 1
        
        if success:
            metrics['successful_requests'] += 1
            metrics['last_success'] = time.time()
            
            # Update average response time
            total_time = metrics['avg_response_time'] * (metrics['successful_requests'] - 1)
            metrics['avg_response_time'] = (total_time + response_time) / metrics['successful_requests']
            
            if data_quality is not None:
                metrics['data_quality_scores'].append(data_quality)
        else:
            metrics['failed_requests'] += 1
    
    def get_source_reliability(self, source: str) -> float:
        """
        Berechnet Zuverlässigkeits-Score für Scraping Source
        """
        
        metrics = self.performance_metrics[source]
        
        if metrics['total_requests'] == 0:
            return 0.5  # Neutral score for untested sources
        
        # Base success rate
        success_rate = metrics['successful_requests'] / metrics['total_requests']
        
        # Penalty for being blocked
        if metrics['blocked_count'] > 0:
            block_penalty = min(0.3, metrics['blocked_count'] * 0.1)
            success_rate -= block_penalty
        
        # Bonus for recent success
        if metrics['last_success']:
            hours_since_success = (time.time() - metrics['last_success']) / 3600
            if hours_since_success < 24:
                success_rate += 0.1  # Recent success bonus
        
        return max(0, min(1, success_rate))
    
    def should_retry_source(self, source: str) -> bool:
        """
        Entscheidet ob Source für Retry geeignet ist
        """
        
        reliability = self.get_source_reliability(source)
        
        # Don't retry sources with very low reliability
        if reliability < 0.2:
            return False
        
        # Don't retry if recently blocked
        metrics = self.performance_metrics[source]
        if metrics['blocked_count'] > 3:
            return False
        
        return True
    
    def optimize_scraping_strategy(self, mine_name: str) -> Dict:
        """
        Optimiert Scraping-Strategie basierend auf Performance-Daten
        """
        
        # Rank sources by reliability
        source_rankings = {}
        for source in self.performance_metrics:
            source_rankings[source] = self.get_source_reliability(source)
        
        # Sort by reliability
        sorted_sources = sorted(source_rankings.items(), key=lambda x: x[1], reverse=True)
        
        # Create optimized strategy
        strategy = {
            'primary_sources': [s[0] for s in sorted_sources[:3] if s[1] > 0.7],
            'secondary_sources': [s[0] for s in sorted_sources[3:6] if s[1] > 0.4],
            'avoid_sources': [s[0] for s in sorted_sources if s[1] < 0.2],
            'recommended_delays': self._calculate_optimal_delays()
        }
        
        return strategy
    
    def _calculate_optimal_delays(self) -> Dict:
        """
        Berechnet optimale Delays basierend auf Success Rates
        """
        
        delays = {}
        
        for source, metrics in self.performance_metrics.items():
            if metrics['blocked_count'] > 0:
                # Increase delay for sources that have been blocked
                base_delay = 5 + (metrics['blocked_count'] * 2)
            else:
                # Standard delay based on success rate
                success_rate = metrics['successful_requests'] / max(1, metrics['total_requests'])
                base_delay = max(1, int(3 * (1 - success_rate)))
            
            delays[source] = base_delay
        
        return delays
```

---

## 🔧 **SCRAPING ORCHESTRATION**

### **Unified Scraping Controller**

```python
class ScrapingOrchestrator:
    """
    Koordiniert alle Scraping-Aktivitäten für optimale Datenextraktion
    Integriert Government, Industry und Company Scraping
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Initialize scrapers
        self.quebec_mern = QuebecMERNScraper()
        self.sedar_plus = SEDARPlusFinancialScraper()
        self.industry_scraper = IndustryPortalScraper()
        self.company_scraper = CompanyWebsiteScraper()
        
        # Performance monitoring
        self.performance_monitor = ScrapingPerformanceMonitor()
        self.ethical_framework = EthicalScrapingFramework()
    
    async def execute_comprehensive_scraping(self, mine_name: str, 
                                           company_name: str = None) -> Dict:
        """
        Führt umfassendes Scraping für Mine durch alle verfügbaren Quellen
        """
        
        logger.info(f"Starting comprehensive scraping for {mine_name}")
        
        scraping_results = {}
        
        # Phase 1: Government Sources (highest priority)
        gov_results = await self._scrape_government_sources(mine_name)
        if gov_results:
            scraping_results['government'] = gov_results
        
        # Phase 2: Financial Sources (if company known)
        if company_name:
            financial_results = await self._scrape_financial_sources(company_name)
            if financial_results:
                scraping_results['financial'] = financial_results
        
        # Phase 3: Industry Sources
        industry_results = await self._scrape_industry_sources(mine_name, company_name)
        if industry_results:
            scraping_results['industry'] = industry_results
        
        # Phase 4: Company Sources (if company known)
        if company_name:
            company_results = await self._scrape_company_sources(company_name, mine_name)
            if company_results:
                scraping_results['company'] = company_results
        
        # Aggregate and validate results
        aggregated_results = self._aggregate_scraping_results(scraping_results)
        
        logger.info(f"Scraping completed for {mine_name}: {len(scraping_results)} source categories")
        
        return aggregated_results
    
    async def _scrape_government_sources(self, mine_name: str) -> Dict:
        """
        Scraped Government Sources für Mine
        """
        
        gov_results = {}
        
        # Quebec MERN
        try:
            mern_data = await self.quebec_mern.scrape_mine_data(mine_name)
            if mern_data:
                gov_results['quebec_mern'] = mern_data
        except Exception as e:
            logger.error(f"Quebec MERN scraping failed: {e}")
        
        # Add other government sources here
        # - NRCAN database
        # - Ontario MNDM
        # - Federal environmental databases
        
        return gov_results
    
    async def _scrape_financial_sources(self, company_name: str) -> Dict:
        """
        Scraped Financial Sources für Company
        """
        
        financial_results = {}
        
        # SEDAR+ Filings
        try:
            sedar_data = await self.sedar_plus.scrape_company_filings(company_name)
            if sedar_data:
                financial_results['sedar_plus'] = sedar_data
        except Exception as e:
            logger.error(f"SEDAR+ scraping failed: {e}")
        
        # Add other financial sources
        # - TSX company profiles
        # - SEC filings (for US-listed companies)
        # - Company annual reports
        
        return financial_results
    
    def _aggregate_scraping_results(self, scraping_results: Dict) -> Dict:
        """
        Aggregiert Scraping-Ergebnisse aus verschiedenen Quellen
        """
        
        aggregated = {}
        source_count = 0
        
        for source_category, category_results in scraping_results.items():
            for source_name, source_data in category_results.items():
                source_count += 1
                
                # Merge source data into aggregated results
                for field, value in source_data.items():
                    if field not in aggregated:
                        aggregated[field] = []
                    
                    aggregated[field].append({
                        'value': value,
                        'source': f"{source_category}_{source_name}",
                        'confidence': self._calculate_source_confidence(source_category, source_name)
                    })
        
        # Select best value for each field
        final_aggregated = {}
        for field, values in aggregated.items():
            if values:
                # Sort by confidence and select best
                best_value = max(values, key=lambda x: x['confidence'])
                final_aggregated[field] = best_value['value']
                final_aggregated[f"{field}_source"] = best_value['source']
        
        final_aggregated['_scraping_metadata'] = {
            'sources_used': source_count,
            'scraping_timestamp': time.time()
        }
        
        return final_aggregated
```

---

**Status:** ✅ Scraping Strategies Complete  
**Implementation Priority:** Phase 2+ (nach API-based Phase 1)  
**Compliance:** Fully ethical mit robots.txt Compliance und Rate Limiting  

*Diese Scraping-Strategien ergänzen die API-Research um direkte Extraktion aus strukturierten Regierungs- und Industrie-Datenbanken für maximale Datenabdeckung.*