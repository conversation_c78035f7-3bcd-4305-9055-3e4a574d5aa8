@echo off
setlocal enabledelayedexpansion
echo.
echo ========================================
echo   🔍 MINE EXTRACTOR WEB v2.0
echo   Enhanced Multi-Phase Research Engine
echo ========================================
echo.
echo ✅ 5-Phase Deep Research • 📚 Source Documentation • 🎯 Confidence Scoring
echo.

REM Suche nach verfügbarem Python - mehrere Fallbacks
set PYTHON_CMD=

REM 1. Versuche miniforge3 (bevorzugt für Kompatibilität)
if exist "C:\ProgramData\miniforge3\python.exe" (
    set PYTHON_CMD=C:\ProgramData\miniforge3\python.exe
    echo 🐍 Using miniforge3 Python
) else if exist "C:\Users\<USER>\miniforge3\python.exe" (
    set PYTHON_CMD=C:\Users\<USER>\miniforge3\python.exe
    echo 🐍 Using user miniforge3 Python
) else (
    REM 2. Versuche Anaconda/Miniconda
    if exist "C:\ProgramData\Anaconda3\python.exe" (
        set PYTHON_CMD=C:\ProgramData\Anaconda3\python.exe
        echo 🐍 Using Anaconda Python
    ) else if exist "C:\Users\<USER>\Anaconda3\python.exe" (
        set PYTHON_CMD=C:\Users\<USER>\Anaconda3\python.exe
        echo 🐍 Using user Anaconda Python
    ) else (
        REM 3. Versuche Standard Python im PATH
        python --version >nul 2>&1
        if not errorlevel 1 (
            set PYTHON_CMD=python
            echo 🐍 Using system Python
        ) else (
            REM 4. Versuche Python3 Kommando
            python3 --version >nul 2>&1
            if not errorlevel 1 (
                set PYTHON_CMD=python3
                echo 🐍 Using python3 command
            ) else (
                REM 5. Versuche py launcher (Windows)
                py --version >nul 2>&1
                if not errorlevel 1 (
                    set PYTHON_CMD=py
                    echo 🐍 Using py launcher
                ) else (
                    echo ❌ FEHLER: Kein Python gefunden!
                    echo.
                    echo 📥 Bitte installiere Python:
                    echo    • Option 1: https://www.python.org/downloads/
                    echo    • Option 2: https://github.com/conda-forge/miniforge
                    echo    • Option 3: Microsoft Store (python)
                    echo.
                    echo 💡 Nach Installation starte dieses Script erneut
                    pause
                    exit /b 1
                )
            )
        )
    )
)

REM Zeige Python-Version
echo ✅ Python gefunden: %PYTHON_CMD%
%PYTHON_CMD% --version
echo.

REM Prüfe ob Hauptsystem verfügbar ist
if not exist "main.py" (
    echo ❌ FEHLER: MineExtractorWeb System nicht gefunden!
    echo.
    echo 📁 Erwartete Dateien:
    echo    main.py - Haupt-Entry-Point
    echo    src\ - Quellcode-Verzeichnis
    echo    config\ - Konfiguration
    echo.
    echo 💡 Stelle sicher, dass du im richtigen Verzeichnis bist:
    echo    C:\Temp\Minen\MineExtractorWeb_v1\
    echo.
    pause
    exit /b 1
)

echo ✅ MineExtractorWeb System gefunden
echo.

REM Prüfe kritische Komponenten
if not exist "src\" (
    echo ❌ Quellcode-Verzeichnis 'src\' fehlt!
    pause
    exit /b 1
)

if not exist "config\" (
    echo ❌ Konfigurations-Verzeichnis 'config\' fehlt!
    pause
    exit /b 1
)

echo ✅ Alle kritischen Komponenten verfügbar
echo.

REM Prüfe Dependencies
echo 🔍 Prüfe kritische Dependencies...
%PYTHON_CMD% -c "import pandas, aiohttp, tkinter" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Wichtige Dependencies fehlen!
    echo.
    echo 🔧 Installiere Dependencies automatisch...
    %PYTHON_CMD% -m pip install pandas aiohttp python-dotenv requests tenacity colorama
    
    REM Teste erneut
    %PYTHON_CMD% -c "import pandas, aiohttp" >nul 2>&1
    if errorlevel 1 (
        echo ❌ Dependencies-Installation fehlgeschlagen
        echo 💡 Versuche trotzdem zu starten...
    ) else (
        echo ✅ Dependencies erfolgreich installiert!
    )
    echo.
) else (
    echo ✅ Alle Dependencies verfügbar
    echo.
)

REM Prüfe .env Datei
if not exist ".env" (
    echo ⚠️ .env Datei nicht gefunden
    echo   API Keys müssen in der GUI konfiguriert werden
    echo.
) else (
    echo ✅ .env Datei gefunden - API Keys sollten automatisch laden
    echo.
)

echo 🚀 Starte Enhanced MineExtractorWeb GUI...
echo.
echo 💡 Nach dem Start:
echo   • Enhanced API Keys werden automatisch aus .env geladen
echo   • Multi-Phase Research Engine verfügbar (6 Phasen statt 5)
echo   • Verbesserte Datenextraktion für ALLE fehlenden Felder
echo   • Vollständige Quellenangaben mit Zuverlässigkeitsbewertung
echo   • Cross-Validation und erweiterte Confidence Scoring
echo   • Spezielle Suche für Produktionsstart, -ende, Fördermenge, Fläche
echo   • Koordinaten-Extraktion und bessere Restaurationskosten-Suche
echo.

REM Starte GUI
echo 🖥️ Starte Hauptanwendung...
%PYTHON_CMD% main.py
if errorlevel 1 (
    echo.
    echo ❌ GUI-Start fehlgeschlagen
    goto TROUBLESHOOT
)

goto SUCCESS

:TROUBLESHOOT
echo.
echo 🔧 TROUBLESHOOTING...
echo.
echo 🛠️ Alternative Optionen:
echo.
echo    1. 🧪 System-Test:        %PYTHON_CMD% validate_system.py
echo    2. 🔑 API konfigurieren:  %PYTHON_CMD% tools\api_key_manager.py
echo    3. ⚙️  Konfiguration:     %PYTHON_CMD% main.py --config
echo    4. 🧪 API testen:         %PYTHON_CMD% main.py --test-apis
echo    5. 🎮 Demo ausführen:     %PYTHON_CMD% demos\quick_start_demo.py
echo.

set /p manual_choice="Wähle Option (1-5) oder Enter zum Beenden: "

if "%manual_choice%"=="1" (
    %PYTHON_CMD% validate_system.py
) else if "%manual_choice%"=="2" (
    %PYTHON_CMD% tools\api_key_manager.py
) else if "%manual_choice%"=="3" (
    %PYTHON_CMD% main.py --config
) else if "%manual_choice%"=="4" (
    %PYTHON_CMD% main.py --test-apis
) else if "%manual_choice%"=="5" (
    %PYTHON_CMD% demos\quick_start_demo.py
)

goto END

:SUCCESS
echo.
echo ✅ MineExtractorWeb erfolgreich gestartet!
echo.
echo 🎯 Fixes und Verbesserungen angewendet:
echo   • GUI lädt API Keys automatisch aus .env
echo   • Enhanced Research Engine mit 6 spezialisierten Phasen
echo   • Verbesserte Suchstrategien für fehlende Datenfelder
echo   • Vollständige Quellenangaben mit URLs und Zuverlässigkeitsbewertung
echo   • Strukturierte Datenextraktion mit Confidence Scoring
echo   • Cross-Validation zwischen verschiedenen Quellen
echo   • Spezielle Prompts für Produktionsstart, -ende, Fördermenge, Fläche
echo   • Koordinaten-Extraktion und Restaurationskosten-Suche
echo   • Automatische API-Tests beim Start
echo.

:END
echo.
echo 👋 MineExtractorWeb beendet
pause
