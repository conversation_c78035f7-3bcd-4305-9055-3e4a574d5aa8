"""
MineExtractorWeb v1.0 - Core Source Package
Web-based Mining Data Research and Extraction System
"""

__version__ = "1.0.0"
__author__ = "Claude Sonnet 4"
__description__ = "Multi-System Web Research Engine for Quebec Mining Data"

# Core imports für einfache Nutzung
try:
    from .web_researcher import WebMiningResearcher
    from .mine_data_models import MineData<PERSON>ields, MineResearchResult
    from .data_parser import MiningDataParser
except ImportError:
    from web_researcher import WebMiningResearcher
    from mine_data_models import MineDataFields, MineResearchResult
    from data_parser import MiningDataParser

# Availability check für optionale Dependencies
try:
    try:
        from .perplexity_client import PerplexityClient
    except ImportError:
        from perplexity_client import PerplexityClient
    HAS_PERPLEXITY = True
except ImportError:
    HAS_PERPLEXITY = False

try:
    try:
        from .gui_integration import WebResearchGUIExtension
    except ImportError:
        from gui_integration import WebResearchGUIExtension
    HAS_GUI = True
except ImportError:
    HAS_GUI = False

# API Status
def get_api_status():
    """Gibt Status aller verfügbaren APIs zurück"""
    from config.api_keys import APIConfig
    return APIConfig.get_api_status_summary()

def get_system_info():
    """Gibt System-Informationen zurück"""
    from config.settings import ProjectSettings
    
    info = {
        'version': __version__,
        'description': __description__,
        'project_info': ProjectSettings.get_project_info(),
        'has_perplexity': HAS_PERPLEXITY,
        'has_gui': HAS_GUI,
        'api_status': get_api_status()
    }
    
    return info

# Quick start function
def quick_start():
    """Gibt Quick Start Information aus"""
    print(f"🌐 {__description__}")
    print(f"📊 Version: {__version__}")
    print("\n" + get_api_status())
    
    if HAS_PERPLEXITY:
        print("\n✅ Ready for web research!")
        print("💡 Example usage:")
        print("   from src import WebMiningResearcher")
        print("   researcher = WebMiningResearcher({'perplexity_key': 'your-key'})")
        print("   result = await researcher.research_mine_comprehensive('Éléonore')")
    else:
        print("\n⚠️  Please configure API keys to start research")
        print("   See config/api_keys.py for setup instructions")

if __name__ == "__main__":
    quick_start()
