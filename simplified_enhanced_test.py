#!/usr/bin/env python3
"""
Simplified Enhanced Research Engine Test
Tests only the core functionality without full improvements
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_core_enhanced_research():
    """Testet die Kern-Enhanced Research Engine"""
    
    print("🔍 Simplified Enhanced Research Engine Test")
    print("=" * 50)
    
    try:
        # Import Enhanced Research Engine
        from enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        if not APIConfig.PERPLEXITY_API_KEY:
            print("❌ Perplexity API key required for testing")
            return False
        
        config = {
            'perplexity_key': APIConfig.PERPLEXITY_API_KEY
        }
        
        print("🧪 Testing with mine: Éléonore")
        
        async with EnhancedMiningResearcher(config) as researcher:
            
            def progress_callback(progress, status):
                print(f"   {progress:.0f}% - {status}")
            
            print("🏗️ Starting simplified enhanced research...")
            start_time = time.time()
            
            # Run only first phase to test basic functionality
            print("🔍 Testing Phase 1: Basic Mine Information")
            
            try:
                # Test only one phase
                phase_config = researcher.research_phases["basic_info"]
                mine_name = "Éléonore"
                
                phase_results = await researcher._research_phase(mine_name, "basic_info", phase_config)
                
                print(f"✅ Phase completed successfully")
                print(f"   Queries used: {len(phase_results.get('search_queries_used', []))}")
                print(f"   Sources found: {len(phase_results.get('sources', []))}")
                print(f"   Data extracted: {len(phase_results.get('data_extracted', {}))}")
                
                # Show sample data
                if phase_results.get('data_extracted'):
                    print("   📋 Sample Data:")
                    for field, value in list(phase_results['data_extracted'].items())[:3]:
                        print(f"      • {field}: {str(value)[:50]}...")
                
                # Show sample sources
                if phase_results.get('sources'):
                    print("   📚 Sample Sources:")
                    for source in phase_results['sources'][:2]:
                        print(f"      • {source.title[:40]}... ({source.reliability_score:.1%})")
                
                duration = time.time() - start_time
                print(f"\n✅ Simplified enhanced research test successful!")
                print(f"   Duration: {duration:.1f}s")
                print(f"   Basic functionality working correctly")
                
                return True
                
            except Exception as e:
                print(f"❌ Phase test failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
    except Exception as e:
        print(f"❌ Enhanced research engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Hauptfunktion"""
    
    print("🚀 Starting Simplified Enhanced Research Test")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        success = await test_core_enhanced_research()
        
        if success:
            print("\n🎉 Simplified Enhanced Research Test Passed!")
            print("\n✅ Core functionality working:")
            print("   • Enhanced Research Engine can be created")
            print("   • API calls are successful")
            print("   • Phase processing works")
            print("   • Data extraction works")
            print("   • Source attribution works")
            
            print("\n💡 Ready for full testing:")
            print("   python test_enhanced_improvements.py")
            print("\n💡 Or start the GUI:")
            print("   python main.py")
            
            return 0
        else:
            print("\n❌ Simplified test failed")
            print("   Please check API configuration and network connection")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    print(f"\n{'🎉 Test completed successfully!' if exit_code == 0 else '❌ Test completed with issues.'}")
    sys.exit(exit_code)
