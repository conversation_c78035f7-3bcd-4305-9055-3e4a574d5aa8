"""
Test Script für MineExtractorWeb v1.0 Fixes
Überprüft ob alle kritischen Fixes funktionieren
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """Test all critical imports"""
    print("🧪 Testing imports...")
    
    try:
        # Test basic imports
        from src.mine_data_models import MineDataFields, MineResearchResult
        print("✅ Mine data models imported")
        
        from src.perplexity_client import PerplexityClient
        print("✅ Perplexity client imported")
        
        from src.tavily_client import TavilyClient
        print("✅ Tavily client imported")
        
        from src.web_researcher import WebMiningResearcher
        print("✅ Web researcher imported")
        
        from src.gui_integration import WebResearchGUIExtension
        print("✅ GUI integration imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_gui_creation():
    """Test GUI creation without tkinter errors"""
    print("\n🖥️ Testing GUI creation...")
    
    try:
        # Import tkinter to check availability
        import tkinter as tk
        print("✅ Tkinter available")
        
        # Test GUI creation
        from src.gui_integration import create_standalone_gui
        
        # This should NOT raise "Too early to create variable" error
        gui = create_standalone_gui()
        print("✅ Standalone GUI created successfully")
        
        # Close the GUI immediately for testing
        if hasattr(gui, 'root'):
            gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False

async def test_api_clients():
    """Test API client initialization"""
    print("\n🔑 Testing API clients...")
    
    try:
        # Test Perplexity client with dummy key
        from src.perplexity_client import PerplexityClient
        perplexity_client = PerplexityClient("dummy-key")
        print("✅ Perplexity client initialized")
        
        # Test Tavily client with dummy key
        from src.tavily_client import TavilyClient
        tavily_client = TavilyClient("dummy-key")
        print("✅ Tavily client initialized")
        
        # Test WebMiningResearcher with dummy config
        from src.web_researcher import WebMiningResearcher
        config = {
            'perplexity_key': 'dummy-perplexity-key',
            'tavily_key': 'dummy-tavily-key'
        }
        researcher = WebMiningResearcher(config)
        print(f"✅ Web researcher initialized with {len(researcher.api_clients)} API clients")
        
        return True
        
    except Exception as e:
        print(f"❌ API client test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config.api_keys import APIConfig
        
        validation = APIConfig.validate_config()
        print(f"✅ API configuration loaded")
        print(f"   Perplexity: {'✅' if validation['perplexity'] else '❌'}")
        print(f"   Tavily: {'✅' if validation['tavily'] else '❌'}")
        print(f"   Exa: {'✅' if validation['exa'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 MineExtractorWeb v1.0 Fix Verification")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Imports", test_imports),
        ("GUI Creation", test_gui_creation),
        ("API Clients", test_api_clients),
        ("Configuration", test_config_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name} Test:")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
        print("\n💡 Next steps:")
        print("   1. Run: python main.py --test-apis")
        print("   2. Run: python main.py --gui")
        print("   3. Configure your API keys in .env file")
    else:
        print("⚠️ Some tests failed - please check the errors above")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
