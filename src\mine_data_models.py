"""
Data Models für MineExtractorWeb v1.0
Definiert Datenstrukturen für Mining-Daten und Research-Ergebnisse
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json
import hashlib

@dataclass
class MineDataFields:
    """
    Hauptdatenstruktur für Mining-Daten
    Kompatibel mit bestehendem PDF-System (identische Struktur)
    """
    
    # Basis-Identifikation
    id: str = ""
    name: str = ""
    
    # Betreiber-Information
    betreiber: str = ""
    
    # Koordinaten
    x_koordinate: str = ""
    y_koordinate: str = ""
    
    # Status-Information
    aktivitaetsstatus: str = ""
    aktivitaetsstatus_detail: str = ""
    
    # Finanzielle Daten (kritisch für Restaurationskosten)
    restaurationskosten_cad: str = ""
    jahr_kostenaufnahme: str = ""
    jahr_dokumenterstellung: str = ""
    
    # Technische Spezifikationen
    rohstoffabbau: str = ""
    minentyp: str = ""
    
    # Produktions-Timeline
    produktionsstart: str = ""
    produktionsende: str = ""
    foerdermenge_jahr: str = ""
    
    # Geografische Daten
    flaeche_km2: str = ""
    
    # Quellenangaben
    quellenangaben: str = ""
    
    def __post_init__(self):
        """Post-initialization processing"""
        # Auto-generate ID if not provided
        if not self.id and self.name:
            self.id = self.generate_id(self.name)
        
        # Set creation year if not provided
        if not self.jahr_dokumenterstellung:
            self.jahr_dokumenterstellung = str(datetime.now().year)
    
    @staticmethod
    def generate_id(mine_name: str) -> str:
        """Generiert eindeutige Mine-ID basierend auf Namen"""
        hash_input = mine_name.lower().encode('utf-8')
        return str(abs(int(hashlib.md5(hash_input).hexdigest(), 16)) % 100000)
    
    def to_dict(self) -> Dict[str, str]:
        """Konvertiert zu Dictionary für CSV-Export"""
        return {
            'ID': self.id,
            'Name': self.name,
            'Betreiber': self.betreiber,
            'x-Koordinate': self.x_koordinate,
            'y-Koordinate': self.y_koordinate,
            'Aktivitätsstatus': self.aktivitaetsstatus,
            'Aktivitätsstatus (aktiv, geplant, geschlossen, sonstiges)': self.aktivitaetsstatus_detail,
            'Restaurationskosten in $ CAD': self.restaurationskosten_cad,
            'Jahr der Aufnahme der Kosten': self.jahr_kostenaufnahme,
            'Jahr der Erstellung des Dokumentes': self.jahr_dokumenterstellung,
            'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)': self.rohstoffabbau,
            'Minentyp (Untertage, Open-Pit, usw.)': self.minentyp,
            'Produktionsstart': self.produktionsstart,
            'Produktionsende': self.produktionsende,
            'Fördermenge/Jahr': self.foerdermenge_jahr,
            'Fläche der Mine in qkm': self.flaeche_km2,
            'Quellenangaben': self.quellenangaben
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'MineDataFields':
        """Erstellt MineDataFields aus Dictionary"""
        # Reverse mapping von CSV headers zu internen Feldnamen
        reverse_mapping = {
            'ID': 'id',
            'Name': 'name',
            'Betreiber': 'betreiber',
            'x-Koordinate': 'x_koordinate',
            'y-Koordinate': 'y_koordinate',
            'Aktivitätsstatus': 'aktivitaetsstatus',
            'Aktivitätsstatus (aktiv, geplant, geschlossen, sonstiges)': 'aktivitaetsstatus_detail',
            'Restaurationskosten in $ CAD': 'restaurationskosten_cad',
            'Jahr der Aufnahme der Kosten': 'jahr_kostenaufnahme',
            'Jahr der Erstellung des Dokumentes': 'jahr_dokumenterstellung',
            'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)': 'rohstoffabbau',
            'Minentyp (Untertage, Open-Pit, usw.)': 'minentyp',
            'Produktionsstart': 'produktionsstart',
            'Produktionsende': 'produktionsende',
            'Fördermenge/Jahr': 'foerdermenge_jahr',
            'Fläche der Mine in qkm': 'flaeche_km2',
            'Quellenangaben': 'quellenangaben'
        }
        
        kwargs = {}
        for csv_header, internal_field in reverse_mapping.items():
            if csv_header in data:
                kwargs[internal_field] = data[csv_header]
        
        return cls(**kwargs)
    
    def get_completion_rate(self) -> float:
        """Berechnet Vollständigkeitsrate (0.0 - 1.0)"""
        total_fields = 17  # Anzahl der Datenfelder
        filled_fields = sum(1 for field in self.__dict__.values() 
                          if field and str(field).strip())
        return filled_fields / total_fields
    
    def get_critical_fields_status(self) -> Dict[str, bool]:
        """Prüft Status kritischer Felder"""
        critical_fields = {
            'name': bool(self.name),
            'betreiber': bool(self.betreiber),
            'aktivitaetsstatus': bool(self.aktivitaetsstatus),
            'restaurationskosten_cad': bool(self.restaurationskosten_cad),
            'rohstoffabbau': bool(self.rohstoffabbau)
        }
        return critical_fields
    
    def is_valid_mine_data(self) -> bool:
        """Prüft ob Mine-Daten als valide gelten"""
        critical = self.get_critical_fields_status()
        # Mindestens Name + 2 weitere kritische Felder erforderlich
        return critical['name'] and sum(critical.values()) >= 3
    
    def __str__(self) -> str:
        """String representation"""
        completion = self.get_completion_rate() * 100
        return f"MineData(name='{self.name}', completion={completion:.1f}%, operator='{self.betreiber}')"

@dataclass
class SourceMetadata:
    """Metadaten für Datenquellen"""
    
    url: str = ""
    title: str = ""
    date_accessed: str = ""
    source_type: str = ""  # 'government', 'company', 'industry', 'news'
    confidence: float = 0.0  # 0.0 - 1.0
    language: str = "en"
    
    def __post_init__(self):
        if not self.date_accessed:
            self.date_accessed = datetime.now().isoformat()

@dataclass
class MineResearchResult:
    """
    Umfassendes Research-Ergebnis mit Metadaten
    Enthält Mine-Daten plus Research-Details
    """
    
    # Haupt-Mine-Daten
    mine_data: MineDataFields = field(default_factory=MineDataFields)
    
    # Research Metadaten
    research_timestamp: str = ""
    research_duration: float = 0.0  # Sekunden
    apis_used: List[str] = field(default_factory=list)
    total_sources: int = 0
    
    # Datenqualität
    confidence_score: float = 0.0  # 0.0 - 1.0
    data_completeness: float = 0.0  # 0.0 - 1.0
    validation_status: str = ""  # 'valid', 'partial', 'invalid'
    
    # Quellen-Details
    sources: List[SourceMetadata] = field(default_factory=list)
    primary_sources: List[str] = field(default_factory=list)
    
    # Errors and Warnings
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Raw API Responses (für Debugging)
    raw_responses: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization calculations"""
        if not self.research_timestamp:
            self.research_timestamp = datetime.now().isoformat()
        
        # Auto-calculate metrics
        self.data_completeness = self.mine_data.get_completion_rate()
        self.total_sources = len(self.sources)
        
        # Calculate confidence score
        self._calculate_confidence_score()
        
        # Determine validation status
        self._determine_validation_status()
    
    def _calculate_confidence_score(self):
        """Berechnet Gesamt-Confidence-Score"""
        if not self.sources:
            self.confidence_score = 0.0
            return
        
        # Weight sources by type
        source_weights = {
            'government': 1.0,
            'company': 0.8,
            'industry': 0.6,
            'news': 0.4
        }
        
        total_weight = 0.0
        weighted_confidence = 0.0
        
        for source in self.sources:
            weight = source_weights.get(source.source_type, 0.5)
            total_weight += weight
            weighted_confidence += source.confidence * weight
        
        if total_weight > 0:
            self.confidence_score = min(1.0, weighted_confidence / total_weight)
        else:
            self.confidence_score = 0.0
    
    def _determine_validation_status(self):
        """Bestimmt Validierungsstatus"""
        if self.mine_data.is_valid_mine_data():
            if self.data_completeness >= 0.8 and self.confidence_score >= 0.7:
                self.validation_status = 'valid'
            else:
                self.validation_status = 'partial'
        else:
            self.validation_status = 'invalid'
    
    def add_source(self, url: str, title: str = "", source_type: str = "unknown", 
                  confidence: float = 0.5):
        """Fügt neue Quelle hinzu"""
        source = SourceMetadata(
            url=url,
            title=title,
            source_type=source_type,
            confidence=confidence
        )
        self.sources.append(source)
        
        # Recalculate metrics
        self.total_sources = len(self.sources)
        self._calculate_confidence_score()
        self._determine_validation_status()
    
    def add_error(self, error_message: str):
        """Fügt Fehler hinzu"""
        self.errors.append(f"[{datetime.now().strftime('%H:%M:%S')}] {error_message}")
    
    def add_warning(self, warning_message: str):
        """Fügt Warnung hinzu"""
        self.warnings.append(f"[{datetime.now().strftime('%H:%M:%S')}] {warning_message}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Gibt Research-Zusammenfassung zurück"""
        return {
            'mine_name': self.mine_data.name,
            'completion_rate': f"{self.data_completeness:.1%}",
            'confidence_score': f"{self.confidence_score:.1%}",
            'validation_status': self.validation_status,
            'sources_count': self.total_sources,
            'apis_used': self.apis_used,
            'research_duration': f"{self.research_duration:.1f}s",
            'has_errors': len(self.errors) > 0,
            'has_warnings': len(self.warnings) > 0
        }
    
    def to_csv_row(self) -> Dict[str, str]:
        """Konvertiert zu CSV-Row (kompatibel mit PDF-System)"""
        return self.mine_data.to_dict()
    
    def to_json(self) -> str:
        """Konvertiert zu JSON für detaillierte Speicherung"""
        data = {
            'mine_data': self.mine_data.__dict__,
            'research_metadata': {
                'timestamp': self.research_timestamp,
                'duration': self.research_duration,
                'apis_used': self.apis_used,
                'confidence_score': self.confidence_score,
                'data_completeness': self.data_completeness,
                'validation_status': self.validation_status,
                'total_sources': self.total_sources
            },
            'sources': [source.__dict__ for source in self.sources],
            'issues': {
                'errors': self.errors,
                'warnings': self.warnings
            }
        }
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'MineResearchResult':
        """Lädt MineResearchResult aus JSON"""
        data = json.loads(json_str)
        
        # Reconstruct mine data
        mine_data = MineDataFields(**data['mine_data'])
        
        # Reconstruct result
        result = cls(mine_data=mine_data)
        
        # Set metadata
        metadata = data['research_metadata']
        result.research_timestamp = metadata['timestamp']
        result.research_duration = metadata['duration']
        result.apis_used = metadata['apis_used']
        result.confidence_score = metadata['confidence_score']
        result.data_completeness = metadata['data_completeness']
        result.validation_status = metadata['validation_status']
        
        # Reconstruct sources
        result.sources = [SourceMetadata(**source_data) for source_data in data['sources']]
        
        # Set issues
        result.errors = data['issues']['errors']
        result.warnings = data['issues']['warnings']
        
        return result
    
    def __str__(self) -> str:
        """String representation"""
        return f"MineResearchResult(name='{self.mine_data.name}', status='{self.validation_status}', confidence={self.confidence_score:.1%})"

@dataclass
class BatchResearchResult:
    """Ergebnisse für Batch-Research von mehreren Minen"""
    
    results: List[MineResearchResult] = field(default_factory=list)
    batch_timestamp: str = ""
    total_duration: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    
    def __post_init__(self):
        if not self.batch_timestamp:
            self.batch_timestamp = datetime.now().isoformat()
        
        # Calculate statistics
        self.success_count = sum(1 for r in self.results if r.validation_status != 'invalid')
        self.failure_count = len(self.results) - self.success_count
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Gibt Batch-Statistiken zurück"""
        if not self.results:
            return {'total': 0, 'success_rate': 0.0}
        
        total_mines = len(self.results)
        avg_completion = sum(r.data_completeness for r in self.results) / total_mines
        avg_confidence = sum(r.confidence_score for r in self.results) / total_mines
        
        return {
            'total_mines': total_mines,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': self.success_count / total_mines,
            'avg_completion': avg_completion,
            'avg_confidence': avg_confidence,
            'total_duration': self.total_duration,
            'avg_duration_per_mine': self.total_duration / total_mines if total_mines > 0 else 0
        }
    
    def get_failed_mines(self) -> List[str]:
        """Gibt Liste der fehlgeschlagenen Mine-Namen zurück"""
        return [r.mine_data.name for r in self.results if r.validation_status == 'invalid']
    
    def get_successful_mines(self) -> List[str]:
        """Gibt Liste der erfolgreichen Mine-Namen zurück"""
        return [r.mine_data.name for r in self.results if r.validation_status != 'invalid']

# Type Aliases für bessere Typisierung
MineDict = Dict[str, str]  # Dictionary representation of mine data
ResearchConfig = Dict[str, Any]  # Configuration for research operations
APIResponse = Dict[str, Any]  # Raw API response format

if __name__ == "__main__":
    # Test data models
    print("🧪 Testing Data Models...")
    
    # Test MineDataFields
    mine = MineDataFields(
        name="Éléonore Test",
        betreiber="Newmont Corporation", 
        aktivitaetsstatus="aktiv",
        restaurationskosten_cad="45000000"
    )
    
    print(f"Mine: {mine}")
    print(f"Completion: {mine.get_completion_rate():.1%}")
    print(f"Valid: {mine.is_valid_mine_data()}")
    
    # Test MineResearchResult
    result = MineResearchResult(mine_data=mine)
    result.add_source("https://example.com", "Test Source", "government", 0.9)
    result.add_source("https://company.com", "Company Report", "company", 0.8)
    
    print(f"\nResult: {result}")
    print(f"Summary: {result.get_summary()}")
    
    print("\n✅ Data models test completed!")
