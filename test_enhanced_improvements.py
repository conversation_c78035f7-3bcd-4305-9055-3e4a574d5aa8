#!/usr/bin/env python3
"""
Test Script for Enhanced Research Engine Improvements
Tests the upgraded features for better data extraction and source documentation
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_enhanced_improvements():
    """Testet die verbesserte Enhanced Research Engine"""
    
    print("🔍 Testing Enhanced Research Engine Improvements")
    print("=" * 60)
    
    try:
        # Import Enhanced Research Engine
        from src.enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        if not APIConfig.PERPLEXITY_API_KEY:
            print("❌ Perplexity API key required for testing")
            print("   Please configure in .env file")
            return False
        
        config = {
            'perplexity_key': APIConfig.PERPLEXITY_API_KEY
        }
        
        test_mines = ["Éléonore", "Canadian Malartic"]
        
        print(f"🧪 Testing with mines: {', '.join(test_mines)}")
        print()
        
        async with EnhancedMining<PERSON>esearcher(config) as researcher:
            
            for mine_name in test_mines:
                print(f"🏗️ Testing Enhanced Research for: {mine_name}")
                print("-" * 50)
                
                def progress_callback(progress, status):
                    print(f"   {progress:.0f}% - {status}")
                
                start_time = time.time()
                
                # Run enhanced research with improved algorithms
                result = await researcher.research_mine_comprehensive(
                    mine_name,
                    progress_callback=progress_callback
                )
                
                duration = time.time() - start_time
                
                print(f"\n✅ Enhanced Research Completed for {mine_name}")
                print(f"   Duration: {duration:.1f}s")
                print(f"   Phases: {len(result.research_phases_completed)}")
                print(f"   Data Points: {len(result.data_points)}")
                print(f"   Sources: {len(result.all_sources)}")
                print(f"   Completeness: {result.data_completeness_score:.1%}")
                
                # Test specific improvements
                print(f"\n📊 Testing Specific Improvements:")
                
                # Check for critical fields that were previously missing
                critical_fields = [
                    'production_start_year', 'production_end_year', 
                    'annual_production_tonnes', 'mine_area_km2', 
                    'restoration_costs_cad'
                ]
                
                found_fields = []
                for field in critical_fields:
                    if field in result.data_points:
                        data_point = result.data_points[field]
                        found_fields.append(field)
                        print(f"   ✅ {field}: {data_point.value} (confidence: {data_point.confidence:.1%})")
                        
                        # Show sources for this field
                        if data_point.sources:
                            print(f"      Sources: {len(data_point.sources)}")
                            for source in data_point.sources[:2]:
                                reliability = "★" * int(source.reliability_score * 5)
                                print(f"        • {source.title[:40]}... ({reliability})")
                
                missing_fields = [f for f in critical_fields if f not in found_fields]
                if missing_fields:
                    print(f"   ⚠️ Still missing: {', '.join(missing_fields)}")
                
                # Test source documentation improvements
                print(f"\n📚 Source Documentation Test:")
                
                source_types = {}
                for source in result.all_sources:
                    source_type = source.content_type
                    if source_type not in source_types:
                        source_types[source_type] = []
                    source_types[source_type].append(source)
                
                for source_type, sources in source_types.items():
                    avg_reliability = sum(s.reliability_score for s in sources) / len(sources)
                    reliability_stars = "★" * int(avg_reliability * 5)
                    print(f"   {source_type.replace('_', ' ').title()}: {len(sources)} sources ({reliability_stars} {avg_reliability:.1%})")
                
                # Test enhanced summary
                print(f"\n📋 Enhanced Summary Test:")
                summary = researcher.get_research_summary(result)
                print("   Summary generated successfully ✅")
                
                # Test source summary
                source_summary = result.get_sources_summary()
                print("   Source summary generated successfully ✅")
                
                print(f"\n" + "="*60 + "\n")
        
        print("🎉 Enhanced Research Engine Testing Completed!")
        print()
        print("🚀 Key Improvements Tested:")
        print("   ✅ Enhanced search queries for missing fields")
        print("   ✅ Improved source classification and reliability scoring")
        print("   ✅ Better data extraction with confidence scoring")
        print("   ✅ Comprehensive source documentation")
        print("   ✅ Cross-validation between sources")
        print("   ✅ Detailed summary with source attribution")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced research testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_conversion_compatibility():
    """Testet die Konvertierung für GUI-Kompatibilität"""
    
    print("\n🔄 Testing Enhanced Result Conversion")
    print("-" * 40)
    
    try:
        from src.gui_integration import WebResearchGUIExtension
        from src.enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        # Create mock enhanced result for testing
        from src.enhanced_web_researcher import EnhancedMineResearchResult, DataPoint, ResearchSource
        
        # Create test enhanced result
        enhanced_result = EnhancedMineResearchResult(mine_name="Test Mine")
        
        # Add test data points
        test_source = ResearchSource(
            url="https://test.com",
            title="Test Source",
            date_accessed="2025-06-07",
            content_type="government_data",
            reliability_score=0.9,
            content_snippet="Test content"
        )
        
        enhanced_result.data_points['mine_operator'] = DataPoint(
            field_name='mine_operator',
            value='Test Mining Corp',
            confidence=0.85,
            sources=[test_source]
        )
        
        enhanced_result.data_points['production_start_year'] = DataPoint(
            field_name='production_start_year',
            value=2010,
            confidence=0.90,
            sources=[test_source]
        )
        
        enhanced_result.all_sources = [test_source]
        enhanced_result.data_completeness_score = 0.75
        enhanced_result.total_research_time = 45.2
        
        # Test conversion
        gui_extension = WebResearchGUIExtension()
        converted_result = gui_extension._convert_enhanced_result(enhanced_result)
        
        print("✅ Conversion successful!")
        print(f"   Mine Name: {converted_result.mine_data.name}")
        print(f"   Operator: {converted_result.mine_data.betreiber}")
        print(f"   Production Start: {converted_result.mine_data.produktionsstart}")
        print(f"   Sources: {len(converted_result.sources)}")
        print(f"   Source Refs: {converted_result.mine_data.quellenangaben[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Hauptfunktion"""
    
    print("🚀 Starting Enhanced Research Engine Improvement Tests")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Test enhanced research engine
        research_success = await test_enhanced_improvements()
        
        # Test conversion compatibility
        conversion_success = await test_conversion_compatibility()
        
        if research_success and conversion_success:
            print("\n🎉 All Enhanced Research Engine Improvements Working!")
            print("\n🔍 What's New:")
            print("   • More specific search queries for missing data fields")
            print("   • Enhanced source classification and reliability scoring")
            print("   • Improved data extraction with structured prompts")
            print("   • Comprehensive source documentation with URLs")
            print("   • Better cross-validation between sources")
            print("   • Detailed summaries with source attribution")
            print("   • Full GUI compatibility maintained")
            
            print("\n💡 Next Steps:")
            print("   1. Run: python main.py --test-enhanced")
            print("   2. Start GUI: python main.py")
            print("   3. Test with your mine list")
            
            return 0
        else:
            print("\n⚠️ Some tests failed - check configuration")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    print(f"\n{'🎉 Testing completed successfully!' if exit_code == 0 else '❌ Testing completed with issues.'}")
    sys.exit(exit_code)
