# Phase 1 Implementation - MineExtractorWeb v1.0
**Schritt-für-Schritt Implementierung des Basic Web Research Systems**

---

## 🎯 **PHASE 1 ZIELE**

### **Deliverables (Woche 1-2):**
- ✅ Funktionierender Web-Research Prototyp
- ✅ Perplexity AI Integration
- ✅ Basis-GUI-Erweiterung
- ✅ Test mit 5 Quebec-Minen
- ✅ Gleiche CSV-Ausgabe wie PDF-System

### **Erfolgskriterien:**
- 80%+ der Test-Minen liefern verwertbare Daten
- Response-Zeit < 2 Minuten pro Mine
- Integration ins bestehende GUI-System
- Identische CSV-Struktur

---

## 📋 **IMPLEMENTIERUNGS-CHECKLIST**

### **Tag 1: Project Setup**
- [ ] ⏳ Projekt-Verzeichnisstruktur erstellen
- [ ] ⏳ Dependencies installieren
- [ ] ⏳ Perplexity API Account setup
- [ ] ⏳ Basic Configuration Files

### **Tag 2-3: Core Research Engine**
- [ ] ⏳ Perplexity Research Class
- [ ] ⏳ Data Parsing & Extraction Logic
- [ ] ⏳ Basic Error Handling

### **Tag 4-5: GUI Integration**
- [ ] ⏳ Web Research Tab in bestehender GUI
- [ ] ⏳ Input/Output Interface
- [ ] ⏳ Progress Tracking

### **Tag 6-7: Testing & Optimization**
- [ ] ⏳ Test mit Quebec-Minen
- [ ] ⏳ Data Quality Validation
- [ ] ⏳ Performance Optimization

---

## 🛠️ **SCHRITT 1: PROJECT SETUP**

### **1.1 Verzeichnisstruktur erstellen:**

```bash
C:\Temp\Minen\MineExtractorWeb_v1\
├── src/
│   ├── __init__.py
│   ├── web_researcher.py          # Hauptklasse für Web Research
│   ├── perplexity_client.py       # Perplexity API Client
│   ├── data_parser.py             # Data Extraction & Parsing
│   ├── mine_data_models.py        # Data Models (gleich wie PDF System)
│   └── gui_integration.py         # GUI Erweiterung
├── config/
│   ├── api_keys.py               # API Konfiguration
│   ├── mining_prompts.py         # Spezialisierte Prompts
│   └── settings.py               # Allgemeine Einstellungen
├── tests/
│   ├── test_perplexity.py        # API Tests
│   ├── test_data_parsing.py      # Parsing Tests
│   └── test_integration.py       # Integration Tests
├── logs/                         # Log Files
├── output/                       # CSV Output Files
├── requirements.txt              # Python Dependencies
└── main.py                       # Haupt-Startscript
```

### **1.2 Dependencies installieren:**

```bash
# requirements.txt
aiohttp==3.8.5
asyncio==3.4.3
pandas==2.1.0
pydantic==2.3.0
python-dotenv==1.0.0
tenacity==8.2.3
loguru==0.7.0
tkinter  # Built-in mit Python
requests==2.31.0
json5==0.9.14
```

```bash
# Installation
cd C:\Temp\Minen\MineExtractorWeb_v1
pip install -r requirements.txt
```

### **1.3 Basic Configuration Setup:**

```python
# config/api_keys.py
import os
from dotenv import load_dotenv

load_dotenv()

class APIConfig:
    """Zentrale API-Konfiguration"""
    
    # Perplexity AI Configuration
    PERPLEXITY_API_KEY = os.getenv('PERPLEXITY_API_KEY', '')
    PERPLEXITY_BASE_URL = 'https://api.perplexity.ai/chat/completions'
    PERPLEXITY_MODEL = 'llama-3.1-sonar-large-128k-online'
    
    # Request Settings
    MAX_TOKENS = 2000
    TEMPERATURE = 0.1
    TIMEOUT_SECONDS = 120
    MAX_RETRIES = 3
    
    # Domain Filters für bessere Ergebnisse
    PRIORITY_DOMAINS = [
        'gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca',
        'tsx.com', 'sec.gov', 'newswire.ca'
    ]
    
    @classmethod
    def validate_config(cls):
        """Validiert API-Konfiguration"""
        if not cls.PERPLEXITY_API_KEY:
            raise ValueError("PERPLEXITY_API_KEY environment variable not set")
        
        return True
```

```python
# config/settings.py
import os

class ProjectSettings:
    """Projekt-weite Einstellungen"""
    
    # Pfade
    PROJECT_ROOT = r"C:\Temp\Minen\MineExtractorWeb_v1"
    LOG_DIRECTORY = os.path.join(PROJECT_ROOT, "logs")
    OUTPUT_DIRECTORY = os.path.join(PROJECT_ROOT, "output")
    
    # CSV Output Settings (identisch mit PDF-System)
    CSV_DELIMITER = '|'
    CSV_ENCODING = 'utf-8-sig'
    
    # Mining Data Fields (exakte Kopie vom PDF-System)
    CSV_HEADERS = [
        'ID', 'Name', 'Betreiber', 'x-Koordinate', 'y-Koordinate',
        'Aktivitätsstatus', 'Aktivitätsstatus (aktiv, geplant, geschlossen, sonstiges)',
        'Restaurationskosten in $ CAD', 'Jahr der Aufnahme der Kosten',
        'Jahr der Erstellung des Dokumentes', 'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)',
        'Minentyp (Untertage, Open-Pit, usw.)', 'Produktionsstart', 'Produktionsende',
        'Fördermenge/Jahr', 'Fläche der Mine in qkm', 'Quellenangaben'
    ]
    
    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}"
```

---

## 🧠 **SCHRITT 2: CORE RESEARCH ENGINE**

### **2.1 Perplexity Client Implementation:**

```python
# src/perplexity_client.py
import aiohttp
import asyncio
import json
import time
from typing import Dict, Optional, List
from tenacity import retry, wait_exponential, stop_after_attempt
from loguru import logger
from config.api_keys import APIConfig

class PerplexityClient:
    """
    Spezialisierter Perplexity AI Client für Mining Research
    Optimiert für Quebec Mining Data Extraction
    """
    
    def __init__(self):
        self.api_key = APIConfig.PERPLEXITY_API_KEY
        self.base_url = APIConfig.PERPLEXITY_BASE_URL
        self.session = None
        
        # Validate configuration
        APIConfig.validate_config()
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=APIConfig.TIMEOUT_SECONDS)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=10), 
           stop=stop_after_attempt(APIConfig.MAX_RETRIES))
    async def research_mine_comprehensive(self, mine_name: str) -> Dict:
        """
        Umfassende Mine-Recherche mit Perplexity AI
        
        Args:
            mine_name: Name der Mine (z.B. "Éléonore", "Canadian Malartic")
            
        Returns:
            Dict mit extrahierten Mining-Daten und Metadaten
        """
        
        prompt = self._build_comprehensive_prompt(mine_name)
        
        payload = {
            "model": APIConfig.PERPLEXITY_MODEL,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": APIConfig.TEMPERATURE,
            "max_tokens": APIConfig.MAX_TOKENS,
            "return_citations": True,
            "search_domain_filter": APIConfig.PRIORITY_DOMAINS,
            "search_recency_filter": "month"
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(self.base_url, headers=headers, json=payload) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Log successful request
                    logger.info(f"Perplexity research successful for {mine_name} ({response_time:.2f}s)")
                    
                    return {
                        'success': True,
                        'mine_name': mine_name,
                        'raw_response': result,
                        'content': result['choices'][0]['message']['content'],
                        'citations': result.get('citations', []),
                        'response_time': response_time,
                        'timestamp': time.time()
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"Perplexity API error {response.status}: {error_text}")
                    raise Exception(f"API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"Perplexity request failed for {mine_name}: {e}")
            raise
    
    def _build_comprehensive_prompt(self, mine_name: str) -> str:
        """
        Erstellt optimierten Prompt für Mining-Daten-Extraktion
        Basiert auf Analyse der erfolgreichen PDF-Extraktion
        """
        
        return f"""Research comprehensive and current data about {mine_name} mine in Quebec, Canada.

CRITICAL DATA POINTS REQUIRED:

1. OPERATOR/BETREIBER:
   - Current owner/operator company (exact legal name)
   - Parent company if applicable

2. OPERATING STATUS:
   - Current status: active/closed/planned/suspended
   - Status year or last update

3. FINANCIAL DATA:
   - Environmental restoration costs in CAD dollars
   - Closure/decommissioning costs
   - Environmental bonds or guarantees

4. TECHNICAL SPECIFICATIONS:
   - Mine type: open-pit/underground/surface mining
   - Primary commodity: gold/copper/nickel/iron ore/lithium/other
   - Mine area in hectares or km²
   - Production capacity/annual volumes

5. PRODUCTION TIMELINE:
   - Production start date/year
   - Production end date/year (if applicable)
   - Project phases

6. LOCATION DATA:
   - GPS coordinates if available
   - Municipality/region in Quebec

SEARCH STRATEGY:
Focus on these high-priority sources:
- Quebec MERN: mrnf.gouv.qc.ca, gestim.mines.gouv.qc.ca
- SEDAR+: sedarplus.ca (Canadian company filings)
- Company official websites and technical reports
- Environmental impact assessments
- TSX/SEC regulatory filings
- Government permits and approvals

OUTPUT FORMAT:
Provide specific data values with source URLs and confidence levels.
If data not found, explicitly state "Not found" rather than omitting.
Include publication dates for financial data.

EXAMPLE FORMAT:
- Operator: [Company Name] (Source: URL, Date: YYYY-MM-DD)
- Status: [Active/Closed] as of [Year] (Source: URL)
- Restoration Costs: $[Amount] CAD (Source: URL, Date: YYYY)
- Mine Type: [Open-pit/Underground] (Source: URL)
- Commodity: [Gold/Copper/etc.] (Source: URL)

Focus on official government and company sources only. Avoid speculation."""

    async def research_financial_focus(self, mine_name: str) -> Dict:
        """
        Spezialisierte Recherche für Finanzdaten (Restoration Costs)
        """
        
        prompt = f"""Find specific financial data for {mine_name} mine in Quebec, Canada:

FOCUS AREAS:
- Environmental restoration costs (CAD)
- Mine closure costs and financial guarantees
- Environmental bonds and securities
- Decommissioning cost estimates

PRIORITY SOURCES:
- Quebec government environmental reports
- Company annual reports and technical reports
- SEDAR+ financial filings
- Environmental impact assessments

Provide exact amounts in CAD with source URLs and dates."""

        # Similar structure as research_mine_comprehensive
        # aber mit finanz-spezifischem Prompt
        pass
    
    async def research_technical_focus(self, mine_name: str) -> Dict:
        """
        Spezialisierte Recherche für technische Daten
        """
        
        prompt = f"""Find technical specifications for {mine_name} mine in Quebec:

FOCUS AREAS:
- Mine type and operation method
- Annual production volumes and capacity
- Mine site area and coordinates
- Mining infrastructure and equipment

PRIORITY SOURCES:
- Technical reports and feasibility studies
- Government mining permits
- Company operational updates
- Engineering assessments

Provide specific technical data with sources."""

        # Implementation similar to comprehensive research
        pass
```

### **2.2 Data Parser Implementation:**

```python
# src/data_parser.py
import re
import json
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

@dataclass
class MineDataFields:
    """
    Data structure matching existing PDF system exactly
    """
    id: str = ""
    name: str = ""
    betreiber: str = ""
    x_koordinate: str = ""
    y_koordinate: str = ""
    aktivitaetsstatus: str = ""
    aktivitaetsstatus_detail: str = ""
    restaurationskosten_cad: str = ""
    jahr_kostenaufnahme: str = ""
    jahr_dokumenterstellung: str = ""
    rohstoffabbau: str = ""
    minentyp: str = ""
    produktionsstart: str = ""
    produktionsende: str = ""
    foerdermenge_jahr: str = ""
    flaeche_km2: str = ""
    quellenangaben: str = ""

class MiningDataParser:
    """
    Parst Perplexity-Antworten und extrahiert strukturierte Mining-Daten
    Verwendet gleiche Parsing-Logic wie PDF-System aber für Web-Content
    """
    
    def __init__(self):
        # Mine name mappings aus PDF-System übernommen
        self.mine_mappings = {
            'fenelon': 'Fénélon',
            'eleonore': 'Éléonore', 
            'raglan': 'Raglan',
            'canadian malartic': 'Canadian Malartic',
            'casa berardi': 'Casa Berardi',
            'laronde': 'LaRonde',
            'troilus': 'Troilus',
            'mont wright': 'Mont Wright',
            'lac tio': 'Lac Tio',
            'niobec': 'Niobec',
            'beaufor': 'Beaufor',
            'goldex': 'Goldex'
        }
        
        # Operator patterns (erweitert vom PDF-System)
        self.operator_patterns = [
            (r'newmont\s+corporation', 'Newmont Corporation'),
            (r'agnico\s+eagle', 'Agnico Eagle Mines Limited'),
            (r'iamgold\s+corporation', 'IAMGOLD Corporation'),
            (r'wallbridge\s+mining', 'Wallbridge Mining Company Limited'),
            (r'glencore\s+plc', 'Glencore plc'),
            (r'arcelor\s*mittal', 'ArcelorMittal'),
            (r'champion\s+iron', 'Champion Iron Limited'),
            (r'hecla\s+mining', 'Hecla Mining Company')
        ]
        
        # Material keywords
        self.commodity_patterns = {
            'Gold': ['gold', 'or', 'aurifère', 'aurum'],
            'Kupfer': ['copper', 'cuivre', 'cu'],
            'Nickel': ['nickel', 'ni'],
            'Eisenerz': ['iron ore', 'fer', 'iron', 'fe'],
            'Lithium': ['lithium', 'li'],
            'Zink': ['zinc', 'zn'],
            'Blei': ['lead', 'plomb', 'pb'],
            'Silber': ['silver', 'argent', 'ag']
        }
        
        # Operation type patterns
        self.operation_patterns = {
            'Open-Pit': ['open.?pit', 'open.?cast', 'surface', 'à ciel ouvert', 'fosse'],
            'Untertage': ['underground', 'souterraine', 'shaft', 'underground mine']
        }
        
        # Status patterns
        self.status_patterns = {
            'aktiv': ['active', 'actif', 'operational', 'operating', 'in production'],
            'geschlossen': ['closed', 'fermé', 'shutdown', 'ceased', 'abandoned'],
            'geplant': ['planned', 'planifié', 'proposed', 'development', 'construction'],
            'ausgesetzt': ['suspended', 'care and maintenance', 'temporarily closed']
        }
    
    def parse_perplexity_response(self, response_data: Dict, mine_name: str) -> MineDataFields:
        """
        Hauptfunktion: Parst Perplexity-Response zu strukturierten Mining-Daten
        """
        
        content = response_data.get('content', '')
        citations = response_data.get('citations', [])
        
        logger.info(f"Parsing response for {mine_name} ({len(content)} chars)")
        
        # Normalisiere Mine-Namen
        normalized_name = self._normalize_mine_name(mine_name)
        
        # Extrahiere alle Datenfelder
        mine_data = MineDataFields()
        mine_data.id = self._generate_mine_id(normalized_name)
        mine_data.name = normalized_name
        mine_data.betreiber = self._extract_operator(content)
        mine_data.aktivitaetsstatus = self._extract_status(content)
        mine_data.aktivitaetsstatus_detail = mine_data.aktivitaetsstatus
        mine_data.restaurationskosten_cad = self._extract_restoration_costs(content)
        mine_data.jahr_kostenaufnahme = self._extract_cost_year(content)
        mine_data.jahr_dokumenterstellung = str(datetime.now().year)
        mine_data.rohstoffabbau = self._extract_commodity(content)
        mine_data.minentyp = self._extract_operation_type(content)
        mine_data.produktionsstart = self._extract_production_start(content)
        mine_data.produktionsende = self._extract_production_end(content)
        mine_data.foerdermenge_jahr = self._extract_production_volume(content)
        mine_data.flaeche_km2 = self._extract_mine_area(content)
        
        # Koordinaten aus citations extrahieren
        coordinates = self._extract_coordinates(content)
        mine_data.x_koordinate = coordinates[0] if coordinates else ""
        mine_data.y_koordinate = coordinates[1] if coordinates else ""
        
        # Quellenangaben zusammenstellen
        mine_data.quellenangaben = self._format_sources(citations)
        
        # Log parsing results
        filled_fields = sum(1 for field in mine_data.__dict__.values() 
                          if field and str(field).strip())
        logger.info(f"Parsed {filled_fields}/17 fields for {mine_name}")
        
        return mine_data
    
    def _normalize_mine_name(self, mine_name: str) -> str:
        """Normalisiert Mine-Namen basierend auf bekannten Mappings"""
        mine_lower = mine_name.lower().strip()
        return self.mine_mappings.get(mine_lower, mine_name.strip())
    
    def _extract_operator(self, content: str) -> str:
        """Extrahiert Betreiber-Information"""
        content_lower = content.lower()
        
        # Direkte Pattern-Suche
        for pattern, operator in self.operator_patterns:
            if re.search(pattern, content_lower):
                return operator
        
        # Fallback: Suche nach operator/owner patterns
        operator_patterns = [
            r'operator[:\s]+([^,\n\.]+)',
            r'owned by[:\s]+([^,\n\.]+)',
            r'company[:\s]+([^,\n\.]+)',
            r'operated by[:\s]+([^,\n\.]+)'
        ]
        
        for pattern in operator_patterns:
            match = re.search(pattern, content_lower)
            if match:
                operator = match.group(1).strip()
                if len(operator) > 3 and len(operator) < 50:  # Plausible length
                    return operator.title()
        
        return ""
    
    def _extract_restoration_costs(self, content: str) -> str:
        """
        Extrahiert Restaurationskosten - kritisches Feld
        Erweiterte Patterns für verschiedene Formate
        """
        
        # Kosten-Patterns (erweitert vom PDF-System)
        cost_patterns = [
            # Standard formats
            r'restoration\s+costs?\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:million|M|billion|B|thousand|K))?',
            r'closure\s+costs?\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:million|M|billion|B|thousand|K))?',
            r'environmental\s+costs?\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:million|M|billion|B|thousand|K))?',
            r'decommissioning\s+costs?\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:million|M|billion|B|thousand|K))?',
            
            # French patterns
            r'coûts?\s+(?:de\s+)?restauration\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:millions?|M|milliards?|B))?',
            r'coûts?\s+(?:de\s+)?fermeture\s*:?\s*(?:CAD\s*)?(?:\$\s*)?([0-9,.\s]+)(?:\s*(?:millions?|M|milliards?|B))?',
            
            # Specific number formats (from PDF patterns)
            r'(\d{2}\s+\d{3}\s+\d{3})\s*\$',  # Format "54 172 653$"
            r'\$\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)',  # $1.5 million
            r'([0-9,]+(?:\.[0-9]+)?)\s*(?:million|M)\s*(?:CAD|\$)',  # 54.2 million CAD
        ]
        
        for pattern in cost_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                normalized_cost = self._normalize_cost_value(match, content)
                if normalized_cost:
                    return normalized_cost
        
        return ""
    
    def _normalize_cost_value(self, cost_str: str, context: str) -> str:
        """
        Normalisiert Kostenwerte zu einheitlichem CAD-Format
        """
        # Remove spaces and commas
        clean_cost = re.sub(r'[\s,]', '', cost_str)
        
        try:
            # Check for multipliers in context around the number
            context_lower = context.lower()
            cost_index = context_lower.find(cost_str.lower())
            
            if cost_index != -1:
                surrounding_text = context_lower[max(0, cost_index-50):cost_index+50]
                
                if any(word in surrounding_text for word in ['million', 'millions', 'M']):
                    value = float(clean_cost) * 1_000_000
                    return str(int(value))
                elif any(word in surrounding_text for word in ['billion', 'milliards', 'B']):
                    value = float(clean_cost) * 1_000_000_000
                    return str(int(value))
                elif any(word in surrounding_text for word in ['thousand', 'milliers', 'K']):
                    value = float(clean_cost) * 1_000
                    return str(int(value))
            
            # Direct number if no multiplier
            value = float(clean_cost)
            if 10_000 <= value <= 10_000_000_000:  # Reasonable range
                return str(int(value))
                
        except (ValueError, OverflowError):
            pass
        
        return ""
    
    def _extract_status(self, content: str) -> str:
        """Extrahiert Aktivitätsstatus"""
        content_lower = content.lower()
        
        for status, keywords in self.status_patterns.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return status
        
        return ""
    
    def _extract_commodity(self, content: str) -> str:
        """Extrahiert Rohstofftyp"""
        content_lower = content.lower()
        
        for commodity, keywords in self.commodity_patterns.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return commodity
        
        return ""
    
    def _extract_operation_type(self, content: str) -> str:
        """Extrahiert Minentyp (Operation Type)"""
        content_lower = content.lower()
        
        for op_type, patterns in self.operation_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    return op_type
        
        return ""
    
    def _extract_production_start(self, content: str) -> str:
        """Extrahiert Produktionsstart-Jahr"""
        patterns = [
            r'production\s+(?:started|began|commenced)\s+(?:in\s+)?(\d{4})',
            r'start(?:ed)?\s+(?:production\s+)?(?:in\s+)?(\d{4})',
            r'operations\s+began\s+(?:in\s+)?(\d{4})',
            r'mise\s+en\s+production\s+(?:en\s+)?(\d{4})'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                year = int(match)
                if 1900 <= year <= 2030:  # Reasonable year range
                    return str(year)
        
        return ""
    
    def _extract_production_end(self, content: str) -> str:
        """Extrahiert Produktionsende-Jahr"""
        patterns = [
            r'production\s+(?:ended|ceased|stopped)\s+(?:in\s+)?(\d{4})',
            r'closed\s+(?:in\s+)?(\d{4})',
            r'shutdown\s+(?:in\s+)?(\d{4})',
            r'fermeture\s+(?:en\s+)?(\d{4})'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                year = int(match)
                if 1900 <= year <= 2030:
                    return str(year)
        
        return ""
    
    def _extract_production_volume(self, content: str) -> str:
        """Extrahiert Produktionsvolumen"""
        patterns = [
            r'(?:produces|production of)\s+([0-9,]+(?:\.[0-9]+)?)\s*(?:tonnes?|tons?|t)\s*(?:per\s+year|annually)',
            r'annual\s+production\s*:?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:tonnes?|tons?|t)',
            r'([0-9,]+(?:\.[0-9]+)?)\s*(?:tonnes?|tons?|t)\s*(?:per\s+year|/year|annually)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                clean_number = re.sub(r'[,\s]', '', match)
                try:
                    value = float(clean_number)
                    if value > 1000:  # Minimum reasonable production
                        return f"{match} t/Jahr"
                except ValueError:
                    continue
        
        return ""
    
    def _extract_mine_area(self, content: str) -> str:
        """Extrahiert Minenfläche"""
        patterns = [
            r'(?:area|size|surface)\s*:?\s*([0-9,]+(?:\.[0-9]+)?)\s*(?:km²|km2|square\s+kilometers)',
            r'([0-9,]+(?:\.[0-9]+)?)\s*(?:hectares?|ha)',
            r'site\s+covers\s+([0-9,]+(?:\.[0-9]+)?)\s*(?:km²|km2|hectares?)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                try:
                    area = float(match.replace(',', ''))
                    if 0.1 <= area <= 1000:  # Reasonable area range
                        if 'hectare' in pattern or 'ha' in pattern:
                            area_km2 = area / 100  # Convert hectares to km²
                            return f"{area_km2:.2f} km²"
                        else:
                            return f"{area} km²"
                except ValueError:
                    continue
        
        return ""
    
    def _extract_coordinates(self, content: str) -> Tuple[str, str]:
        """Extrahiert GPS-Koordinaten"""
        coord_patterns = [
            r'(-?\d+\.?\d*)\s*[°,]\s*(-?\d+\.?\d*)',  # Basic lat, lon
            r'latitude[:\s]+(-?\d+\.?\d*)[°]?\s*,?\s*longitude[:\s]+(-?\d+\.?\d*)',
            r'coordinates[:\s]+(-?\d+\.?\d*)[°]?\s*[,/]\s*(-?\d+\.?\d*)'
        ]
        
        for pattern in coord_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                try:
                    lat, lon = float(match[0]), float(match[1])
                    # Quebec coordinate ranges (approximate)
                    if 45 <= lat <= 62 and -85 <= lon <= -57:
                        return str(lon), str(lat)  # x, y format
                except ValueError:
                    continue
        
        return "", ""
    
    def _extract_cost_year(self, content: str) -> str:
        """Extrahiert Jahr der Kostenaufnahme"""
        # Suche nach Jahreszahlen in Verbindung mit Kosten
        patterns = [
            r'(?:costs?|coûts?).{0,20}(\d{4})',
            r'(\d{4}).{0,20}(?:costs?|coûts?)',
            r'as\s+of\s+(\d{4})',
            r'updated\s+(\d{4})'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                year = int(match)
                if 2000 <= year <= datetime.now().year:
                    return str(year)
        
        # Fallback: Current year
        return str(datetime.now().year)
    
    def _format_sources(self, citations: List[Dict]) -> str:
        """Formatiert Quellenangaben für CSV-Output"""
        if not citations:
            return ""
        
        source_urls = []
        for citation in citations[:5]:  # Limit to 5 sources
            url = citation.get('url', '')
            title = citation.get('title', '')
            
            if url:
                if title and len(title) < 50:
                    source_urls.append(f"{title}: {url}")
                else:
                    source_urls.append(url)
        
        return "; ".join(source_urls)
    
    def _generate_mine_id(self, mine_name: str) -> str:
        """Generiert eindeutige Mine-ID"""
        import hashlib
        hash_input = mine_name.lower().encode('utf-8')
        return str(abs(int(hashlib.md5(hash_input).hexdigest(), 16)) % 10000)
```

---

## 🖥️ **SCHRITT 3: GUI INTEGRATION**

### **3.1 GUI Erweiterung für Web Research:**

```python
# src/gui_integration.py
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import asyncio
import threading
from typing import List
import pandas as pd
from datetime import datetime

from src.web_researcher import WebMiningResearcher
from config.settings import ProjectSettings

class WebResearchGUIExtension:
    """
    Erweitert bestehende MineExtractor GUI um Web-Research-Funktionalität
    Integriert nahtlos als neuer Tab
    """
    
    def __init__(self, parent_gui, parent_notebook):
        self.parent_gui = parent_gui
        self.parent_notebook = parent_notebook
        
        # Web Research Engine
        self.web_researcher = None
        self.is_researching = False
        
        # GUI Variables
        self.mine_names_input = tk.StringVar()
        self.api_key_perplexity = tk.StringVar()
        self.max_mines = tk.StringVar(value="10")
        self.output_file_web = tk.StringVar(value=r"C:\Temp\Minen\web_research_results.csv")
        
        # Progress tracking
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="✅ Ready for Web Research")
        
        # Setup Web Research Tab
        self.setup_web_research_tab()
    
    def setup_web_research_tab(self):
        """Erstellt Web Research Tab in bestehender GUI"""
        
        # Create Web Research Tab
        web_tab = ttk.Frame(self.parent_notebook)
        self.parent_notebook.add(web_tab, text="🌐 Web Research")
        
        # Main container
        main_frame = ttk.Frame(web_tab, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, 
                               text="🌐 Web Mining Research v1.0", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Description
        desc_text = "Automatische Web-Recherche für Mining-Daten aus Internetquellen"
        ttk.Label(main_frame, text=desc_text, foreground="blue").grid(row=1, column=0, columnspan=3, pady=(0, 15))
        
        # API Configuration Section
        self.setup_api_config_section(main_frame, row=2)
        
        # Input Section
        self.setup_input_section(main_frame, row=3)
        
        # Options Section
        self.setup_options_section(main_frame, row=4)
        
        # Control Buttons
        self.setup_control_buttons(main_frame, row=5)
        
        # Progress Section
        self.setup_progress_section(main_frame, row=6)
        
        # Results Display
        self.setup_results_section(main_frame, row=7)
    
    def setup_api_config_section(self, parent, row):
        """API-Konfiguration Section"""
        
        api_frame = ttk.LabelFrame(parent, text="🔑 API Configuration", padding="10")
        api_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        api_frame.columnconfigure(1, weight=1)
        
        # Perplexity API Key
        ttk.Label(api_frame, text="Perplexity API Key:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(api_frame, textvariable=self.api_key_perplexity, show="*", width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=2)
        ttk.Button(api_frame, text="Test", command=self.test_perplexity_api).grid(row=0, column=2, pady=2)
        
        # API Status
        self.api_status_label = ttk.Label(api_frame, text="❌ API nicht konfiguriert", foreground="red")
        self.api_status_label.grid(row=1, column=0, columnspan=3, pady=5)
    
    def setup_input_section(self, parent, row):
        """Mine Input Section"""
        
        input_frame = ttk.LabelFrame(parent, text="📝 Mine Names Input", padding="10")
        input_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        # Instructions
        instructions = """Geben Sie Mine-Namen ein (eine pro Zeile oder komma-getrennt):
Beispiele: Éléonore, Canadian Malartic, Raglan, Casa Berardi, LaRonde"""
        ttk.Label(input_frame, text=instructions, foreground="gray").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # Text Input
        self.mine_input_text = tk.Text(input_frame, height=8, width=60, wrap=tk.WORD)
        self.mine_input_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        
        # Scrollbar for text input
        input_scrollbar = ttk.Scrollbar(input_frame, orient=tk.VERTICAL, command=self.mine_input_text.yview)
        input_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S), pady=2)
        self.mine_input_text.configure(yscrollcommand=input_scrollbar.set)
        
        # Quick fill buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, pady=(5, 0))
        
        ttk.Button(button_frame, text="Quebec Top 10", command=self.fill_quebec_top_mines).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Gold Mines", command=self.fill_gold_mines).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Clear", command=self.clear_mine_input).pack(side=tk.LEFT, padx=2)
    
    def setup_options_section(self, parent, row):
        """Options Section"""
        
        options_frame = ttk.LabelFrame(parent, text="⚙️ Options", padding="10")
        options_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Max mines
        ttk.Label(options_frame, text="Max. Mines:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(options_frame, textvariable=self.max_mines, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Output file
        ttk.Label(options_frame, text="Output CSV:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(options_frame, textvariable=self.output_file_web, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=2)
        ttk.Button(options_frame, text="📂", command=self.browse_output_file).grid(row=1, column=2, pady=2)
    
    def setup_control_buttons(self, parent, row):
        """Control Buttons Section"""
        
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=3, pady=15)
        
        # Main action button
        self.start_button = ttk.Button(button_frame, text="🚀 Start Web Research", command=self.start_web_research)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        # Stop button
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop", command=self.stop_web_research, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Open results button
        self.open_results_button = ttk.Button(button_frame, text="📁 Open Results", command=self.open_results, state=tk.DISABLED)
        self.open_results_button.pack(side=tk.LEFT, padx=5)
    
    def setup_progress_section(self, parent, row):
        """Progress Section"""
        
        progress_frame = ttk.LabelFrame(parent, text="📊 Progress", padding="10")
        progress_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        
        # Status label
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var, font=("Arial", 10, "bold"))
        self.status_label.grid(row=1, column=0, pady=2)
    
    def setup_results_section(self, parent, row):
        """Results Display Section"""
        
        results_frame = ttk.LabelFrame(parent, text="📝 Research Log", padding="10")
        results_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(results_frame, height=12, wrap=tk.WORD, 
                                                 font=("Consolas", 9), background="#f8f9fa")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Log control buttons
        log_button_frame = ttk.Frame(results_frame)
        log_button_frame.grid(row=1, column=0, pady=(5, 0))
        
        ttk.Button(log_button_frame, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(log_button_frame, text="Copy Log", command=self.copy_log).pack(side=tk.LEFT, padx=2)
        
        # Initial log message
        self.log("🌐 Web Mining Research System v1.0 initialized")
        self.log("💡 Enter mine names and configure Perplexity API key to start")
    
    def fill_quebec_top_mines(self):
        """Füllt Eingabefeld mit Quebec Top-Minen"""
        top_mines = [
            "Éléonore",
            "Canadian Malartic", 
            "Raglan",
            "Casa Berardi",
            "LaRonde",
            "Mont Wright",
            "Lac Tio",
            "Troilus",
            "Niobec",
            "Beaufor"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(top_mines))
        self.log(f"📝 Loaded {len(top_mines)} Quebec top mines")
    
    def fill_gold_mines(self):
        """Füllt Eingabefeld mit Gold-Minen"""
        gold_mines = [
            "Éléonore",
            "Canadian Malartic",
            "Casa Berardi", 
            "LaRonde",
            "Goldex",
            "Beaufor",
            "Sigma",
            "Kiena"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(gold_mines))
        self.log(f"📝 Loaded {len(gold_mines)} Quebec gold mines")
    
    def clear_mine_input(self):
        """Leert Eingabefeld"""
        self.mine_input_text.delete(1.0, tk.END)
        self.log("🗑️ Mine input cleared")
    
    def test_perplexity_api(self):
        """Testet Perplexity API Key"""
        api_key = self.api_key_perplexity.get().strip()
        
        if not api_key:
            messagebox.showerror("Error", "Please enter Perplexity API Key")
            return
        
        self.log("🧪 Testing Perplexity API...")
        
        # Run test in separate thread
        threading.Thread(target=self._test_api_thread, args=(api_key,), daemon=True).start()
    
    def _test_api_thread(self, api_key):
        """API Test in separatem Thread"""
        try:
            # Create test researcher
            from src.web_researcher import WebMiningResearcher
            
            # Quick test with minimal request
            test_researcher = WebMiningResearcher({'perplexity_key': api_key})
            
            # Simple test query
            asyncio.run(self._run_api_test(test_researcher))
            
            # Update GUI from main thread
            self.parent_gui.root.after(0, self._api_test_success)
            
        except Exception as e:
            self.parent_gui.root.after(0, lambda: self._api_test_failed(str(e)))
    
    async def _run_api_test(self, researcher):
        """Führt API-Test durch"""
        # Simple test request
        result = await researcher.test_api_connection()
        return result
    
    def _api_test_success(self):
        """API Test erfolgreich"""
        self.api_status_label.config(text="✅ Perplexity API connected", foreground="green")
        self.log("✅ Perplexity API test successful!")
        messagebox.showinfo("Success", "Perplexity API connected successfully!")
    
    def _api_test_failed(self, error):
        """API Test fehlgeschlagen"""
        self.api_status_label.config(text="❌ API connection failed", foreground="red")
        self.log(f"❌ API test failed: {error}")
        messagebox.showerror("API Test Failed", f"Error: {error}")
    
    def start_web_research(self):
        """Startet Web Research"""
        
        # Validierung
        if not self._validate_research_inputs():
            return
        
        # UI Updates
        self.is_researching = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.open_results_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("🚀 Starting web research...")
        
        # Parse mine names
        mine_names = self._parse_mine_names()
        self.log(f"🎯 Starting research for {len(mine_names)} mines")
        
        # Start research in separate thread
        research_thread = threading.Thread(target=self._research_thread, args=(mine_names,), daemon=True)
        research_thread.start()
    
    def _validate_research_inputs(self) -> bool:
        """Validiert Research-Eingaben"""
        
        # API Key check
        if not self.api_key_perplexity.get().strip():
            messagebox.showerror("Error", "Please configure Perplexity API Key")
            return False
        
        # Mine names check
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        if not mine_text:
            messagebox.showerror("Error", "Please enter mine names")
            return False
        
        # Output file check
        if not self.output_file_web.get().strip():
            messagebox.showerror("Error", "Please specify output file")
            return False
        
        return True
    
    def _parse_mine_names(self) -> List[str]:
        """Parst Mine-Namen aus Eingabefeld"""
        
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        
        # Split by newlines or commas
        if '\n' in mine_text:
            mine_names = [name.strip() for name in mine_text.split('\n')]
        else:
            mine_names = [name.strip() for name in mine_text.split(',')]
        
        # Filter empty names
        mine_names = [name for name in mine_names if name]
        
        # Apply max limit
        max_mines = int(self.max_mines.get() or 10)
        mine_names = mine_names[:max_mines]
        
        return mine_names
    
    def _research_thread(self, mine_names: List[str]):
        """Research in separatem Thread"""
        try:
            # Create researcher
            from src.web_researcher import WebMiningResearcher
            
            config = {
                'perplexity_key': self.api_key_perplexity.get().strip()
            }
            
            # Run async research
            results_df = asyncio.run(self._run_research(mine_names, config))
            
            # Save results
            output_path = self.output_file_web.get()
            self._save_results_csv(results_df, output_path)
            
            # Success callback
            self.parent_gui.root.after(0, lambda: self._research_completed_success(len(results_df)))
            
        except Exception as e:
            # Error callback
            self.parent_gui.root.after(0, lambda: self._research_completed_error(str(e)))
    
    async def _run_research(self, mine_names: List[str], config: dict) -> pd.DataFrame:
        """Führt Web Research durch"""
        
        researcher = WebMiningResearcher(config)
        
        results = []
        for i, mine_name in enumerate(mine_names):
            if not self.is_researching:  # Check for stop signal
                break
            
            # Update progress
            progress = (i / len(mine_names)) * 100
            self.parent_gui.root.after(0, lambda p=progress: self.progress_var.set(p))
            self.parent_gui.root.after(0, lambda n=mine_name, idx=i+1, total=len(mine_names): 
                                     self.status_var.set(f"🔍 ({idx}/{total}) Researching {n}..."))
            
            try:
                # Research individual mine
                mine_data = await researcher.research_mine_comprehensive(mine_name)
                
                # Convert to dict for DataFrame
                results.append(mine_data.__dict__)
                
                # Log progress
                self.parent_gui.root.after(0, lambda n=mine_name: self.log(f"✅ Completed: {n}"))
                
            except Exception as e:
                # Log error but continue
                error_data = {field: "" for field in ProjectSettings.CSV_HEADERS}
                error_data['Name'] = mine_name
                error_data['Quellenangaben'] = f"Error: {str(e)}"
                results.append(error_data)
                
                self.parent_gui.root.after(0, lambda n=mine_name, err=str(e): 
                                         self.log(f"❌ Error {n}: {err}"))
        
        return pd.DataFrame(results)
    
    def _save_results_csv(self, df: pd.DataFrame, output_path: str):
        """Speichert Ergebnisse als CSV"""
        
        # Rename columns to match exact PDF system format
        column_mapping = {
            'id': 'ID',
            'name': 'Name',
            'betreiber': 'Betreiber',
            'x_koordinate': 'x-Koordinate',
            'y_koordinate': 'y-Koordinate',
            'aktivitaetsstatus': 'Aktivitätsstatus',
            'aktivitaetsstatus_detail': 'Aktivitätsstatus (aktiv, geplant, geschlossen, sonstiges)',
            'restaurationskosten_cad': 'Restaurationskosten in $ CAD',
            'jahr_kostenaufnahme': 'Jahr der Aufnahme der Kosten',
            'jahr_dokumenterstellung': 'Jahr der Erstellung des Dokumentes',
            'rohstoffabbau': 'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)',
            'minentyp': 'Minentyp (Untertage, Open-Pit, usw.)',
            'produktionsstart': 'Produktionsstart',
            'produktionsende': 'Produktionsende',
            'foerdermenge_jahr': 'Fördermenge/Jahr',
            'flaeche_km2': 'Fläche der Mine in qkm',
            'quellenangaben': 'Quellenangaben'
        }
        
        df_renamed = df.rename(columns=column_mapping)
        
        # Ensure all expected columns exist
        for header in ProjectSettings.CSV_HEADERS:
            if header not in df_renamed.columns:
                df_renamed[header] = ""
        
        # Reorder columns to match expected format
        df_final = df_renamed[ProjectSettings.CSV_HEADERS]
        
        # Save with same format as PDF system
        df_final.to_csv(output_path, index=False, 
                       encoding=ProjectSettings.CSV_ENCODING, 
                       sep=ProjectSettings.CSV_DELIMITER)
    
    def _research_completed_success(self, num_results: int):
        """Research erfolgreich abgeschlossen"""
        
        self.is_researching = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.open_results_button.config(state=tk.NORMAL)
        self.progress_var.set(100)
        self.status_var.set(f"✅ Research completed! {num_results} mines processed")
        
        self.log(f"🎉 Web research completed successfully!")
        self.log(f"📊 Results: {num_results} mines processed")
        self.log(f"💾 Output saved to: {self.output_file_web.get()}")
        
        messagebox.showinfo("Success", f"Web research completed!\n{num_results} mines processed")
    
    def _research_completed_error(self, error: str):
        """Research mit Fehler abgebrochen"""
        
        self.is_researching = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("❌ Research failed")
        
        self.log(f"❌ Research failed: {error}")
        messagebox.showerror("Research Failed", f"Error: {error}")
    
    def stop_web_research(self):
        """Stoppt Web Research"""
        self.is_researching = False
        self.log("⏹️ Stopping web research...")
        self.status_var.set("⏹️ Stopping research...")
    
    def open_results(self):
        """Öffnet Ergebnis-Datei"""
        import os
        import subprocess
        
        output_path = self.output_file_web.get()
        if os.path.exists(output_path):
            try:
                os.startfile(output_path)  # Windows
                self.log(f"📁 Opened: {os.path.basename(output_path)}")
            except:
                messagebox.showinfo("File Location", f"Results saved to:\n{output_path}")
        else:
            messagebox.showerror("File Not Found", f"Results file not found:\n{output_path}")
    
    def browse_output_file(self):
        """Datei-Browser für Output-Datei"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            title="Save Web Research Results",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            self.output_file_web.set(filename)
            self.log(f"💾 Output file: {os.path.basename(filename)}")
    
    def log(self, message: str):
        """Fügt Log-Nachricht hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Update GUI
        if hasattr(self.parent_gui, 'root'):
            self.parent_gui.root.update_idletasks()
    
    def clear_log(self):
        """Leert Log"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 Log cleared")
    
    def copy_log(self):
        """Kopiert Log in Zwischenablage"""
        log_content = self.log_text.get(1.0, tk.END)
        self.parent_gui.root.clipboard_clear()
        self.parent_gui.root.clipboard_append(log_content)
        self.log("📋 Log copied to clipboard")
```

---

## ⚡ **SCHRITT 4: MAIN IMPLEMENTATION**

### **4.1 Web Research Engine:**

```python
# src/web_researcher.py
import asyncio
from typing import Dict, List, Optional
from loguru import logger

from src.perplexity_client import PerplexityClient
from src.data_parser import MiningDataParser, MineDataFields
from config.api_keys import APIConfig

class WebMiningResearcher:
    """
    Hauptklasse für Web-basierte Mining-Daten-Recherche
    Phase 1: Fokus auf Perplexity AI Integration
    """
    
    def __init__(self, config: Dict[str, str]):
        self.config = config
        self.parser = MiningDataParser()
        
        # Validate required configuration
        if 'perplexity_key' not in config or not config['perplexity_key']:
            raise ValueError("Perplexity API key required in config")
    
    async def research_mine_comprehensive(self, mine_name: str) -> MineDataFields:
        """
        Führt umfassende Recherche für eine einzelne Mine durch
        """
        
        logger.info(f"Starting comprehensive research for: {mine_name}")
        
        try:
            async with PerplexityClient() as client:
                # Set API key
                client.api_key = self.config['perplexity_key']
                
                # Execute research
                research_result = await client.research_mine_comprehensive(mine_name)
                
                if research_result['success']:
                    # Parse results to structured data
                    mine_data = self.parser.parse_perplexity_response(
                        research_result, mine_name
                    )
                    
                    logger.info(f"Research completed for {mine_name}")
                    return mine_data
                
                else:
                    # Return empty data with error info
                    empty_data = MineDataFields()
                    empty_data.name = mine_name
                    empty_data.quellenangaben = "Research failed"
                    return empty_data
                    
        except Exception as e:
            logger.error(f"Research failed for {mine_name}: {e}")
            
            # Return error data
            error_data = MineDataFields()
            error_data.name = mine_name
            error_data.quellenangaben = f"Error: {str(e)}"
            return error_data
    
    async def test_api_connection(self) -> bool:
        """
        Testet API-Verbindung mit minimaler Anfrage
        """
        
        try:
            async with PerplexityClient() as client:
                client.api_key = self.config['perplexity_key']
                
                # Simple test query
                test_result = await client.research_mine_comprehensive("Test Mine")
                return test_result.get('success', False)
                
        except Exception as e:
            logger.error(f"API test failed: {e}")
            return False
    
    async def research_mine_list(self, mine_names: List[str]) -> List[MineDataFields]:
        """
        Recherchiert Liste von Minen parallel
        """
        
        logger.info(f"Starting batch research for {len(mine_names)} mines")
        
        # Process mines sequentially to respect API limits
        results = []
        for mine_name in mine_names:
            result = await self.research_mine_comprehensive(mine_name)
            results.append(result)
            
            # Rate limiting
            await asyncio.sleep(1)
        
        logger.info(f"Batch research completed: {len(results)} results")
        return results
```

### **4.2 Main Entry Point:**

```python
# main.py
import asyncio
import sys
import tkinter as tk
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.gui_integration import WebResearchGUIExtension
from config.settings import ProjectSettings

def main():
    """
    Hauptfunktion für Phase 1 Implementation
    Kann standalone oder als Erweiterung genutzt werden
    """
    
    print("🌐 MineExtractorWeb v1.0 - Phase 1")
    print("=" * 50)
    
    try:
        # Check if this is integration mode or standalone
        if len(sys.argv) > 1 and sys.argv[1] == "standalone":
            run_standalone_gui()
        else:
            print("ℹ️  Integration mode - use integrate_with_existing_gui()")
            print("   For standalone: python main.py standalone")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

def run_standalone_gui():
    """Startet eigenständige GUI für Testing"""
    
    root = tk.Tk()
    root.title("MineExtractorWeb v1.0 - Standalone")
    root.geometry("900x700")
    
    # Create notebook
    notebook = tk.ttk.Notebook(root)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Mock parent GUI for testing
    class MockParentGUI:
        def __init__(self):
            self.root = root
    
    parent_gui = MockParentGUI()
    
    # Create web research extension
    web_extension = WebResearchGUIExtension(parent_gui, notebook)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() - root.winfo_width()) // 2
    y = (root.winfo_screenheight() - root.winfo_height()) // 2
    root.geometry(f"+{x}+{y}")
    
    print("✅ Standalone GUI started")
    root.mainloop()

def integrate_with_existing_gui(existing_gui, existing_notebook):
    """
    Integriert Web Research in bestehende MineExtractor GUI
    
    Args:
        existing_gui: Bestehende MineExtractorGUI Instanz
        existing_notebook: Bestehende ttk.Notebook Instanz
    
    Returns:
        WebResearchGUIExtension Instanz
    """
    
    print("🔗 Integrating Web Research into existing GUI...")
    
    try:
        web_extension = WebResearchGUIExtension(existing_gui, existing_notebook)
        print("✅ Web Research successfully integrated!")
        return web_extension
        
    except Exception as e:
        print(f"❌ Integration failed: {e}")
        raise

if __name__ == "__main__":
    main()
```

---

## 📋 **TESTING & VALIDATION**

### **5.1 Test Script:**

```python
# tests/test_phase1.py
import asyncio
import os
from src.web_researcher import WebMiningResearcher

async def test_phase1_implementation():
    """
    Comprehensive testing für Phase 1
    """
    
    print("🧪 Testing Phase 1 Implementation")
    print("=" * 40)
    
    # Configuration
    config = {
        'perplexity_key': os.getenv('PERPLEXITY_API_KEY', 'test-key')
    }
    
    if config['perplexity_key'] == 'test-key':
        print("⚠️  Warning: Using test API key")
        print("   Set PERPLEXITY_API_KEY environment variable for real testing")
        return
    
    # Test mines
    test_mines = ['Éléonore', 'Canadian Malartic', 'Raglan']
    
    try:
        # Initialize researcher
        researcher = WebMiningResearcher(config)
        
        # Test API connection
        print("🔌 Testing API connection...")
        api_connected = await researcher.test_api_connection()
        
        if api_connected:
            print("✅ API connection successful")
        else:
            print("❌ API connection failed")
            return
        
        # Test individual mine research
        print("\n🔍 Testing individual mine research...")
        for mine_name in test_mines:
            print(f"   Testing: {mine_name}")
            
            result = await researcher.research_mine_comprehensive(mine_name)
            
            # Validate result
            filled_fields = sum(1 for field in result.__dict__.values() 
                              if field and str(field).strip())
            
            print(f"   ✅ {mine_name}: {filled_fields}/17 fields filled")
            
            if result.restaurationskosten_cad:
                print(f"      💰 Restoration costs: {result.restaurationskosten_cad} CAD")
            
            if result.betreiber:
                print(f"      🏢 Operator: {result.betreiber}")
        
        print("\n✅ Phase 1 testing completed successfully!")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_phase1_implementation())
```

---

## ✅ **DEPLOYMENT CHECKLIST**

### **Phase 1 Go-Live Checklist:**

```markdown
## Pre-Deployment
- [ ] Perplexity API Account erstellt und getestet
- [ ] Environment Variables konfiguriert
- [ ] Alle Dependencies installiert
- [ ] Test mit mindestens 5 Quebec-Minen erfolgreich

## Deployment
- [ ] Code in Zielverzeichnis kopiert
- [ ] Integration in bestehende GUI getestet
- [ ] CSV-Output Format validiert (identisch mit PDF-System)
- [ ] Error Handling getestet

## Post-Deployment
- [ ] User Training durchgeführt
- [ ] Performance Monitoring aktiviert
- [ ] Backup der Konfiguration erstellt
- [ ] Phase 2 Planung initiiert
```

---

**Status:** ✅ Phase 1 Implementation Guide Complete  
**Geschätzte Implementierungszeit:** 1-2 Wochen  
**Nächster Schritt:** Code Implementation und Testing  

*Diese Implementierung bietet eine solide Grundlage für die Multi-System-Erweiterung in Phase 2.*