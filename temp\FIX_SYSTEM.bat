@echo off
echo.
echo ========================================
echo   🔧 MINE EXTRACTOR WEB v1.0 - FIX
echo   Dependencies Repair & System Fix
echo ========================================
echo.

REM Verwende miniforge Python
set PYTHON_CMD=C:\ProgramData\miniforge3\python.exe

REM Prüfe Python-Verfügbarkeit
%PYTHON_CMD% --version >nul 2>&1
if errorlevel 1 (
    echo ❌ miniforge Python nicht gefunden, versuche Standard Python...
    set PYTHON_CMD=python
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Kein Python gefunden!
        echo.
        echo 📥 Bitte installiere Python von:
        echo    https://github.com/conda-forge/miniforge
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Python gefunden: %PYTHON_CMD%
%PYTHON_CMD% --version
echo.

echo 🔧 SCHRITT 1: Dependencies reparieren...
echo.

if exist "fix_dependencies.py" (
    echo 📦 Führe Dependencies-Fix aus...
    %PYTHON_CMD% fix_dependencies.py
    
    if errorlevel 1 (
        echo ❌ Dependencies-Fix fehlgeschlagen
        echo 🔄 Versuche manuelle Installation...
        goto MANUAL_FIX
    ) else (
        echo ✅ Dependencies erfolgreich repariert!
    )
) else (
    echo ⚠️  fix_dependencies.py nicht gefunden, versuche manuelle Installation...
    goto MANUAL_FIX
)

goto TEST_SYSTEM

:MANUAL_FIX
echo.
echo 🔧 MANUELLE DEPENDENCIES-INSTALLATION
echo.

echo 📦 Installiere essentielle Pakete...
%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install pandas
%PYTHON_CMD% -m pip install aiohttp
%PYTHON_CMD% -m pip install python-dotenv
%PYTHON_CMD% -m pip install requests
%PYTHON_CMD% -m pip install tenacity
%PYTHON_CMD% -m pip install colorama

echo ✅ Manuelle Installation abgeschlossen
echo.

:TEST_SYSTEM
echo 🧪 SCHRITT 2: System testen...
echo.

if exist "final_test.py" (
    echo 🔍 Führe Systemtest aus...
    %PYTHON_CMD% final_test.py
    
    if errorlevel 1 (
        echo ⚠️  Systemtest ergab Probleme, aber System könnte trotzdem funktional sein
    ) else (
        echo ✅ Systemtest erfolgreich!
    )
) else (
    echo ⚠️  final_test.py nicht gefunden, teste Basic-Import...
    %PYTHON_CMD% -c "import sys; sys.path.insert(0, 'src'); import mine_data_models; print('✅ Basic imports OK')"
    
    if errorlevel 1 (
        echo ❌ Basic imports fehlgeschlagen
        echo 💡 Eventuell sind noch weitere Dependencies nötig
    )
)

echo.
echo 🚀 SCHRITT 3: System starten...
echo.

set /p start_choice="System jetzt starten? (j/n): "

if /i "%start_choice%"=="j" (
    echo.
    echo 🖥️  Starte MineExtractorWeb GUI...
    %PYTHON_CMD% main.py
    
    if errorlevel 1 (
        echo.
        echo ❌ GUI-Start fehlgeschlagen
        echo.
        echo 🛠️  Weitere Optionen:
        echo    1. python main.py --config    (Konfiguration prüfen)
        echo    2. python main.py --test-apis (APIs testen)
        echo    3. python tools\api_key_manager.py (API Keys konfigurieren)
        echo.
    )
) else (
    echo.
    echo 💡 System bereit für manuellen Start:
    echo    • python main.py (GUI starten)
    echo    • python main.py --config (Konfiguration prüfen)
    echo    • python tools\api_key_manager.py (API Keys konfigurieren)
)

echo.
echo ✅ Fix-Prozess abgeschlossen!
echo.
pause
