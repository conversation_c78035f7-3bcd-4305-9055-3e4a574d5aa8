#!/usr/bin/env python3
"""
Quick System Test für MineExtractorWeb v1.0
"""

import sys
import os
from pathlib import Path

# Set project path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_data_models():
    """Test data models"""
    try:
        from mine_data_models import MineDataFields, MineResearchResult
        
        mine = MineDataFields(
            name="Test Mine",
            betreiber="Test Company", 
            aktivitaetsstatus="aktiv"
        )
        
        print("✅ Data Models working")
        print(f"   Completion: {mine.get_completion_rate():.1%}")
        return True
    except Exception as e:
        print(f"❌ Data Models error: {e}")
        return False

def test_data_parser():
    """Test data parser"""
    try:
        from data_parser import MiningDataParser, quick_parse
        
        sample_content = "Test Mine is operated by Test Company. The mine is active."
        result = quick_parse(sample_content, "Test Mine")
        
        print("✅ Data Parser working")
        print(f"   Parsed: {result.name}")
        return True
    except Exception as e:
        print(f"❌ Data Parser error: {e}")
        return False

def test_web_researcher():
    """Test web researcher"""
    try:
        from web_researcher import WebMiningResearcher
        
        config = {'perplexity_key': 'test-key'}
        researcher = WebMiningResearcher(config)
        
        print("✅ Web Researcher working")
        print(f"   APIs configured: {len(researcher.api_clients)}")
        return True
    except Exception as e:
        print(f"❌ Web Researcher error: {e}")
        return False

def test_configuration():
    """Test configuration"""
    try:
        from config.settings import ProjectSettings
        from config.mining_prompts import PromptBuilder
        
        info = ProjectSettings.get_project_info()
        prompt = PromptBuilder.build_comprehensive_prompt("Test Mine")
        
        print("✅ Configuration working")
        print(f"   Project: {info['name']} v{info['version']}")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def main():
    print("🧪 MineExtractorWeb v1.0 - Quick System Test")
    print("=" * 50)
    
    tests = [
        ("Data Models", test_data_models),
        ("Data Parser", test_data_parser), 
        ("Web Researcher", test_web_researcher),
        ("Configuration", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        if test_func():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is functional.")
        print("\n🎯 Next Steps:")
        print("   1. Configure API keys in .env file")
        print("   2. Run: python main.py --test-apis")
        print("   3. Start GUI: python main.py")
    else:
        print("⚠️  Some tests failed. Check error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
