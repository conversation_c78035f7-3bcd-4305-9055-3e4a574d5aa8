#!/usr/bin/env python3
"""
Simple System Test für MineExtractorWeb v1.0
Prüft grundlegende Funktionalität nach Dependencies-Fix
"""

import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_basic_imports():
    """Testet grundlegende Imports"""
    print("🔍 Testing basic imports...")
    
    try:
        # Test core models
        import mine_data_models
        from mine_data_models import MineDataFields
        print("   ✅ mine_data_models - OK")
        
        # Test data parser
        import data_parser
        from data_parser import MiningDataParser
        print("   ✅ data_parser - OK")
        
        # Test web researcher (might fail due to dependencies)
        try:
            import web_researcher
            from web_researcher import WebMiningResearcher
            print("   ✅ web_researcher - OK")
        except ImportError as e:
            print(f"   ⚠️  web_researcher - Missing dependencies: {e}")
        
        # Test configuration
        try:
            from config.settings import ProjectSettings
            print("   ✅ config.settings - OK")
        except ImportError as e:
            print(f"   ❌ config.settings - Error: {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Basic import failed: {e}")
        return False

def test_basic_functionality():
    """Testet grundlegende Funktionalität"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test data model creation
        from mine_data_models import MineDataFields
        
        mine = MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv"
        )
        
        assert mine.name == "Test Mine"
        assert mine.get_completion_rate() > 0
        print("   ✅ Data model creation - OK")
        
        # Test CSV conversion
        csv_data = mine.to_dict()
        assert "Name" in csv_data
        assert csv_data["Name"] == "Test Mine"
        print("   ✅ CSV conversion - OK")
        
        # Test data parser (basic)
        try:
            from data_parser import quick_parse
            
            sample_text = "Test Mine is operated by Test Company. Status is active."
            parsed = quick_parse(sample_text, "Test Mine")
            
            assert parsed.name == "Test Mine"
            print("   ✅ Data parser basic test - OK")
            
        except ImportError:
            print("   ⚠️  Data parser - Skipped due to missing dependencies")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        return False

def test_dependencies():
    """Testet kritische Dependencies"""
    print("\n📦 Testing critical dependencies...")
    
    critical_deps = {
        "pandas": "Data processing",
        "aiohttp": "Async HTTP client", 
        "dotenv": "Environment variables",
        "requests": "HTTP requests"
    }
    
    available = 0
    
    for dep, description in critical_deps.items():
        try:
            if dep == "dotenv":
                import dotenv
            else:
                __import__(dep)
            print(f"   ✅ {dep} - {description}")
            available += 1
        except ImportError:
            print(f"   ❌ {dep} - {description} (MISSING)")
    
    print(f"\n   📊 Dependencies: {available}/{len(critical_deps)} available")
    
    if available >= 3:
        print("   ✅ Sufficient dependencies for basic operation")
        return True
    else:
        print("   ❌ Too few dependencies - system may not work properly")
        return False

def test_gui_availability():
    """Testet GUI-Verfügbarkeit"""
    print("\n🖥️  Testing GUI availability...")
    
    try:
        import tkinter
        print("   ✅ tkinter - GUI framework available")
        
        try:
            import gui_integration
            print("   ✅ gui_integration - GUI module available")
        except ImportError as e:
            print(f"   ⚠️  gui_integration - Partial availability: {e}")
        
        return True
        
    except ImportError:
        print("   ❌ tkinter - GUI not available (install python3-tk on Linux)")
        return False

def main():
    """Hauptfunktion"""
    print("🧪 MineExtractorWeb v1.0 - Simple System Test")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Dependencies", test_dependencies),
        ("Basic Functionality", test_basic_functionality),
        ("GUI Availability", test_gui_availability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        print("\n🚀 Next steps:")
        print("   1. Configure API keys: python tools/api_key_manager.py")
        print("   2. Start GUI: python main.py")
        
    elif passed >= total - 1:
        print("✅ System mostly functional with minor issues.")
        print("   Try running the system - it should work.")
        
    else:
        print("⚠️  System has significant issues.")
        print("   Run FIX_SYSTEM.bat to repair dependencies.")
    
    return passed >= (total - 1)

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    input("Press Enter to exit...")
    
    sys.exit(0 if success else 1)
