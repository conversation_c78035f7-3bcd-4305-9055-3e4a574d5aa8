"""
Project Settings für MineExtractorWeb v1.0
Zentrale Konfiguration für alle Projekteinstellungen
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

class ProjectSettings:
    """Zentrale Projekt-Konfiguration"""
    
    # Project Information
    PROJECT_NAME = "MineExtractorWeb"
    PROJECT_VERSION = "1.0.0"
    PROJECT_DESCRIPTION = "Multi-System Web Research Engine für Quebec Mining Data"
    
    # Directory Structure
    PROJECT_ROOT = Path(__file__).parent.parent
    SRC_DIR = PROJECT_ROOT / "src"
    CONFIG_DIR = PROJECT_ROOT / "config"
    DOCS_DIR = PROJECT_ROOT / "Doku"
    TESTS_DIR = PROJECT_ROOT / "tests"
    LOGS_DIR = PROJECT_ROOT / "logs"
    OUTPUT_DIR = PROJECT_ROOT / "output"
    CACHE_DIR = PROJECT_ROOT / "cache"
    DEMOS_DIR = PROJECT_ROOT / "demos"
    TOOLS_DIR = PROJECT_ROOT / "tools"
    
    # File Formats and Encoding
    CSV_ENCODING = 'utf-8-sig'
    CSV_DELIMITER = '|'  # Kompatibel mit bestehendem PDF-System
    JSON_ENCODING = 'utf-8'
    LOG_ENCODING = 'utf-8'
    
    # CSV Headers (kompatibel mit bestehendem PDF-System)
    CSV_HEADERS = [
        'ID',
        'Name',
        'Betreiber',
        'x-Koordinate',
        'y-Koordinate',
        'Aktivitätsstatus',
        'Aktivitätsstatus (aktiv, geplant, geschlossen, sonstiges)',
        'Restaurationskosten in $ CAD',
        'Jahr der Aufnahme der Kosten',
        'Jahr der Erstellung des Dokumentes',
        'Rohstoffabbau (Gold, Kupfer, Kohle, usw.)',
        'Minentyp (Untertage, Open-Pit, usw.)',
        'Produktionsstart',
        'Produktionsende',
        'Fördermenge/Jahr',
        'Fläche der Mine in qkm',
        'Quellenangaben'
    ]
    
    # Research Settings
    DEFAULT_MAX_MINES = 10
    DEFAULT_TIMEOUT_SECONDS = 120
    DEFAULT_BATCH_DELAY = 2.0
    DEFAULT_MAX_RETRIES = 3
    
    # Performance Settings
    MAX_CONCURRENT_REQUESTS = 3
    RATE_LIMIT_DELAY = 2.0
    REQUEST_TIMEOUT = 120
    MAX_SOURCES_PER_MINE = 10
    
    # Cache Settings
    ENABLE_CACHE = True
    CACHE_DURATION_HOURS = 24
    CACHE_MAX_SIZE_MB = 500
    
    # Logging Settings
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_MAX_FILES = 10
    LOG_MAX_SIZE_MB = 50
    
    # Quality Thresholds
    MIN_COMPLETION_RATE = 0.3  # Minimum completion rate to consider valid
    MIN_CONFIDENCE_SCORE = 0.5  # Minimum confidence score
    MIN_CRITICAL_FIELDS = 3  # Minimum critical fields required
    
    # Critical Fields (must have at least MIN_CRITICAL_FIELDS)
    CRITICAL_FIELDS = [
        'name',
        'betreiber',
        'aktivitaetsstatus',
        'restaurationskosten_cad',
        'rohstoffabbau'
    ]
    
    # Quebec-specific Settings
    QUEBEC_REGIONS = [
        'Quebec', 'Québec', 'QC',
        'Abitibi', 'Nord-du-Québec',
        'Côte-Nord', 'Saguenay',
        'Eeyou Istchee', 'Nunavik'
    ]
    
    QUEBEC_COORDINATES = {
        'latitude_range': (45.0, 62.0),
        'longitude_range': (-79.0, -57.0)
    }
    
    # Priority Domains (in order of trust/reliability)
    GOVERNMENT_DOMAINS = [
        'mrnf.gouv.qc.ca',
        'gestim.mines.gouv.qc.ca',
        'sedarplus.ca',
        'nrcan.gc.ca',
        'canada.ca',
        'gov.ca',
        'gc.ca'
    ]
    
    COMPANY_DOMAINS = [
        'newmont.com',
        'agnicoeagle.com',
        'iamgold.com',
        'barrick.com',
        'kinross.com',
        'yamana.com'
    ]
    
    INDUSTRY_DOMAINS = [
        'mining.com',
        'northernminer.com',
        'miningweekly.com',
        'kitco.com',
        's2analytics.com'
    ]
    
    @classmethod
    def ensure_directories(cls):
        """Stellt sicher, dass alle Projektverzeichnisse existieren"""
        
        directories = [
            cls.LOGS_DIR,
            cls.OUTPUT_DIR,
            cls.CACHE_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ Project directories ensured: {len(directories)} directories")
    
    @classmethod
    def get_default_output_path(cls, suffix: str = None) -> Path:
        """
        Generiert Standard-Ausgabepfad für Results
        
        Args:
            suffix: Optional suffix für Dateiname
            
        Returns:
            Path für Output-Datei
        """
        
        cls.ensure_directories()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if suffix:
            filename = f"web_research_{suffix}_{timestamp}.csv"
        else:
            filename = f"web_research_results_{timestamp}.csv"
        
        return cls.OUTPUT_DIR / filename
    
    @classmethod
    def get_log_path(cls, log_name: str = "mineextractor_web") -> Path:
        """
        Generiert Log-Pfad
        
        Args:
            log_name: Name der Log-Datei
            
        Returns:
            Path für Log-Datei
        """
        
        cls.ensure_directories()
        
        return cls.LOGS_DIR / f"{log_name}.log"
    
    @classmethod
    def get_cache_path(cls, cache_name: str) -> Path:
        """
        Generiert Cache-Pfad
        
        Args:
            cache_name: Name der Cache-Datei
            
        Returns:
            Path für Cache-Datei
        """
        
        cls.ensure_directories()
        
        return cls.CACHE_DIR / f"{cache_name}.json"
    
    @classmethod
    def normalize_mine_name(cls, mine_name: str) -> str:
        """
        Normalisiert Mine-Namen
        
        Args:
            mine_name: Original mine name
            
        Returns:
            Normalized mine name
        """
        
        import re
        
        # Remove common prefixes/suffixes
        name = re.sub(r'\s+(mine|mining|project|deposit)$', '', mine_name, flags=re.IGNORECASE)
        name = re.sub(r'^(the\s+)', '', name, flags=re.IGNORECASE)
        
        # Clean extra whitespace
        name = re.sub(r'\s+', ' ', name)
        
        return name.strip()
    
    @classmethod
    def validate_coordinates(cls, latitude: float, longitude: float) -> bool:
        """
        Validiert ob Koordinaten in Quebec liegen
        
        Args:
            latitude: Breitengrad
            longitude: Längengrad
            
        Returns:
            True wenn in Quebec-Region
        """
        
        lat_range = cls.QUEBEC_COORDINATES['latitude_range']
        lon_range = cls.QUEBEC_COORDINATES['longitude_range']
        
        return (lat_range[0] <= latitude <= lat_range[1] and 
                lon_range[0] <= longitude <= lon_range[1])
    
    @classmethod
    def classify_source_domain(cls, url: str) -> str:
        """
        Klassifiziert Domain-Typ
        
        Args:
            url: URL der Quelle
            
        Returns:
            Domain-Typ: 'government', 'company', 'industry', 'other'
        """
        
        if not url:
            return 'other'
        
        url_lower = url.lower()
        
        for domain in cls.GOVERNMENT_DOMAINS:
            if domain in url_lower:
                return 'government'
        
        for domain in cls.COMPANY_DOMAINS:
            if domain in url_lower:
                return 'company'
        
        for domain in cls.INDUSTRY_DOMAINS:
            if domain in url_lower:
                return 'industry'
        
        return 'other'
    
    @classmethod
    def get_source_reliability_score(cls, url: str) -> float:
        """
        Berechnet Zuverlässigkeits-Score für Quelle
        
        Args:
            url: URL der Quelle
            
        Returns:
            Reliability score (0.0 - 1.0)
        """
        
        domain_type = cls.classify_source_domain(url)
        
        reliability_scores = {
            'government': 1.0,
            'company': 0.8,
            'industry': 0.6,
            'other': 0.4
        }
        
        return reliability_scores.get(domain_type, 0.4)
    
    @classmethod
    def get_project_info(cls) -> Dict[str, Any]:
        """
        Gibt umfassende Projekt-Informationen zurück
        
        Returns:
            Dictionary mit Projekt-Details
        """
        
        return {
            'name': cls.PROJECT_NAME,
            'version': cls.PROJECT_VERSION,
            'description': cls.PROJECT_DESCRIPTION,
            'root': str(cls.PROJECT_ROOT),
            'src_dir': str(cls.SRC_DIR),
            'config_dir': str(cls.CONFIG_DIR),
            'docs_dir': str(cls.DOCS_DIR),
            'logs_dir': str(cls.LOGS_DIR),
            'output_dir': str(cls.OUTPUT_DIR),
            'cache_dir': str(cls.CACHE_DIR),
            'csv_encoding': cls.CSV_ENCODING,
            'csv_delimiter': cls.CSV_DELIMITER,
            'critical_fields': cls.CRITICAL_FIELDS,
            'quebec_regions': cls.QUEBEC_REGIONS
        }
    
    @classmethod
    def load_user_config(cls, config_file: Optional[Path] = None) -> Dict[str, Any]:
        """
        Lädt Benutzer-Konfiguration aus Datei
        
        Args:
            config_file: Pfad zur Konfigurationsdatei
            
        Returns:
            User configuration dictionary
        """
        
        if config_file is None:
            config_file = cls.PROJECT_ROOT / "user_config.json"
        
        if not config_file.exists():
            return {}
        
        try:
            with open(config_file, 'r', encoding=cls.JSON_ENCODING) as f:
                return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load user config: {e}")
            return {}
    
    @classmethod
    def save_user_config(cls, config: Dict[str, Any], config_file: Optional[Path] = None):
        """
        Speichert Benutzer-Konfiguration
        
        Args:
            config: Configuration dictionary
            config_file: Pfad zur Konfigurationsdatei
        """
        
        if config_file is None:
            config_file = cls.PROJECT_ROOT / "user_config.json"
        
        try:
            with open(config_file, 'w', encoding=cls.JSON_ENCODING) as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save user config: {e}")
    
    @classmethod
    def setup_logging(cls, log_level: str = None) -> None:
        """
        Konfiguriert Logging für das Projekt
        
        Args:
            log_level: Log level override
        """
        
        import logging
        from logging.handlers import RotatingFileHandler
        
        level = log_level or cls.LOG_LEVEL
        
        # Ensure logs directory
        cls.ensure_directories()
        
        # Configure root logger
        logger = logging.getLogger()
        logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # File handler
        log_file = cls.get_log_path()
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=cls.LOG_MAX_SIZE_MB * 1024 * 1024,
            backupCount=cls.LOG_MAX_FILES,
            encoding=cls.LOG_ENCODING
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_formatter = logging.Formatter(cls.LOG_FORMAT)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        print(f"✅ Logging configured: {level} level, output to {log_file}")

# Convenience functions für GUI und CLI

def get_quick_fill_options() -> Dict[str, List[str]]:
    """Gibt vorkonfigurierte Mine-Listen für Quick Fill zurück"""
    
    return {
        'quebec_top_10': [
            "Éléonore",
            "Canadian Malartic", 
            "Raglan",
            "Casa Berardi",
            "LaRonde",
            "Mont Wright",
            "Lac Tio",
            "Troilus",
            "Niobec",
            "Beaufor"
        ],
        
        'gold_mines': [
            "Éléonore",
            "Canadian Malartic",
            "Casa Berardi", 
            "LaRonde",
            "Goldex",
            "Beaufor",
            "Sigma",
            "Kiena",
            "Lamaque"
        ],
        
        'test_mines': [
            "Éléonore",
            "Canadian Malartic",
            "Raglan"
        ],
        
        'copper_mines': [
            "Raglan",
            "Horne",
            "Opemiska",
            "Copper Rand"
        ],
        
        'iron_mines': [
            "Mont Wright",
            "Lac Bloom",
            "Fire Lake",
            "Bloom Lake"
        ]
    }

def get_mining_statistics_quebec() -> Dict[str, Any]:
    """Gibt Quebec Mining-Statistiken zurück"""
    
    return {
        'total_active_mines': 150,
        'major_commodities': ['Gold', 'Iron Ore', 'Copper', 'Zinc', 'Nickel'],
        'top_regions': ['Abitibi', 'Nord-du-Québec', 'Côte-Nord'],
        'estimated_total_value': '$12.8 billion CAD',
        'average_restoration_cost': '$25 million CAD',
        'data_source': 'MERN Quebec, 2024'
    }

if __name__ == "__main__":
    # Test project settings
    print("🔧 Testing Project Settings...")
    
    # Test directory creation
    ProjectSettings.ensure_directories()
    
    # Test path generation
    output_path = ProjectSettings.get_default_output_path("test")
    print(f"✅ Default output path: {output_path}")
    
    # Test mine name normalization
    test_names = ["The Éléonore Mine", "Canadian Malartic Mining Project", "Raglan"]
    for name in test_names:
        normalized = ProjectSettings.normalize_mine_name(name)
        print(f"✅ Normalized '{name}' → '{normalized}'")
    
    # Test coordinate validation
    test_coords = [(52.7, -76.1), (40.0, -80.0), (60.0, -70.0)]
    for lat, lon in test_coords:
        is_quebec = ProjectSettings.validate_coordinates(lat, lon)
        status = "✅" if is_quebec else "❌"
        print(f"{status} Coordinates ({lat}, {lon}) in Quebec: {is_quebec}")
    
    # Test project info
    info = ProjectSettings.get_project_info()
    print(f"✅ Project: {info['name']} v{info['version']}")
    
    print("\n✅ Project settings test completed!")
