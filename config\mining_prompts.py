"""
Mining Prompts für MineExtractorWeb v1.0
Spezialisierte KI-Prompts für verschiedene APIs und Research-Strategien
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import re

class PromptBuilder:
    """
    Builder-Klasse für intelligente, API-spezifische Mining Research Prompts
    Optimiert für verschiedene Research-Strategien und Datentypen
    """
    
    def __init__(self):
        """Initialisiert PromptBuilder mit vorkonfigurierten Templates"""
        
        # Setup base templates
        self._setup_base_templates()
        
        # Setup API-specific configurations
        self._setup_api_configurations()
        
        # Setup field-specific prompts
        self._setup_field_prompts()
        
        # Setup context enhancers
        self._setup_context_enhancers()
    
    def _setup_base_templates(self):
        """Konfiguriert Basis-Prompt-Templates"""
        
        self.base_templates = {
            'comprehensive': """Research comprehensive information about {mine_name} mine in Quebec, Canada.

CRITICAL REQUIREMENTS:
1. Find the exact restoration/closure/environmental costs in Canadian dollars (CAD)
2. Identify the current operator/owner company
3. Determine current operational status (active, closed, suspended, planned)
4. Identify primary commodity/mineral being mined
5. Find GPS coordinates or location details
6. Determine mine type (open-pit, underground, surface)

SEARCH STRATEGY:
- Prioritize official government sources (mrnf.gouv.qc.ca, sedarplus.ca, nrcan.gc.ca)
- Include company financial reports and regulatory filings
- Search recent news and industry publications
- Look for environmental assessments and closure plans

FORMAT YOUR RESPONSE:
For each piece of information found, provide:
- The specific data point
- The source URL
- The reliability/date of the information

Focus especially on finding restoration costs as this is the most critical data field.""",
            
            'financial_focus': """Find detailed financial information for {mine_name} mine in Quebec, Canada, with primary focus on restoration and closure costs.

PRIORITY DATA NEEDED:
1. RESTORATION COSTS (highest priority)
   - Environmental restoration costs
   - Mine closure costs
   - Reclamation expenses
   - Decommissioning costs
   - All in Canadian dollars (CAD)

2. FINANCIAL CONTEXT
   - Year of cost assessment
   - Who estimated the costs (company, government, consultant)
   - Current vs historical cost estimates
   - Any cost escalations or updates

3. RELATED FINANCIAL DATA
   - Total project investment
   - Annual production value
   - Operating costs
   - Capital expenditure

SEARCH SOURCES:
- Government regulatory databases (SEDAR+, MERN Quebec)
- Company annual reports and 10-K filings
- Environmental impact assessments
- Mining closure plans
- Financial analyst reports

Please provide exact dollar amounts with sources and assessment dates.""",
            
            'technical_focus': """Research technical and operational details for {mine_name} mine in Quebec, Canada.

TECHNICAL DATA REQUIRED:
1. MINE SPECIFICATIONS
   - Mine type (open-pit, underground, surface)
   - Mining method and equipment
   - Ore processing methods
   - Mine life and reserves

2. LOCATION DATA
   - Exact GPS coordinates
   - Geographic region in Quebec
   - Nearest towns/cities
   - Mine area/footprint

3. PRODUCTION DATA
   - Primary commodity/mineral
   - Secondary minerals
   - Annual production capacity
   - Production start/end dates
   - Peak production periods

4. OPERATIONAL STATUS
   - Current operational status
   - Recent operational changes
   - Future plans (expansion, closure)

Focus on finding precise, technical data from engineering reports, mine plans, and geological surveys.""",
            
            'regulatory_focus': """Find regulatory and compliance information for {mine_name} mine in Quebec, Canada.

REGULATORY DATA NEEDED:
1. GOVERNMENT OVERSIGHT
   - Operating permits and licenses
   - Environmental approvals
   - Regulatory compliance status
   - Government inspections

2. CLOSURE PLANNING
   - Mine closure plan status
   - Environmental bond requirements
   - Restoration timelines
   - Post-closure monitoring

3. LEGAL CONTEXT
   - Current operator/owner
   - Previous ownership changes
   - Legal disputes or issues
   - Indigenous consultation status

Search official government databases and regulatory filings for authoritative information."""
        }
    
    def _setup_api_configurations(self):
        """Konfiguriert API-spezifische Einstellungen"""
        
        self.api_configs = {
            'perplexity': {
                'model': 'llama-3.1-sonar-large-128k-online',
                'max_tokens': 2000,
                'temperature': 0.1,
                'search_focus': 'comprehensive',
                'domain_filters': [
                    'gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca',
                    'gestim.mines.gouv.qc.ca', 'nrcan.gc.ca'
                ],
                'recency_filter': 'month',
                'prompt_prefix': "Using current web search capabilities, ",
                'source_requirement': "Always include source URLs for each claim."
            },
            
            'tavily': {
                'search_depth': 'advanced',
                'max_results': 10,
                'search_focus': 'government_data',
                'include_domains': [
                    'gov.ca', 'gc.ca', 'sec.gov', 'tsx.com',
                    'sedarplus.ca', 'mrnf.gouv.qc.ca'
                ],
                'exclude_domains': ['reddit.com', 'facebook.com', 'twitter.com'],
                'prompt_prefix': "Search government and regulatory sources for ",
                'source_requirement': "Prioritize official government sources."
            },
            
            'exa': {
                'type': 'neural',
                'use_autoprompt': True,
                'num_results': 8,
                'search_focus': 'semantic_similarity',
                'include_domains': [
                    'mining.com', 'northernminer.com', 'newmont.com',
                    'agnicoeagle.com', 'iamgold.com'
                ],
                'prompt_prefix': "Find semantically related content about ",
                'source_requirement': "Include company and industry sources."
            }
        }
    
    def _setup_field_prompts(self):
        """Konfiguriert feldspezifische Prompt-Ergänzungen"""
        
        self.field_prompts = {
            'restaurationskosten_cad': {
                'keywords': [
                    'restoration costs', 'closure costs', 'environmental costs',
                    'reclamation costs', 'decommissioning costs', 'rehabilitation costs',
                    'coûts de restauration', 'coûts de fermeture'
                ],
                'search_terms': [
                    'CAD million', 'Canadian dollars', 'environmental bond',
                    'mine closure plan', 'restoration fund'
                ],
                'context': "Find exact dollar amounts in Canadian currency (CAD). Look for recent cost estimates and include the year of assessment."
            },
            
            'betreiber': {
                'keywords': [
                    'operator', 'owner', 'company', 'operated by', 'owned by',
                    'exploitant', 'propriétaire', 'opérateur'
                ],
                'search_terms': [
                    'mining company', 'corporation', 'Ltd', 'Inc', 'Corp',
                    'resources', 'mining'
                ],
                'context': "Find the current operator/owner company name. Include any recent ownership changes."
            },
            
            'aktivitaetsstatus': {
                'keywords': [
                    'status', 'active', 'operating', 'closed', 'suspended',
                    'operational', 'production', 'statut', 'actif', 'fermé'
                ],
                'search_terms': [
                    'currently operating', 'mine status', 'operational status',
                    'production status', 'suspended operations'
                ],
                'context': "Determine current operational status. Look for recent operational updates."
            },
            
            'rohstoffabbau': {
                'keywords': [
                    'commodity', 'mineral', 'ore', 'gold', 'copper', 'iron',
                    'zinc', 'nickel', 'uranium', 'minerai', 'minéral'
                ],
                'search_terms': [
                    'gold mine', 'copper mine', 'iron ore', 'precious metals',
                    'base metals', 'mineral extraction'
                ],
                'context': "Identify primary and secondary commodities being mined."
            }
        }
    
    def _setup_context_enhancers(self):
        """Konfiguriert Context-Enhancer für bessere Ergebnisse"""
        
        self.context_enhancers = {
            'quebec_context': [
                "This mine is located in Quebec, Canada",
                "Quebec has French and English mining regulations",
                "Quebec mining is regulated by MERN (Ministère de l'Énergie et des Ressources naturelles)",
                "Look for both French and English sources"
            ],
            
            'financial_context': [
                "Canadian mining companies report in CAD",
                "Restoration costs are often in financial statements",
                "SEDAR+ contains Canadian company filings",
                "Environmental bonds are required by Quebec law"
            ],
            
            'reliability_context': [
                "Government sources are most reliable",
                "Company reports may be optimistic",
                "Recent data is more accurate",
                "Cross-reference multiple sources"
            ]
        }
    
    @classmethod
    def build_comprehensive_prompt(cls, mine_name: str, **kwargs) -> str:
        """
        Erstellt umfassenden Research-Prompt
        
        Args:
            mine_name: Name der Mine
            **kwargs: Zusätzliche Parameter
            
        Returns:
            Formatierter Prompt-String
        """
        
        builder = cls()
        
        # Base template
        prompt = builder.base_templates['comprehensive'].format(mine_name=mine_name)
        
        # Add Quebec context
        if kwargs.get('add_quebec_context', True):
            prompt += "\n\nQUEBEC CONTEXT:\n"
            prompt += "\n".join(f"- {ctx}" for ctx in builder.context_enhancers['quebec_context'])
        
        # Add financial context if needed
        if kwargs.get('focus_financial', True):
            prompt += "\n\nFINANCIAL FOCUS:\n"
            prompt += "Restoration costs are the HIGHEST PRIORITY data field. "
            prompt += "Quebec mining companies must provide environmental bonds for closure costs."
        
        return prompt
    
    @classmethod
    def build_targeted_prompt(cls, mine_name: str, data_type: str, api_name: str, **kwargs) -> str:
        """
        Erstellt zielgerichteten Prompt für spezifischen Datentyp
        
        Args:
            mine_name: Name der Mine
            data_type: Type of data ('financial', 'technical', 'regulatory')
            api_name: Target API ('perplexity', 'tavily', 'exa')
            **kwargs: Zusätzliche Parameter
            
        Returns:
            Optimierter Prompt für spezifischen Use Case
        """
        
        builder = cls()
        
        # Select base template
        template_key = f"{data_type}_focus"
        if template_key not in builder.base_templates:
            template_key = 'comprehensive'
        
        prompt = builder.base_templates[template_key].format(mine_name=mine_name)
        
        # Add API-specific prefix
        api_config = builder.api_configs.get(api_name, {})
        if 'prompt_prefix' in api_config:
            prompt = api_config['prompt_prefix'] + prompt
        
        # Add source requirements
        if 'source_requirement' in api_config:
            prompt += f"\n\nSOURCE REQUIREMENTS: {api_config['source_requirement']}"
        
        return prompt
    
    @classmethod
    def build_field_specific_prompt(cls, mine_name: str, field_name: str, **kwargs) -> str:
        """
        Erstellt feldspezifischen Prompt
        
        Args:
            mine_name: Name der Mine
            field_name: Spezifisches Datenfeld
            **kwargs: Zusätzliche Parameter
            
        Returns:
            Feldspezifischer Prompt
        """
        
        builder = cls()
        
        if field_name not in builder.field_prompts:
            # Fallback to comprehensive
            return cls.build_comprehensive_prompt(mine_name, **kwargs)
        
        field_config = builder.field_prompts[field_name]
        
        # Build field-specific prompt
        prompt = f"Find detailed information about {field_name} for {mine_name} mine in Quebec, Canada.\n\n"
        
        # Add field context
        prompt += f"SPECIFIC FOCUS: {field_config['context']}\n\n"
        
        # Add keywords to search for
        prompt += "KEYWORDS TO SEARCH FOR:\n"
        prompt += ", ".join(field_config['keywords'])
        prompt += "\n\n"
        
        # Add search terms
        prompt += "RELEVANT SEARCH TERMS:\n"
        prompt += ", ".join(field_config['search_terms'])
        prompt += "\n\n"
        
        # Add general instructions
        prompt += "Please provide specific, factual information with source URLs."
        
        return prompt
    
    @classmethod
    def build_batch_prompt(cls, mine_names: List[str], focus_area: str = 'comprehensive') -> str:
        """
        Erstellt Batch-Prompt für mehrere Minen
        
        Args:
            mine_names: Liste der Mine-Namen
            focus_area: Fokus-Bereich
            
        Returns:
            Batch-optimierter Prompt
        """
        
        if len(mine_names) == 1:
            return cls.build_comprehensive_prompt(mine_names[0])
        
        prompt = f"Research information for the following {len(mine_names)} mines in Quebec, Canada:\n\n"
        
        for i, mine_name in enumerate(mine_names, 1):
            prompt += f"{i}. {mine_name}\n"
        
        prompt += f"\n\nFor each mine, find:\n"
        prompt += "- Current operator/owner\n"
        prompt += "- Operational status\n"
        prompt += "- Primary commodity\n"
        prompt += "- Restoration/closure costs (CAD)\n"
        prompt += "- Location/coordinates\n\n"
        
        prompt += "Format the response clearly for each mine with source URLs."
        
        return prompt
    
    @classmethod
    def enhance_prompt_with_context(cls, base_prompt: str, context_type: str, **kwargs) -> str:
        """
        Verbessert Prompt mit zusätzlichem Kontext
        
        Args:
            base_prompt: Basis-Prompt
            context_type: Type des Kontexts
            **kwargs: Zusätzliche Parameter
            
        Returns:
            Enhanced prompt
        """
        
        builder = cls()
        
        if context_type in builder.context_enhancers:
            context_lines = builder.context_enhancers[context_type]
            
            enhanced_prompt = base_prompt
            enhanced_prompt += f"\n\n{context_type.upper().replace('_', ' ')}:\n"
            enhanced_prompt += "\n".join(f"- {line}" for line in context_lines)
            
            return enhanced_prompt
        
        return base_prompt
    
    @classmethod
    def create_validation_prompt(cls, mine_name: str, extracted_data: Dict[str, Any]) -> str:
        """
        Erstellt Validierungs-Prompt für extrahierte Daten
        
        Args:
            mine_name: Name der Mine
            extracted_data: Bereits extrahierte Daten
            
        Returns:
            Validierungs-Prompt
        """
        
        prompt = f"Validate and verify the following information about {mine_name} mine in Quebec:\n\n"
        
        for field, value in extracted_data.items():
            if value:
                prompt += f"- {field}: {value}\n"
        
        prompt += "\n\nPlease:\n"
        prompt += "1. Verify each data point against reliable sources\n"
        prompt += "2. Flag any inconsistencies or errors\n"
        prompt += "3. Provide alternative values if found\n"
        prompt += "4. Include source URLs for verification\n\n"
        prompt += "Focus on accuracy and provide confidence levels for each data point."
        
        return prompt

class MiningPrompts:
    """Convenience class für häufig verwendete Prompts"""
    
    # Quick access to common prompts
    COMPREHENSIVE = PromptBuilder.build_comprehensive_prompt
    FINANCIAL = lambda mine_name: PromptBuilder.build_targeted_prompt(mine_name, 'financial', 'perplexity')
    TECHNICAL = lambda mine_name: PromptBuilder.build_targeted_prompt(mine_name, 'technical', 'perplexity')
    REGULATORY = lambda mine_name: PromptBuilder.build_targeted_prompt(mine_name, 'regulatory', 'perplexity')
    
    # Field-specific shortcuts
    RESTORATION_COSTS = lambda mine_name: PromptBuilder.build_field_specific_prompt(mine_name, 'restaurationskosten_cad')
    OPERATOR = lambda mine_name: PromptBuilder.build_field_specific_prompt(mine_name, 'betreiber')
    STATUS = lambda mine_name: PromptBuilder.build_field_specific_prompt(mine_name, 'aktivitaetsstatus')
    COMMODITY = lambda mine_name: PromptBuilder.build_field_specific_prompt(mine_name, 'rohstoffabbau')
    
    @classmethod
    def get_prompt_for_api(cls, mine_name: str, api_name: str, focus: str = 'comprehensive') -> str:
        """
        Gibt optimierten Prompt für spezifische API zurück
        
        Args:
            mine_name: Name der Mine
            api_name: Target API
            focus: Focus area
            
        Returns:
            API-optimierter Prompt
        """
        
        if focus == 'financial':
            return PromptBuilder.build_targeted_prompt(mine_name, 'financial', api_name, focus_financial=True)
        elif focus == 'technical':
            return PromptBuilder.build_targeted_prompt(mine_name, 'technical', api_name)
        elif focus == 'regulatory':
            return PromptBuilder.build_targeted_prompt(mine_name, 'regulatory', api_name)
        else:
            return PromptBuilder.build_comprehensive_prompt(mine_name, focus_financial=True)

# Prompt Templates für verschiedene Szenarien

QUEBEC_MINE_TEMPLATES = {
    'gold_mine_prompt': """Research {mine_name} gold mine in Quebec, Canada. Focus on:
- Current gold production rates and reserves
- Operating company and ownership structure  
- Environmental restoration costs in CAD
- Mine life and closure timeline
- Processing methods and recovery rates""",
    
    'copper_mine_prompt': """Research {mine_name} copper mine in Quebec, Canada. Focus on:
- Copper production capacity and grades
- Associated metals (zinc, gold, silver)
- Operating costs and economics
- Environmental impact and closure costs
- Transportation and infrastructure""",
    
    'iron_ore_prompt': """Research {mine_name} iron ore mine in Quebec, Canada. Focus on:
- Iron ore grade and pellet production
- Shipping and logistics to markets
- Operating company and joint ventures
- Environmental bonds and closure costs
- Mine life and expansion plans"""
}

# Test functions

def test_prompt_generation():
    """Testet Prompt-Generierung"""
    
    print("🧪 Testing prompt generation...")
    
    test_mine = "Éléonore"
    
    # Test comprehensive prompt
    comp_prompt = PromptBuilder.build_comprehensive_prompt(test_mine)
    print(f"✅ Comprehensive prompt: {len(comp_prompt)} characters")
    
    # Test targeted prompts
    financial_prompt = PromptBuilder.build_targeted_prompt(test_mine, 'financial', 'perplexity')
    print(f"✅ Financial prompt: {len(financial_prompt)} characters")
    
    # Test field-specific prompts
    costs_prompt = PromptBuilder.build_field_specific_prompt(test_mine, 'restaurationskosten_cad')
    print(f"✅ Costs prompt: {len(costs_prompt)} characters")
    
    # Test API-specific prompts
    perplexity_prompt = MiningPrompts.get_prompt_for_api(test_mine, 'perplexity', 'financial')
    print(f"✅ Perplexity prompt: {len(perplexity_prompt)} characters")
    
    return True

if __name__ == "__main__":
    # Test prompt system
    test_prompt_generation()
    
    # Show example prompt
    print("\n📝 Example comprehensive prompt:")
    print("-" * 60)
    example = PromptBuilder.build_comprehensive_prompt("Éléonore")
    print(example[:500] + "..." if len(example) > 500 else example)
    
    print("\n✅ Mining prompts test completed!")
