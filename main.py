#!/usr/bin/env python3
"""
MineExtractorWeb v2.0 - Enhanced Multi-Phase Research Engine
Umfassende Web-Research-Platform für Quebec Mining Data mit mehrstufiger Analyse

Features:
    ✅ 5-Phase Deep Research Process
    ✅ Comprehensive Source Documentation  
    ✅ Cross-Validation and Confidence Scoring
    ✅ Quebec Mining Database Integration
    ✅ Enhanced Data Extraction
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# Version info
__version__ = "2.0.0"
__author__ = "Claude Sonnet 4"

def setup_logging():
    """Konfiguriert Enhanced Logging für die Anwendung"""
    import logging
    
    # Enhanced logging setup
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("logs/mineextractor_web.log", encoding='utf-8')
        ]
    )
    
    # Ensure logs directory exists
    os.makedirs("logs", exist_ok=True)

def print_banner():
    """Zeigt Enhanced Anwendungs-Banner"""
    print("=" * 70)
    print(f"🌐 MineExtractorWeb v{__version__} - Enhanced Research Engine")
    print("Multi-Phase Deep Mining Data Research System")
    print("🔍 5-Phase Research • 📚 Source Documentation • 🎯 Confidence Scoring")
    print("=" * 70)

def check_dependencies():
    """Prüft kritische Dependencies"""
    missing_deps = []
    
    try:
        import aiohttp
    except ImportError:
        missing_deps.append("aiohttp")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    if missing_deps:
        print("❌ Missing critical dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Install dependencies with: pip install -r requirements.txt")
        return False
    
    return True

def show_configuration_status():
    """Zeigt erweiterten Konfigurationsstatus"""
    print("\n📋 Enhanced Configuration Status:")
    print("-" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        # Show API status
        print(APIConfig.get_api_status_summary())
        
        # Show active APIs
        active_apis = APIConfig.get_active_apis()
        if active_apis:
            print(f"\n✅ Active APIs: {', '.join(active_apis)}")
            print("🔍 Enhanced multi-phase research available!")
        else:
            print("\n❌ No APIs configured - limited functionality")
        
        # Show project info
        try:
            from config.settings import ProjectSettings
            info = ProjectSettings.get_project_info()
            print(f"\n📁 Project Root: {info['root']}")
            print(f"📊 Output Directory: {info['output_dir']}")
            print(f"📝 Log Directory: {info['log_dir']}")
        except ImportError:
            print("\n⚠️  Project settings not available")
            
    except ImportError:
        print("❌ Configuration modules not available")
        print("   Ensure project is properly set up")

async def test_enhanced_research_engine():
    """Testet die Enhanced Research Engine"""
    print("\n🧪 Testing Enhanced Research Engine:")
    print("-" * 40)
    
    try:
        from src.enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        if not APIConfig.PERPLEXITY_API_KEY:
            print("❌ Perplexity API Key required for enhanced research")
            return False
        
        config = {
            'perplexity_key': APIConfig.PERPLEXITY_API_KEY
        }
        
        print("🔍 Testing with sample mine: 'Éléonore'...")
        
        async with EnhancedMiningResearcher(config) as researcher:
            
            def progress_callback(progress, status):
                print(f"   Progress: {progress:.0f}% - {status}")
            
            result = await researcher.research_mine_comprehensive(
                "Éléonore", 
                progress_callback=progress_callback
            )
            
            print("\n📊 Enhanced Research Results:")
            print(f"   Data Points Found: {len(result.data_points)}")
            print(f"   Sources Used: {len(result.all_sources)}")
            print(f"   Completeness: {result.data_completeness_score:.1%}")
            print(f"   Research Time: {result.total_research_time:.1f}s")
            print(f"   Phases Completed: {len(result.research_phases_completed)}")
            
            # Show sample data
            if result.data_points:
                print("\n📋 Sample Data Found:")
                for i, (field, data_point) in enumerate(list(result.data_points.items())[:5]):
                    print(f"   • {field}: {data_point.value} (confidence: {data_point.confidence:.1%})")
            
            # Show sources by type
            source_types = {}
            for source in result.all_sources:
                source_types[source.content_type] = source_types.get(source.content_type, 0) + 1
            
            if source_types:
                print("\n📚 Sources by Type:")
                for source_type, count in source_types.items():
                    print(f"   • {source_type.replace('_', ' ').title()}: {count}")
            
            print("\n✅ Enhanced Research Engine test successful!")
            return True
        
    except Exception as e:
        print(f"❌ Enhanced research test failed: {e}")
        return False

async def test_api_connections():
    """Testet API-Verbindungen (Compatibility Function)"""
    print("\n🧪 Testing API Connections:")
    print("-" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        # Test Perplexity
        if APIConfig.PERPLEXITY_API_KEY:
            from src.perplexity_client import test_api_key
            perplexity_success = await test_api_key(APIConfig.PERPLEXITY_API_KEY)
            status = "✅ PASS" if perplexity_success else "❌ FAIL"
            print(f"{status} Perplexity API")
        else:
            print("❌ FAIL Perplexity API (not configured)")
            perplexity_success = False
        
        # Test Tavily
        if APIConfig.TAVILY_API_KEY:
            from src.tavily_client import test_api_key
            tavily_success = await test_api_key(APIConfig.TAVILY_API_KEY)
            status = "✅ PASS" if tavily_success else "❌ FAIL"
            print(f"{status} Tavily API")
        else:
            print("⚠️  SKIP Tavily API (not configured)")
            tavily_success = False
        
        # Summary
        successful_apis = sum([perplexity_success, tavily_success])
        total_apis = 2
        
        print(f"\n📊 API Test Summary: {successful_apis}/{total_apis} APIs working")
        
        if successful_apis == 0:
            print("\n⚠️  No APIs are working. Please check your configuration.")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API testing failed: {e}")
        print("\n💡 Ensure API keys are configured in .env file")
        return False

async def quick_enhanced_research(mine_name: str):
    """Führt Enhanced Multi-Phase Research durch"""
    print(f"\n🔍 Enhanced Research: {mine_name}")
    print("-" * 40)
    
    try:
        from src.enhanced_web_researcher import EnhancedMiningResearcher
        from config.api_keys import APIConfig
        
        config = {
            'perplexity_key': APIConfig.PERPLEXITY_API_KEY
        }
        
        async with EnhancedMiningResearcher(config) as researcher:
            
            def progress_callback(progress, status):
                print(f"🔄 {progress:.0f}% - {status}")
            
            print(f"🚀 Starting enhanced multi-phase research for {mine_name}...")
            result = await researcher.research_mine_comprehensive(
                mine_name, 
                progress_callback=progress_callback
            )
            
            # Display comprehensive results
            print("\n" + researcher.get_research_summary(result))
            print("\n" + result.get_sources_summary())
            
            # Save enhanced results
            enhanced_output = f"enhanced_research_{mine_name.replace(' ', '_')}.json"
            
            import json
            
            # Create detailed JSON output
            output_data = {
                'mine_name': result.mine_name,
                'research_timestamp': result.all_sources[0].date_accessed if result.all_sources else '',
                'data_completeness_score': result.data_completeness_score,
                'total_research_time': result.total_research_time,
                'research_phases_completed': result.research_phases_completed,
                'data_points': {},
                'sources': []
            }
            
            # Add data points with full metadata
            for field_name, data_point in result.data_points.items():
                output_data['data_points'][field_name] = {
                    'value': data_point.value,
                    'confidence': data_point.confidence,
                    'extraction_method': data_point.extraction_method,
                    'cross_validated': data_point.cross_validated,
                    'source_count': len(data_point.sources)
                }
            
            # Add sources with full metadata
            for source in result.all_sources:
                output_data['sources'].append({
                    'url': source.url,
                    'title': source.title,
                    'content_type': source.content_type,
                    'reliability_score': source.reliability_score,
                    'date_accessed': source.date_accessed
                })
            
            with open(enhanced_output, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Enhanced results saved to: {enhanced_output}")
            
            # Also save simple CSV for compatibility
            simple_csv = f"enhanced_research_{mine_name.replace(' ', '_')}.csv"
            
            import pandas as pd
            
            # Create simple CSV with main data
            csv_data = {
                'Mine Name': [mine_name],
                'Data Completeness': [f"{result.data_completeness_score:.1%}"],
                'Sources Found': [len(result.all_sources)],
                'Research Time (s)': [f"{result.total_research_time:.1f}"]
            }
            
            # Add main data fields
            main_fields = ['mine_operator', 'production_start_year', 'annual_production_tonnes', 
                          'mine_area_km2', 'restoration_costs_cad', 'commodity_type']
            
            for field in main_fields:
                if field in result.data_points:
                    csv_data[field.replace('_', ' ').title()] = [result.data_points[field].value]
                else:
                    csv_data[field.replace('_', ' ').title()] = ['']
            
            df = pd.DataFrame(csv_data)
            df.to_csv(simple_csv, index=False, encoding='utf-8-sig')
            
            print(f"💾 CSV summary saved to: {simple_csv}")
            
    except Exception as e:
        print(f"❌ Enhanced research failed: {e}")
        import traceback
        traceback.print_exc()

def start_enhanced_gui():
    """Startet die Enhanced GUI (oder Fallback zur Basic GUI) mit robuster Fehlerbehandlung und klarem Logging."""
    print("\n🖥️ Starte die Enhanced GUI...")
    
    # Versuch, die Enhanced GUI zu importieren und zu instanziieren
    current_gui_instance = None
    try:
        from src.enhanced_gui_integration import EnhancedWebResearchGUI
        current_gui_instance = EnhancedWebResearchGUI()
        print("✅ EnhancedWebResearchGUI erfolgreich instanziiert.")

        # Prüfen, ob die Enhanced GUI die erwartete Methode hat
        if hasattr(current_gui_instance, 'run_standalone'):
            print("✅ Enhanced GUI wurde erfolgreich erstellt und gestartet!")
            print("💡 Funktionen der Enhanced GUI:")
            print("   • Automatische API-Key-Ladung aus .env")
            print("   • Mehrphasen-Recherche (5 Phasen)")
            print("   • Detaillierte Quelldokumentation")
            print("   • Verbesserte Datenextraktion")
            print("   • Cross-Validation und Confidence Scoring")
            print("\n💡 Schließe das GUI-Fenster, um die Anwendung zu beenden.")
            current_gui_instance.run_standalone()
            return # Erfolgreicher Start, Funktion beenden
        else:
            print("❌ Enhanced GUI-Instanz fehlt 'run_standalone' Methode. Führe Fallback aus...")
            # Fällt in den folgenden Fallback-Block

    except ImportError as e:
        print(f"❌ Fehler beim Import von EnhancedWebResearchGUI: {e}")
        print("   Stellen Sie sicher, dass 'src/enhanced_gui_integration.py' existiert und korrekt ist.")
    except Exception as e:
        print(f"❌ Fehler bei der Instanziierung oder dem Start der Enhanced GUI: {e}")

    # Fallback zur Basic GUI, wenn Enhanced GUI nicht gestartet werden konnte
    print("🔄 Enhanced GUI konnte nicht gestartet werden. Fallback zur Basic GUI...")
    try:
        from src.gui_integration import create_standalone_gui
        current_gui_instance = create_standalone_gui()
        print("✅ Basic GUI als Fallback erstellt.")
        print("\n💡 Schließe das GUI-Fenster, um die Anwendung zu beenden.")
        print("🌐 Starte MineExtractorWeb GUI (Basic)...")
        # Hier wird KEIN run_standalone() aufgerufen, sondern direkt mainloop()
        current_gui_instance.root.mainloop()
    except Exception as e_fallback:
        print(f"❌ Basic GUI Fallback ebenfalls fehlgeschlagen: {e_fallback}")
        print("   Fataler Fehler: Keine GUI konnte gestartet werden. Bitte überprüfen Sie Ihre Python-Umgebung und Abhängigkeiten.")

def show_enhanced_help():
    """Zeigt erweiterte Hilfe"""
    print(f"""
🌐 MineExtractorWeb v{__version__} - Enhanced Research Guide
{'=' * 70}

🆕 NEW: Enhanced Multi-Phase Research Engine
✅ 5-Phase Deep Research Process
✅ Comprehensive Source Documentation  
✅ Cross-Validation and Confidence Scoring
✅ Quebec Mining Database Integration

BASIC USAGE:
  python main.py                    Start enhanced GUI
  python main.py --gui              Start enhanced GUI (same as above)

ENHANCED RESEARCH:
  python main.py --research <mine>  Enhanced multi-phase research
  python main.py --test-enhanced    Test enhanced research engine
  
  Examples:
    python main.py --research "Éléonore"
    python main.py --research "Canadian Malartic"

CONFIGURATION:
  python main.py --config           Show enhanced configuration status
  python main.py --test-apis        Test all API connections
  python main.py --setup            Interactive setup wizard

🔍 ENHANCED RESEARCH PHASES:
  Phase 1: Basic Mine Information
  Phase 2: Production & Operational Data  
  Phase 3: Technical Specifications
  Phase 4: Financial & Closure Costs
  Phase 5: Government & Regulatory Data

📊 IMPROVED DATA EXTRACTION:
  • Production start/end dates
  • Annual production volumes
  • Mine area in km²
  • Restoration costs (CAD)
  • Detailed source attribution
  • Confidence scoring (0-100%)

📚 SOURCE DOCUMENTATION:
  • Government databases (MERN, GESTIM)
  • Company annual reports
  • Industry publications
  • News archives
  • Full URL and reliability scoring

🎯 CONFIDENCE SCORING:
  • Source reliability (government > industry > news)
  • Cross-validation between sources
  • Data type reliability (numbers > text)
  • Multiple source confirmation

API SETUP:
  Required: PERPLEXITY_API_KEY (for multi-phase research)
  Optional: TAVILY_API_KEY (for government data)
  
  Add to .env file:
     PERPLEXITY_API_KEY=your_key_here
     TAVILY_API_KEY=your_key_here

OUTPUT FORMATS:
  • Enhanced JSON (full metadata and sources)
  • CSV (compatible with existing system)
  • Detailed research reports
  • Source documentation

TROUBLESHOOTING:
  - Enhanced research requires Perplexity API key
  - Check logs/mineextractor_web.log for details
  - Run: python main.py --test-enhanced
  - Ensure internet connection for research

SUPPORT:
  Check Doku/ directory for enhanced documentation.
""")

async def interactive_enhanced_setup():
    """Interaktiver Enhanced Setup-Wizard"""
    print("\n🔧 Enhanced Setup Wizard")
    print("=" * 40)
    
    # Check environment file
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
        create_env = input("Create .env file now? (y/n): ").lower().strip()
        
        if create_env == 'y':
            try:
                from config.api_keys import ENV_TEMPLATE
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(ENV_TEMPLATE)
                print("✅ .env template created")
                print("💡 Please edit .env file and add your API keys")
            except Exception as e:
                print(f"❌ Failed to create .env: {e}")
    
    # Test enhanced research engine
    print("\n🧪 Testing Enhanced Research Engine...")
    engine_test_success = await test_enhanced_research_engine()
    
    if not engine_test_success:
        print("\n⚠️  Enhanced research engine configuration incomplete")
        print("💡 Please configure Perplexity API key in .env file")
        print("🔑 Required for multi-phase research capabilities")
    
    # Test dependencies
    print("\n📦 Checking dependencies...")
    if check_dependencies():
        print("✅ All critical dependencies available")
    else:
        print("❌ Missing dependencies - run: pip install -r requirements.txt")
    
    # Show next steps
    print("\n🎯 Next Steps:")
    if engine_test_success:
        print("  1. ✅ Enhanced research engine ready!")
        print("  2. Run: python main.py --gui (for enhanced GUI)")
        print("  3. Or: python main.py --research \"Mine Name\" (for CLI research)")
    else:
        print("  1. Configure Perplexity API key in .env file")
        print("  2. Test: python main.py --test-enhanced")
        print("  3. Start: python main.py --gui")

def main():
    """Hauptfunktion mit Enhanced Features"""
    
    # Setup
    setup_logging()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="MineExtractorWeb v2.0 - Enhanced Research Engine",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Enhanced GUI
  python main.py --test-enhanced           # Test enhanced research
  python main.py --research "Éléonore"     # Enhanced research
        """
    )
    
    parser.add_argument("--version", action="version", version=f"MineExtractorWeb v{__version__}")
    parser.add_argument("--gui", action="store_true", help="Start enhanced GUI (default)")
    parser.add_argument("--config", action="store_true", help="Show enhanced configuration status")
    parser.add_argument("--test-apis", action="store_true", help="Test API connections")
    parser.add_argument("--test-enhanced", action="store_true", help="Test enhanced research engine")
    parser.add_argument("--research", type=str, help="Enhanced research for specified mine")
    parser.add_argument("--setup", action="store_true", help="Enhanced setup wizard")
    parser.add_argument("--help-extended", action="store_true", help="Show enhanced help")
    
    args = parser.parse_args()
    
    # Show banner
    print_banner()
    
    # Check dependencies first
    if not check_dependencies():
        return 1
    
    # Handle arguments
    try:
        if args.help_extended:
            show_enhanced_help()
            return 0
        
        elif args.config:
            show_configuration_status()
            return 0
        
        elif args.test_enhanced:
            success = asyncio.run(test_enhanced_research_engine())
            return 0 if success else 1
        
        elif args.test_apis:
            success = asyncio.run(test_api_connections())
            return 0 if success else 1
        
        elif args.research:
            asyncio.run(quick_enhanced_research(args.research))
            return 0
        
        elif args.setup:
            asyncio.run(interactive_enhanced_setup())
            return 0
        
        else:
            # Default: Start Enhanced GUI
            start_enhanced_gui()
            return 0
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        return 1
    
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
