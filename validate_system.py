#!/usr/bin/env python3
"""
System Validation & Status Check für MineExtractorWeb v1.0
Überprüft den vollständigen Implementierungsstand und Systemstatus
"""

import os
import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

class SystemValidator:
    """Umfassende System-Validierung"""
    
    def __init__(self):
        self.project_root = project_root
        self.validation_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'components': {},
            'missing_items': [],
            'recommendations': []
        }
    
    def validate_project_structure(self) -> Dict[str, bool]:
        """Validiert Projektstruktur"""
        
        print("🏗️  Validating project structure...")
        
        required_files = {
            # Core source files
            'src/__init__.py': 'Core package initialization',
            'src/mine_data_models.py': 'Data models for mining data',
            'src/perplexity_client.py': 'Perplexity AI client',
            'src/data_parser.py': 'Data extraction and parsing',
            'src/web_researcher.py': 'Main research engine',
            'src/gui_integration.py': 'GUI integration',
            
            # Configuration
            'config/api_keys.py': 'API configuration',
            'config/settings.py': 'Project settings',
            'config/mining_prompts.py': 'Mining-specific prompts',
            
            # Documentation
            'Doku/00_PROJEKT_UEBERSICHT.md': 'Project overview',
            'Doku/01_TECHNISCHE_ARCHITEKTUR.md': 'Technical architecture',
            'Doku/02_API_INTEGRATION_GUIDE.md': 'API integration guide',
            'Doku/03_IMPLEMENTIERUNG_PHASE1.md': 'Phase 1 implementation',
            
            # Project files
            'main.py': 'Main entry point',
            'requirements.txt': 'Python dependencies',
            'README.md': 'Project documentation',
            '.env.template': 'Environment template',
            'setup.py': 'Setup script',
            
            # Tests
            'tests/test_comprehensive.py': 'Test suite'
        }
        
        required_directories = {
            'src/': 'Source code directory',
            'config/': 'Configuration files',
            'Doku/': 'Documentation',
            'tests/': 'Test files',
            'logs/': 'Log files (auto-created)',
            'output/': 'Output files (auto-created)'
        }
        
        results = {}
        
        # Check files
        for file_path, description in required_files.items():
            full_path = self.project_root / file_path
            exists = full_path.exists()
            results[file_path] = exists
            
            status = "✅" if exists else "❌"
            print(f"   {status} {file_path} - {description}")
            
            if not exists:
                self.validation_results['missing_items'].append(f"Missing file: {file_path}")
        
        # Check directories
        for dir_path, description in required_directories.items():
            full_path = self.project_root / dir_path
            exists = full_path.exists()
            results[dir_path] = exists
            
            status = "✅" if exists else "❌"
            print(f"   {status} {dir_path} - {description}")
            
            if not exists:
                self.validation_results['missing_items'].append(f"Missing directory: {dir_path}")
        
        success_rate = sum(results.values()) / len(results)
        self.validation_results['components']['project_structure'] = {
            'status': 'pass' if success_rate >= 0.9 else 'fail',
            'success_rate': success_rate,
            'details': results
        }
        
        print(f"   📊 Project structure: {success_rate:.1%} complete")
        return results
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """Validiert Python Dependencies"""
        
        print("\n📦 Validating dependencies...")
        
        critical_imports = {
            'aiohttp': 'Async HTTP client',
            'pandas': 'Data processing',
            'tkinter': 'GUI framework',
            'asyncio': 'Async programming',
            'json': 'JSON handling',
            'pathlib': 'Path utilities',
            'typing': 'Type hints',
            'datetime': 'Date/time handling'
        }
        
        optional_imports = {
            'tenacity': 'Retry mechanisms',
            'loguru': 'Enhanced logging',
            'pydantic': 'Data validation',
            'requests': 'HTTP fallback'
        }
        
        results = {}
        
        # Test critical imports
        for module, description in critical_imports.items():
            try:
                __import__(module)
                results[module] = True
                print(f"   ✅ {module} - {description}")
            except ImportError:
                results[module] = False
                print(f"   ❌ {module} - {description} (CRITICAL)")
                self.validation_results['missing_items'].append(f"Missing critical dependency: {module}")
        
        # Test optional imports
        for module, description in optional_imports.items():
            try:
                __import__(module)
                results[module] = True
                print(f"   ✅ {module} - {description}")
            except ImportError:
                results[module] = False
                print(f"   ⚠️  {module} - {description} (optional)")
        
        critical_success = all(results[mod] for mod in critical_imports.keys() if mod in results)
        
        self.validation_results['components']['dependencies'] = {
            'status': 'pass' if critical_success else 'fail',
            'critical_available': critical_success,
            'details': results
        }
        
        return results
    
    def validate_source_code(self) -> Dict[str, bool]:
        """Validiert Source Code Imports"""
        
        print("\n🔧 Validating source code...")
        
        source_modules = {
            'mine_data_models': 'Core data models',
            'data_parser': 'Data parsing engine',
            'web_researcher': 'Main research engine'
        }
        
        results = {}
        
        for module, description in source_modules.items():
            try:
                imported_module = __import__(module)
                results[module] = True
                print(f"   ✅ {module} - {description}")
                
                # Test key classes/functions
                if module == 'mine_data_models':
                    assert hasattr(imported_module, 'MineDataFields')
                    assert hasattr(imported_module, 'MineResearchResult')
                    print(f"      ✅ Key classes available")
                
                elif module == 'data_parser':
                    assert hasattr(imported_module, 'MiningDataParser')
                    print(f"      ✅ Parser class available")
                
                elif module == 'web_researcher':
                    assert hasattr(imported_module, 'WebMiningResearcher')
                    print(f"      ✅ Researcher class available")
                
            except (ImportError, AssertionError) as e:
                results[module] = False
                print(f"   ❌ {module} - {description} (ERROR: {e})")
                self.validation_results['missing_items'].append(f"Source code error: {module}")
        
        all_available = all(results.values())
        
        self.validation_results['components']['source_code'] = {
            'status': 'pass' if all_available else 'fail',
            'all_modules_available': all_available,
            'details': results
        }
        
        return results
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validiert Konfiguration"""
        
        print("\n⚙️  Validating configuration...")
        
        results = {
            'env_file_exists': False,
            'api_keys_configured': {},
            'settings_loadable': False,
            'prompts_loadable': False
        }
        
        # Check .env file
        env_file = self.project_root / ".env"
        if env_file.exists():
            results['env_file_exists'] = True
            print("   ✅ .env file exists")
            
            # Check API keys
            try:
                with open(env_file, 'r') as f:
                    env_content = f.read()
                
                api_keys = ['PERPLEXITY_API_KEY', 'TAVILY_API_KEY', 'EXA_API_KEY']
                for key in api_keys:
                    if f"{key}=" in env_content and not f"{key}=your_" in env_content:
                        results['api_keys_configured'][key] = True
                        print(f"   ✅ {key} configured")
                    else:
                        results['api_keys_configured'][key] = False
                        print(f"   ❌ {key} not configured")
                
            except Exception as e:
                print(f"   ❌ Error reading .env file: {e}")
        else:
            print("   ❌ .env file missing")
            self.validation_results['missing_items'].append("Missing .env file")
        
        # Test configuration loading
        try:
            from config.settings import ProjectSettings
            results['settings_loadable'] = True
            print("   ✅ Settings configuration loadable")
        except Exception as e:
            print(f"   ❌ Settings configuration error: {e}")
        
        try:
            from config.mining_prompts import MiningPrompts
            results['prompts_loadable'] = True
            print("   ✅ Mining prompts loadable")
        except Exception as e:
            print(f"   ❌ Mining prompts error: {e}")
        
        config_status = (results['settings_loadable'] and 
                        results['prompts_loadable'] and
                        any(results['api_keys_configured'].values()))
        
        self.validation_results['components']['configuration'] = {
            'status': 'pass' if config_status else 'fail',
            'details': results
        }
        
        return results
    
    async def validate_api_connectivity(self) -> Dict[str, bool]:
        """Testet API-Konnektivität (falls Keys verfügbar)"""
        
        print("\n🌐 Validating API connectivity...")
        
        results = {}
        
        try:
            from config.api_keys import APIConfig
            
            # Check which APIs are configured
            validation = APIConfig.validate_config()
            
            if validation['perplexity']:
                try:
                    from perplexity_client import test_api_key
                    success = await test_api_key(APIConfig.PERPLEXITY_API_KEY)
                    results['perplexity'] = success
                    status = "✅" if success else "❌"
                    print(f"   {status} Perplexity AI connectivity")
                except Exception as e:
                    results['perplexity'] = False
                    print(f"   ❌ Perplexity AI error: {e}")
            else:
                results['perplexity'] = False
                print("   ⏭️  Perplexity AI not configured")
            
            # Future APIs would be tested here
            for api_name in ['tavily', 'exa']:
                if validation.get(api_name, False):
                    results[api_name] = False  # Not implemented yet
                    print(f"   🔄 {api_name.title()} API not implemented yet")
                else:
                    results[api_name] = False
                    print(f"   ⏭️  {api_name.title()} API not configured")
        
        except Exception as e:
            print(f"   ❌ API validation error: {e}")
            results['error'] = str(e)
        
        any_working = any(results.values())
        
        self.validation_results['components']['api_connectivity'] = {
            'status': 'pass' if any_working else 'partial',
            'any_apis_working': any_working,
            'details': results
        }
        
        return results
    
    def validate_gui_components(self) -> Dict[str, bool]:
        """Validiert GUI-Komponenten"""
        
        print("\n🖥️  Validating GUI components...")
        
        results = {}
        
        # Test tkinter availability
        try:
            import tkinter as tk
            from tkinter import ttk
            results['tkinter_available'] = True
            print("   ✅ tkinter available")
        except ImportError:
            results['tkinter_available'] = False
            print("   ❌ tkinter not available")
            self.validation_results['missing_items'].append("tkinter not available")
        
        # Test GUI integration module
        try:
            from gui_integration import WebResearchGUIExtension
            results['gui_module_loadable'] = True
            print("   ✅ GUI integration module loadable")
        except Exception as e:
            results['gui_module_loadable'] = False
            print(f"   ❌ GUI integration error: {e}")
        
        # Test standalone GUI creation
        if results['tkinter_available'] and results['gui_module_loadable']:
            try:
                from gui_integration import create_standalone_gui
                results['standalone_gui_creatable'] = True
                print("   ✅ Standalone GUI creatable")
            except Exception as e:
                results['standalone_gui_creatable'] = False
                print(f"   ❌ Standalone GUI error: {e}")
        else:
            results['standalone_gui_creatable'] = False
        
        gui_functional = all(results.values())
        
        self.validation_results['components']['gui'] = {
            'status': 'pass' if gui_functional else 'fail',
            'fully_functional': gui_functional,
            'details': results
        }
        
        return results
    
    def run_functionality_tests(self) -> Dict[str, bool]:
        """Führt Funktionalitätstests durch"""
        
        print("\n🧪 Running functionality tests...")
        
        results = {}
        
        # Test data models
        try:
            from mine_data_models import MineDataFields, MineResearchResult
            
            # Create test mine data
            mine = MineDataFields(
                name="Test Mine",
                betreiber="Test Company",
                aktivitaetsstatus="aktiv"
            )
            
            # Test basic functionality
            assert mine.name == "Test Mine"
            assert mine.is_valid_mine_data()
            assert mine.get_completion_rate() > 0
            
            # Test CSV conversion
            csv_dict = mine.to_dict()
            assert 'Name' in csv_dict
            assert csv_dict['Name'] == "Test Mine"
            
            results['data_models'] = True
            print("   ✅ Data models functional")
            
        except Exception as e:
            results['data_models'] = False
            print(f"   ❌ Data models error: {e}")
        
        # Test data parser
        try:
            from data_parser import MiningDataParser, quick_parse
            
            parser = MiningDataParser()
            
            # Test parser with sample content
            sample_content = """
            Test Mine is operated by Test Company.
            The mine is currently active.
            Restoration costs are estimated at $10 million CAD.
            """
            
            result = quick_parse(sample_content, "Test Mine")
            assert result.name == "Test Mine"
            
            results['data_parser'] = True
            print("   ✅ Data parser functional")
            
        except Exception as e:
            results['data_parser'] = False
            print(f"   ❌ Data parser error: {e}")
        
        # Test web researcher (without API calls)
        try:
            from web_researcher import WebMiningResearcher
            
            config = {'perplexity_key': 'test-key'}
            researcher = WebMiningResearcher(config)
            
            # Test initialization
            assert researcher.config == config
            assert 'perplexity' in researcher.api_clients
            
            results['web_researcher'] = True
            print("   ✅ Web researcher functional")
            
        except Exception as e:
            results['web_researcher'] = False
            print(f"   ❌ Web researcher error: {e}")
        
        all_functional = all(results.values())
        
        self.validation_results['components']['functionality'] = {
            'status': 'pass' if all_functional else 'fail',
            'all_tests_pass': all_functional,
            'details': results
        }
        
        return results
    
    def generate_recommendations(self) -> List[str]:
        """Generiert Empfehlungen basierend auf Validierungsergebnissen"""
        
        recommendations = []
        
        # Check missing items
        if self.validation_results['missing_items']:
            recommendations.append("🔧 Fix missing components:")
            for item in self.validation_results['missing_items'][:5]:  # Top 5
                recommendations.append(f"   - {item}")
        
        # API configuration
        components = self.validation_results['components']
        if 'configuration' in components:
            config_details = components['configuration']['details']
            if not any(config_details.get('api_keys_configured', {}).values()):
                recommendations.append("🔑 Configure at least one API key (PERPLEXITY_API_KEY recommended)")
        
        # Performance recommendations
        if 'api_connectivity' in components:
            if not components['api_connectivity']['details'].get('perplexity', False):
                recommendations.append("🚀 Set up Perplexity AI for full functionality")
        
        # Setup recommendations
        if not components.get('dependencies', {}).get('critical_available', False):
            recommendations.append("📦 Install missing dependencies: pip install -r requirements.txt")
        
        if not components.get('gui', {}).get('fully_functional', False):
            recommendations.append("🖥️ Install tkinter for GUI functionality")
        
        # Success recommendations
        overall_health = self._calculate_overall_health()
        if overall_health >= 0.8:
            recommendations.append("✅ System is ready for production use!")
            recommendations.append("💡 Run: python main.py --test-apis to verify API connectivity")
        elif overall_health >= 0.6:
            recommendations.append("⚠️  System partially functional - address missing components")
        else:
            recommendations.append("❌ System needs significant work before use")
        
        return recommendations
    
    def _calculate_overall_health(self) -> float:
        """Berechnet Overall-System-Health Score"""
        
        components = self.validation_results['components']
        
        # Weight different components
        weights = {
            'project_structure': 0.2,
            'dependencies': 0.25,
            'source_code': 0.25,
            'configuration': 0.15,
            'api_connectivity': 0.1,
            'gui': 0.05
        }
        
        total_score = 0
        total_weight = 0
        
        for component, weight in weights.items():
            if component in components:
                comp_data = components[component]
                
                # Determine component score
                if comp_data['status'] == 'pass':
                    score = 1.0
                elif comp_data['status'] == 'partial':
                    score = 0.5
                else:
                    score = 0.0
                
                total_score += score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def save_validation_report(self):
        """Speichert Validierungsbericht"""
        
        # Calculate overall health
        health_score = self._calculate_overall_health()
        self.validation_results['overall_health'] = health_score
        
        if health_score >= 0.8:
            self.validation_results['overall_status'] = 'excellent'
        elif health_score >= 0.6:
            self.validation_results['overall_status'] = 'good'
        elif health_score >= 0.4:
            self.validation_results['overall_status'] = 'partial'
        else:
            self.validation_results['overall_status'] = 'poor'
        
        # Add recommendations
        self.validation_results['recommendations'] = self.generate_recommendations()
        
        # Save to file
        report_file = self.project_root / "validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Validation report saved to: {report_file}")
    
    def print_summary(self):
        """Druckt Zusammenfassung"""
        
        health_score = self._calculate_overall_health()
        
        print("\n" + "=" * 70)
        print("📊 SYSTEM VALIDATION SUMMARY")
        print("=" * 70)
        
        # Overall status
        status_icons = {
            'excellent': '🟢',
            'good': '🟡', 
            'partial': '🟠',
            'poor': '🔴'
        }
        
        status = self.validation_results['overall_status']
        icon = status_icons.get(status, '❓')
        
        print(f"\n{icon} Overall System Health: {health_score:.1%} ({status.upper()})")
        
        # Component status
        print(f"\n📋 Component Status:")
        components = self.validation_results['components']
        
        for comp_name, comp_data in components.items():
            status_icon = "✅" if comp_data['status'] == 'pass' else "❌"
            display_name = comp_name.replace('_', ' ').title()
            print(f"   {status_icon} {display_name}")
        
        # Recommendations
        recommendations = self.validation_results['recommendations']
        if recommendations:
            print(f"\n💡 Recommendations:")
            for rec in recommendations:
                print(f"   {rec}")
        
        # Next steps
        print(f"\n🎯 Next Steps:")
        if health_score >= 0.8:
            print("   1. 🚀 System ready! Run: python main.py")
            print("   2. 🔑 Configure API keys if not done")
            print("   3. 🧪 Test with sample mines")
        elif health_score >= 0.6:
            print("   1. 🔧 Address missing components above")
            print("   2. 🔑 Configure API keys")
            print("   3. 🧪 Re-run validation")
        else:
            print("   1. 📦 Install dependencies: pip install -r requirements.txt")
            print("   2. 🔧 Fix critical issues above")
            print("   3. 🔄 Re-run setup: python setup.py")
        
        print("\n" + "=" * 70)

async def main():
    """Hauptfunktion für System-Validierung"""
    
    print("🔍 MineExtractorWeb v1.0 - System Validation")
    print("=" * 60)
    
    validator = SystemValidator()
    
    try:
        # Run all validations
        validator.validate_project_structure()
        validator.validate_dependencies()
        validator.validate_source_code()
        validator.validate_configuration()
        await validator.validate_api_connectivity()
        validator.validate_gui_components()
        validator.run_functionality_tests()
        
        # Generate and save report
        validator.save_validation_report()
        validator.print_summary()
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
