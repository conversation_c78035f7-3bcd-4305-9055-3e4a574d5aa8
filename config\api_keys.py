"""
API Configuration Module für MineExtractorWeb v1.0
Zentrale Verwaltung aller API-Keys und Einstellungen
"""

import os
from dotenv import load_dotenv
from typing import Dict, List

# Load environment variables
load_dotenv()

class APIConfig:
    """Zentrale API-Konfiguration für alle Web Research Services"""
    
    # Perplexity AI Configuration (Primary Research API)
    PERPLEXITY_API_KEY = os.getenv('PERPLEXITY_API_KEY', '')
    PERPLEXITY_BASE_URL = 'https://api.perplexity.ai/chat/completions'
    PERPLEXITY_MODEL = 'llama-3.1-sonar-large-128k-online'
    
    # Tavily AI Configuration (Government & Regulatory Data)
    TAVILY_API_KEY = os.getenv('TAVILY_API_KEY', '')
    TAVILY_BASE_URL = 'https://api.tavily.com/search'
    
    # Exa.ai Configuration (Semantic Search)
    EXA_API_KEY = os.getenv('EXA_API_KEY', '')
    EXA_BASE_URL = 'https://api.exa.ai/search'
    
    # Request Settings
    MAX_TOKENS = 2000
    TEMPERATURE = 0.1
    TIMEOUT_SECONDS = 120
    MAX_RETRIES = 3
    RATE_LIMIT_DELAY = 2  # seconds between requests
    
    # Domain Filters für bessere Ergebnisse (Quebec Mining Focus)
    PRIORITY_DOMAINS = [
        'gov.ca', 'sedarplus.ca', 'mrnf.gouv.qc.ca',
        'tsx.com', 'sec.gov', 'newswire.ca',
        'mining.com', 'northernminer.com',
        'gestim.mines.gouv.qc.ca'
    ]
    
    # Government Sources (Highest Priority)
    GOVERNMENT_DOMAINS = [
        'mrnf.gouv.qc.ca',           # Quebec Ministry of Natural Resources
        'gestim.mines.gouv.qc.ca',   # Quebec Mining Database
        'sedarplus.ca',              # Canadian Securities Database
        'nrcan.gc.ca',               # Natural Resources Canada
        'canada.ca'                  # Government of Canada
    ]
    
    # Industry Sources
    INDUSTRY_DOMAINS = [
        'mining.com',
        'miningweekly.com', 
        'northernminer.com',
        'kitco.com',
        's2analytics.com'
    ]
    
    @classmethod
    def validate_config(cls) -> Dict[str, bool]:
        """
        Validiert alle API-Konfigurationen
        
        Returns:
            Dict mit Validierungsstatus für jede API
        """
        validation_results = {
            'perplexity': bool(cls.PERPLEXITY_API_KEY),
            'tavily': bool(cls.TAVILY_API_KEY),
            'exa': bool(cls.EXA_API_KEY)
        }
        
        return validation_results
    
    @classmethod
    def get_active_apis(cls) -> List[str]:
        """Gibt Liste der konfigurierten APIs zurück"""
        validation = cls.validate_config()
        return [api for api, is_valid in validation.items() if is_valid]
    
    @classmethod
    def is_ready_for_research(cls) -> bool:
        """Prüft ob mindestens eine API konfiguriert ist"""
        return any(cls.validate_config().values())
    
    @classmethod
    def get_api_status_summary(cls) -> str:
        """Gibt Status-Zusammenfassung aller APIs zurück"""
        validation = cls.validate_config()
        active_count = sum(validation.values())
        total_count = len(validation)
        
        status_lines = []
        status_lines.append(f"API Status: {active_count}/{total_count} configured")
        
        for api, is_configured in validation.items():
            status = "✅" if is_configured else "❌"
            status_lines.append(f"  {status} {api.title()}")
        
        return "\n".join(status_lines)

class ResearchConfig:
    """Konfiguration für Research-Strategien und Prompts"""
    
    # Research Focus Areas
    RESEARCH_PRIORITIES = {
        'financial': {
            'weight': 0.9,
            'keywords': ['restoration costs', 'closure costs', 'environmental costs', 'CAD']
        },
        'operational': {
            'weight': 0.8,
            'keywords': ['operator', 'status', 'production', 'mining type']
        },
        'technical': {
            'weight': 0.7,
            'keywords': ['coordinates', 'area', 'commodity', 'volume']
        }
    }
    
    # Quebec-specific search terms
    QUEBEC_TERMS = [
        'Quebec', 'Québec', 'QC', 'Canada',
        'MERN', 'MRNF', 'Hydro-Québec'
    ]
    
    # Mining-specific terms
    MINING_TERMS = [
        'mine', 'mining', 'miner', 'extraction',
        'ore', 'deposit', 'mineral', 'resource'
    ]

# Environment file template for new users
ENV_TEMPLATE = """
# MineExtractorWeb v1.0 API Configuration
# Copy this file to .env and add your actual API keys

# Perplexity AI (Primary Research API) - Required
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Tavily AI (Government Data) - Optional but recommended
TAVILY_API_KEY=your_tavily_api_key_here

# Exa.ai (Semantic Search) - Optional
EXA_API_KEY=your_exa_api_key_here

# Additional Settings
LOG_LEVEL=INFO
DEBUG_MODE=False
"""

def create_env_template():
    """Erstellt .env Template Datei wenn nicht vorhanden"""
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env.template')
    
    if not os.path.exists(env_path):
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(ENV_TEMPLATE)
        
        print(f"📝 Created .env template at: {env_path}")
        print("   Copy to .env and add your API keys")

if __name__ == "__main__":
    # Test configuration
    print("🔧 Testing API Configuration...")
    print(APIConfig.get_api_status_summary())
    
    if not APIConfig.is_ready_for_research():
        print("\n⚠️  No APIs configured - creating template...")
        create_env_template()
    else:
        print(f"\n✅ Ready for research with: {', '.join(APIConfig.get_active_apis())}")
