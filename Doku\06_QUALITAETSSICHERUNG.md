# Qualitätssicherung - MineExtractorWeb v1.0
**Comprehensive Data Quality Framework für Multi-Source Mining Data**

---

## 🎯 **QUALITY FRAMEWORK OVERVIEW**

Das Qualitätssicherungs-Framework stellt sicher, dass Multi-Source-Daten höchste Qualitätsstandards erfüllen durch systematische Validierung, Cross-Validation und kontinuierliche Qualitätsverbesserung.

### **Qualitäts-Dimensionen:**
- **Completeness:** Vollständigkeit der extrahierten Daten
- **Accuracy:** Korrektheit und Plausibilität der Werte
- **Consistency:** Interne Konsistenz zwischen Datenfeldern
- **Freshness:** Aktualität der Datenquellen
- **Reliability:** Vertrauenswürdigkeit der Quellen
- **Traceability:** Nachverfolgbarkeit zu ursprünglichen Quellen

---

## 🔍 **MULTI-LEVEL VALIDATION FRAMEWORK**

### **Level 1: Source-Level Validation**

```python
class SourceLevelValidator:
    """
    Validiert Datenqualität auf Quellen-Ebene
    Bewertet einzelne API/Scraping-Ergebnisse vor Aggregation
    """
    
    def __init__(self):
        # Source reliability scores (learned from historical data)
        self.source_reliability = {
            'government_official': 0.95,
            'company_reports': 0.85,
            'api_research': 0.80,
            'industry_databases': 0.75,
            'news_media': 0.60,
            'web_scraping': 0.70
        }
        
        # Field-specific validation rules
        self.field_validators = {
            'restoration_costs': self._validate_restoration_costs,
            'coordinates': self._validate_coordinates,
            'production_dates': self._validate_production_dates,
            'mine_type': self._validate_mine_type,
            'commodity': self._validate_commodity,
            'operator': self._validate_operator
        }
    
    async def validate_source_data(self, source_result: ResearchResult) -> ValidationReport:
        """
        Validiert Daten aus einzelner Quelle
        """
        
        validation_results = {}
        validation_errors = []
        quality_score = 0.0
        
        # Validate each data field
        for field, value in source_result.data.items():
            if field in self.field_validators:
                try:
                    is_valid, confidence, errors = self.field_validators[field](value)
                    
                    validation_results[field] = {
                        'valid': is_valid,
                        'confidence': confidence,
                        'errors': errors
                    }
                    
                    if is_valid:
                        quality_score += confidence
                    else:
                        validation_errors.extend(errors)
                        
                except Exception as e:
                    validation_errors.append(f"Validation error for {field}: {e}")
        
        # Calculate overall quality score
        if validation_results:
            quality_score = quality_score / len(validation_results)
        
        # Apply source reliability weighting
        source_type = source_result.source_type
        reliability_weight = self.source_reliability.get(source_type, 0.5)
        final_score = quality_score * reliability_weight
        
        return ValidationReport(
            source=source_result.source,
            field_validations=validation_results,
            errors=validation_errors,
            quality_score=final_score,
            recommendation=self._generate_source_recommendation(final_score, validation_errors)
        )
    
    def _validate_restoration_costs(self, cost_value: str) -> Tuple[bool, float, List[str]]:
        """
        Validiert Restaurationskosten auf Plausibilität
        """
        
        errors = []
        
        if not cost_value or not str(cost_value).strip():
            return False, 0.0, ["No restoration cost provided"]
        
        try:
            # Extract numeric value
            numeric_value = re.sub(r'[^\d.]', '', str(cost_value))
            cost_amount = float(numeric_value)
            
            # Plausibility checks
            if cost_amount < 10_000:
                errors.append(f"Restoration cost too low: ${cost_amount:,.0f}")
                return False, 0.2, errors
            
            if cost_amount > 10_000_000_000:  # 10 billion CAD
                errors.append(f"Restoration cost too high: ${cost_amount:,.0f}")
                return False, 0.3, errors
            
            # Confidence based on reasonable ranges
            if 100_000 <= cost_amount <= 1_000_000_000:  # 100K to 1B
                confidence = 0.9
            elif 10_000 <= cost_amount <= 5_000_000_000:  # 10K to 5B
                confidence = 0.7
            else:
                confidence = 0.5
            
            return True, confidence, []
            
        except ValueError:
            errors.append(f"Invalid cost format: {cost_value}")
            return False, 0.0, errors
    
    def _validate_coordinates(self, coordinates: Dict) -> Tuple[bool, float, List[str]]:
        """
        Validiert GPS-Koordinaten für Quebec Mining Region
        """
        
        errors = []
        
        if not coordinates or not isinstance(coordinates, dict):
            return False, 0.0, ["No coordinates provided"]
        
        x_coord = coordinates.get('x', coordinates.get('longitude'))
        y_coord = coordinates.get('y', coordinates.get('latitude'))
        
        if not x_coord or not y_coord:
            return False, 0.0, ["Incomplete coordinates"]
        
        try:
            x, y = float(x_coord), float(y_coord)
            
            # Quebec coordinate ranges (approximate)
            # Latitude: 45°N to 62°N
            # Longitude: 57°W to 85°W (negative values)
            
            if not (45 <= y <= 62):
                errors.append(f"Latitude {y} outside Quebec range (45-62°N)")
            
            if not (-85 <= x <= -57):
                errors.append(f"Longitude {x} outside Quebec range (57-85°W)")
            
            if errors:
                return False, 0.3, errors
            
            # Higher confidence for coordinates in known mining regions
            mining_regions = [
                {'lat_range': (48, 50), 'lon_range': (-79, -75)},  # Abitibi
                {'lat_range': (54, 58), 'lon_range': (-75, -70)},  # Ungava
                {'lat_range': (50, 52), 'lon_range': (-72, -68)}   # Côte-Nord
            ]
            
            confidence = 0.7  # Base confidence
            for region in mining_regions:
                lat_min, lat_max = region['lat_range']
                lon_min, lon_max = region['lon_range']
                
                if lat_min <= y <= lat_max and lon_min <= x <= lon_max:
                    confidence = 0.9  # Higher confidence in known mining region
                    break
            
            return True, confidence, []
            
        except ValueError:
            errors.append(f"Invalid coordinate format: {x_coord}, {y_coord}")
            return False, 0.0, errors
    
    def _validate_production_dates(self, dates: Dict) -> Tuple[bool, float, List[str]]:
        """
        Validiert Produktionsdaten auf zeitliche Konsistenz
        """
        
        errors = []
        
        start_date = dates.get('start')
        end_date = dates.get('end')
        
        if not start_date and not end_date:
            return False, 0.0, ["No production dates provided"]
        
        try:
            current_year = datetime.now().year
            
            # Validate start date
            if start_date:
                start_year = int(start_date)
                if not (1800 <= start_year <= current_year + 5):
                    errors.append(f"Invalid start year: {start_year}")
            
            # Validate end date
            if end_date:
                end_year = int(end_date)
                if not (1800 <= end_year <= current_year + 50):
                    errors.append(f"Invalid end year: {end_year}")
            
            # Check logical consistency
            if start_date and end_date:
                start_year = int(start_date)
                end_year = int(end_date)
                
                if start_year >= end_year:
                    errors.append(f"Start year {start_year} >= end year {end_year}")
                
                # Reasonable mine life (1 to 100 years)
                mine_life = end_year - start_year
                if mine_life > 100:
                    errors.append(f"Unusually long mine life: {mine_life} years")
            
            if errors:
                return False, 0.4, errors
            
            # Confidence based on data completeness
            if start_date and end_date:
                confidence = 0.9
            elif start_date or end_date:
                confidence = 0.7
            else:
                confidence = 0.3
            
            return True, confidence, []
            
        except ValueError:
            errors.append(f"Invalid date format: start={start_date}, end={end_date}")
            return False, 0.0, errors
```

### **Level 2: Cross-Source Validation**

```python
class CrossSourceValidator:
    """
    Validiert Konsistenz zwischen verschiedenen Datenquellen
    Identifiziert Konflikte und bewertet Quellenvertrauenswürdigkeit
    """
    
    def __init__(self):
        # Conflict resolution weights by source type
        self.source_weights = {
            'quebec_mern': 1.0,
            'sedar_plus': 0.9,
            'perplexity': 0.8,
            'tavily': 0.8,
            'company_website': 0.7,
            'industry_portal': 0.6
        }
        
        # Tolerance levels for numeric fields
        self.tolerance_levels = {
            'restoration_costs': 0.15,  # 15% tolerance
            'coordinates': 0.01,        # 0.01 degree tolerance
            'production_volumes': 0.20   # 20% tolerance
        }
    
    async def validate_cross_source_consistency(self, 
                                              multi_source_data: List[SourceData]) -> CrossValidationReport:
        """
        Validiert Konsistenz zwischen mehreren Datenquellen
        """
        
        conflicts = self._identify_conflicts(multi_source_data)
        consistency_score = self._calculate_consistency_score(conflicts, multi_source_data)
        
        # Resolve conflicts using weighted consensus
        resolved_data = self._resolve_conflicts_weighted(conflicts, multi_source_data)
        
        # Generate cross-validation recommendations
        recommendations = self._generate_cross_validation_recommendations(conflicts)
        
        return CrossValidationReport(
            conflicts=conflicts,
            consistency_score=consistency_score,
            resolved_data=resolved_data,
            recommendations=recommendations,
            source_reliability_scores=self._calculate_source_reliability(multi_source_data)
        )
    
    def _identify_conflicts(self, source_data_list: List[SourceData]) -> Dict[str, ConflictInfo]:
        """
        Identifiziert Konflikte zwischen Datenquellen
        """
        
        conflicts = {}
        
        # Group data by field
        field_data = defaultdict(list)
        for source_data in source_data_list:
            for field, value in source_data.data.items():
                if value and str(value).strip():
                    field_data[field].append({
                        'value': value,
                        'source': source_data.source,
                        'confidence': source_data.confidence
                    })
        
        # Check for conflicts in each field
        for field, values in field_data.items():
            if len(values) > 1:
                conflict_info = self._analyze_field_conflict(field, values)
                if conflict_info.has_conflict:
                    conflicts[field] = conflict_info
        
        return conflicts
    
    def _analyze_field_conflict(self, field: str, values: List[Dict]) -> ConflictInfo:
        """
        Analysiert Konflikte für spezifisches Datenfeld
        """
        
        # Different analysis based on field type
        if field in ['restoration_costs', 'production_volumes']:
            return self._analyze_numeric_conflict(field, values)
        elif field in ['coordinates']:
            return self._analyze_coordinate_conflict(field, values)
        elif field in ['operator', 'mine_name']:
            return self._analyze_text_conflict(field, values)
        else:
            return self._analyze_categorical_conflict(field, values)
    
    def _analyze_numeric_conflict(self, field: str, values: List[Dict]) -> ConflictInfo:
        """
        Analysiert numerische Konflikte mit Toleranz-Levels
        """
        
        numeric_values = []
        for item in values:
            try:
                # Extract numeric value
                clean_value = re.sub(r'[^\d.]', '', str(item['value']))
                numeric_value = float(clean_value)
                numeric_values.append({
                    'numeric_value': numeric_value,
                    'original_value': item['value'],
                    'source': item['source'],
                    'confidence': item['confidence']
                })
            except ValueError:
                continue
        
        if len(numeric_values) < 2:
            return ConflictInfo(has_conflict=False)
        
        # Calculate variance
        values_only = [v['numeric_value'] for v in numeric_values]
        mean_value = sum(values_only) / len(values_only)
        max_deviation = max(abs(v - mean_value) for v in values_only)
        
        # Check if deviation exceeds tolerance
        tolerance = self.tolerance_levels.get(field, 0.10)
        relative_deviation = max_deviation / mean_value if mean_value > 0 else 1.0
        
        has_conflict = relative_deviation > tolerance
        
        return ConflictInfo(
            has_conflict=has_conflict,
            field=field,
            conflicting_values=numeric_values,
            conflict_severity='high' if relative_deviation > tolerance * 2 else 'medium',
            resolution_strategy='weighted_average' if not has_conflict else 'source_priority'
        )
    
    def _resolve_conflicts_weighted(self, conflicts: Dict, source_data_list: List[SourceData]) -> Dict:
        """
        Löst Konflikte durch gewichteten Konsens
        """
        
        resolved_data = {}
        
        # Group all data by field
        field_data = defaultdict(list)
        for source_data in source_data_list:
            for field, value in source_data.data.items():
                if value and str(value).strip():
                    source_weight = self.source_weights.get(source_data.source, 0.5)
                    field_data[field].append({
                        'value': value,
                        'source': source_data.source,
                        'weight': source_weight * source_data.confidence
                    })
        
        # Resolve each field
        for field, values in field_data.items():
            if field in conflicts:
                # Use conflict resolution strategy
                conflict_info = conflicts[field]
                resolved_value = self._apply_resolution_strategy(conflict_info, values)
            else:
                # Use highest weighted value
                best_value = max(values, key=lambda x: x['weight'])
                resolved_value = best_value['value']
            
            resolved_data[field] = resolved_value
        
        return resolved_data
```

### **Level 3: Business Logic Validation**

```python
class BusinessLogicValidator:
    """
    Validiert Daten gegen Business Rules und Domain Knowledge
    Stellt sicher, dass Daten mining-spezifische Logik erfüllen
    """
    
    def __init__(self):
        # Known mining companies and their subsidiaries
        self.company_relationships = {
            'Newmont Corporation': ['Newmont', 'Newmont Goldcorp', 'Newmont Corp'],
            'Agnico Eagle Mines Limited': ['Agnico Eagle', 'AEM', 'Agnico'],
            'IAMGOLD Corporation': ['IAMGOLD', 'IAM', 'Iamgold Corp'],
            'Glencore plc': ['Glencore', 'Xstrata']
        }
        
        # Commodity-Operation type compatibility
        self.commodity_operation_rules = {
            'Gold': ['Underground', 'Open-Pit'],
            'Iron Ore': ['Open-Pit'],  # Iron ore is typically open-pit
            'Copper': ['Underground', 'Open-Pit'],
            'Nickel': ['Underground', 'Open-Pit'],
            'Lithium': ['Open-Pit', 'Brine']
        }
        
        # Regional knowledge for Quebec
        self.quebec_mining_regions = {
            'Abitibi': {
                'commodities': ['Gold', 'Copper', 'Zinc'],
                'lat_range': (48, 50),
                'lon_range': (-79, -75)
            },
            'Ungava': {
                'commodities': ['Nickel', 'Iron Ore'],
                'lat_range': (54, 58),
                'lon_range': (-75, -70)
            },
            'Côte-Nord': {
                'commodities': ['Iron Ore', 'Titanium'],
                'lat_range': (50, 52),
                'lon_range': (-72, -68)
            }
        }
    
    async def validate_business_logic(self, mine_data: Dict) -> BusinessValidationReport:
        """
        Validiert Mining-Daten gegen Business Logic Rules
        """
        
        validation_results = {}
        warnings = []
        errors = []
        
        # Rule 1: Commodity-Operation Type Compatibility
        compatibility_result = self._validate_commodity_operation_compatibility(mine_data)
        validation_results['commodity_operation'] = compatibility_result
        if not compatibility_result['valid']:
            warnings.extend(compatibility_result['issues'])
        
        # Rule 2: Regional Commodity Plausibility
        regional_result = self._validate_regional_commodity_plausibility(mine_data)
        validation_results['regional_commodity'] = regional_result
        if not regional_result['valid']:
            warnings.extend(regional_result['issues'])
        
        # Rule 3: Operator-Mine Association
        operator_result = self._validate_operator_mine_association(mine_data)
        validation_results['operator_association'] = operator_result
        if not operator_result['valid']:
            warnings.extend(operator_result['issues'])
        
        # Rule 4: Production Timeline Logic
        timeline_result = self._validate_production_timeline(mine_data)
        validation_results['production_timeline'] = timeline_result
        if not timeline_result['valid']:
            errors.extend(timeline_result['issues'])
        
        # Rule 5: Financial Plausibility
        financial_result = self._validate_financial_plausibility(mine_data)
        validation_results['financial_plausibility'] = financial_result
        if not financial_result['valid']:
            warnings.extend(financial_result['issues'])
        
        # Calculate overall business logic score
        valid_rules = sum(1 for result in validation_results.values() if result['valid'])
        business_logic_score = valid_rules / len(validation_results)
        
        return BusinessValidationReport(
            business_logic_score=business_logic_score,
            validation_results=validation_results,
            warnings=warnings,
            errors=errors,
            recommendations=self._generate_business_recommendations(validation_results)
        )
    
    def _validate_commodity_operation_compatibility(self, mine_data: Dict) -> Dict:
        """
        Validiert Kompatibilität zwischen Commodity und Operation Type
        """
        
        commodity = mine_data.get('commodity', '').strip()
        operation_type = mine_data.get('mine_type', '').strip()
        
        if not commodity or not operation_type:
            return {'valid': True, 'issues': [], 'confidence': 0.5}
        
        compatible_operations = self.commodity_operation_rules.get(commodity, [])
        
        if not compatible_operations:
            return {
                'valid': True, 
                'issues': [f"Unknown commodity: {commodity}"], 
                'confidence': 0.7
            }
        
        # Check compatibility
        is_compatible = any(op.lower() in operation_type.lower() for op in compatible_operations)
        
        if is_compatible:
            return {'valid': True, 'issues': [], 'confidence': 0.9}
        else:
            return {
                'valid': False,
                'issues': [f"{commodity} typically not mined via {operation_type}"],
                'confidence': 0.3
            }
    
    def _validate_regional_commodity_plausibility(self, mine_data: Dict) -> Dict:
        """
        Validiert Plausibilität der Commodity für Quebec Region
        """
        
        commodity = mine_data.get('commodity', '').strip()
        coordinates = mine_data.get('coordinates', {})
        
        if not commodity:
            return {'valid': True, 'issues': [], 'confidence': 0.5}
        
        if not coordinates:
            return {'valid': True, 'issues': ['No coordinates for regional validation'], 'confidence': 0.6}
        
        try:
            lat = float(coordinates.get('latitude', coordinates.get('y', 0)))
            lon = float(coordinates.get('longitude', coordinates.get('x', 0)))
            
            # Check which Quebec mining region the coordinates fall into
            matching_regions = []
            for region_name, region_info in self.quebec_mining_regions.items():
                lat_min, lat_max = region_info['lat_range']
                lon_min, lon_max = region_info['lon_range']
                
                if lat_min <= lat <= lat_max and lon_min <= lon <= lon_max:
                    matching_regions.append(region_name)
                    
                    # Check if commodity is typical for this region
                    if commodity in region_info['commodities']:
                        return {
                            'valid': True,
                            'issues': [],
                            'confidence': 0.9,
                            'region': region_name
                        }
            
            if matching_regions:
                region = matching_regions[0]
                expected_commodities = self.quebec_mining_regions[region]['commodities']
                return {
                    'valid': False,
                    'issues': [f"{commodity} uncommon in {region} region. Expected: {', '.join(expected_commodities)}"],
                    'confidence': 0.4,
                    'region': region
                }
            else:
                return {
                    'valid': True,
                    'issues': ['Coordinates outside known Quebec mining regions'],
                    'confidence': 0.6
                }
                
        except (ValueError, KeyError):
            return {
                'valid': True,
                'issues': ['Invalid coordinates for regional validation'],
                'confidence': 0.5
            }
    
    def _validate_production_timeline(self, mine_data: Dict) -> Dict:
        """
        Validiert logische Konsistenz der Produktions-Timeline
        """
        
        start_year = mine_data.get('production_start')
        end_year = mine_data.get('production_end')
        status = mine_data.get('status', '').lower()
        
        issues = []
        
        try:
            current_year = datetime.now().year
            
            # Convert to integers if possible
            if start_year:
                start_year = int(start_year)
            if end_year:
                end_year = int(end_year)
            
            # Timeline logic checks
            if start_year and end_year:
                if start_year >= end_year:
                    issues.append(f"Start year {start_year} should be before end year {end_year}")
                
                mine_life = end_year - start_year
                if mine_life > 100:
                    issues.append(f"Unusually long mine life: {mine_life} years")
                elif mine_life < 1:
                    issues.append(f"Unusually short mine life: {mine_life} years")
            
            # Status consistency checks
            if 'closed' in status or 'shutdown' in status:
                if not end_year:
                    issues.append("Closed mine should have end year")
                elif end_year > current_year:
                    issues.append(f"Closed mine has future end year: {end_year}")
            
            if 'active' in status or 'operational' in status:
                if end_year and end_year <= current_year:
                    issues.append(f"Active mine should not have past end year: {end_year}")
            
            return {
                'valid': len(issues) == 0,
                'issues': issues,
                'confidence': 0.9 if len(issues) == 0 else 0.3
            }
            
        except (ValueError, TypeError):
            return {
                'valid': False,
                'issues': ['Invalid date format in production timeline'],
                'confidence': 0.1
            }
```

---

## 📊 **AUTOMATED QUALITY SCORING**

### **Comprehensive Quality Score Calculation**

```python
class QualityScoreCalculator:
    """
    Berechnet umfassende Qualitäts-Scores für Mining-Daten
    Kombiniert alle Validation-Levels zu finaler Bewertung
    """
    
    def __init__(self):
        # Weight different quality dimensions
        self.dimension_weights = {
            'completeness': 0.25,
            'accuracy': 0.25,
            'consistency': 0.20,
            'freshness': 0.15,
            'source_reliability': 0.15
        }
        
        # Field importance weights
        self.field_importance = {
            'mine_name': 1.0,
            'operator': 0.9,
            'restoration_costs': 0.95,
            'status': 0.85,
            'commodity': 0.8,
            'mine_type': 0.7,
            'coordinates': 0.6,
            'production_start': 0.75,
            'production_end': 0.7,
            'production_volume': 0.65,
            'mine_area': 0.55
        }
    
    async def calculate_comprehensive_quality_score(self, 
                                                  mine_data: Dict,
                                                  validation_reports: List[ValidationReport]) -> QualityScoreReport:
        """
        Berechnet umfassenden Qualitäts-Score
        """
        
        # Calculate individual dimension scores
        completeness_score = self._calculate_completeness_score(mine_data)
        accuracy_score = self._calculate_accuracy_score(validation_reports)
        consistency_score = self._calculate_consistency_score(validation_reports)
        freshness_score = self._calculate_freshness_score(validation_reports)
        reliability_score = self._calculate_reliability_score(validation_reports)
        
        # Calculate weighted overall score
        dimension_scores = {
            'completeness': completeness_score,
            'accuracy': accuracy_score,
            'consistency': consistency_score,
            'freshness': freshness_score,
            'source_reliability': reliability_score
        }
        
        overall_score = sum(
            score * self.dimension_weights[dimension]
            for dimension, score in dimension_scores.items()
        )
        
        # Generate quality grade
        quality_grade = self._assign_quality_grade(overall_score)
        
        # Generate improvement recommendations
        recommendations = self._generate_improvement_recommendations(dimension_scores, mine_data)
        
        return QualityScoreReport(
            overall_score=overall_score,
            quality_grade=quality_grade,
            dimension_scores=dimension_scores,
            recommendations=recommendations,
            critical_issues=self._identify_critical_issues(validation_reports),
            data_completeness_percentage=completeness_score * 100
        )
    
    def _calculate_completeness_score(self, mine_data: Dict) -> float:
        """
        Berechnet Vollständigkeits-Score basierend auf gefüllten Feldern
        """
        
        total_weighted_importance = sum(self.field_importance.values())
        filled_weighted_importance = 0
        
        for field, importance in self.field_importance.items():
            value = mine_data.get(field, '')
            if value and str(value).strip():
                filled_weighted_importance += importance
        
        return filled_weighted_importance / total_weighted_importance
    
    def _calculate_accuracy_score(self, validation_reports: List[ValidationReport]) -> float:
        """
        Berechnet Genauigkeits-Score basierend auf Validierungsergebnissen
        """
        
        if not validation_reports:
            return 0.5  # Neutral score if no validation data
        
        accuracy_scores = []
        
        for report in validation_reports:
            if hasattr(report, 'field_validations'):
                for field, validation in report.field_validations.items():
                    if validation['valid']:
                        accuracy_scores.append(validation['confidence'])
                    else:
                        accuracy_scores.append(0.0)
        
        return sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0.5
    
    def _assign_quality_grade(self, overall_score: float) -> str:
        """
        Weist Qualitäts-Grade basierend auf Score zu
        """
        
        if overall_score >= 0.90:
            return "A+ (Excellent)"
        elif overall_score >= 0.80:
            return "A (Very Good)"
        elif overall_score >= 0.70:
            return "B (Good)"
        elif overall_score >= 0.60:
            return "C (Acceptable)"
        elif overall_score >= 0.50:
            return "D (Poor)"
        else:
            return "F (Fail)"
    
    def _generate_improvement_recommendations(self, dimension_scores: Dict, mine_data: Dict) -> List[str]:
        """
        Generiert spezifische Verbesserungsempfehlungen
        """
        
        recommendations = []
        
        # Completeness recommendations
        if dimension_scores['completeness'] < 0.7:
            missing_critical_fields = []
            for field, importance in self.field_importance.items():
                if importance > 0.8 and not mine_data.get(field):
                    missing_critical_fields.append(field)
            
            if missing_critical_fields:
                recommendations.append(
                    f"Priority: Search for missing critical data: {', '.join(missing_critical_fields)}"
                )
        
        # Accuracy recommendations
        if dimension_scores['accuracy'] < 0.7:
            recommendations.append(
                "Review data accuracy - consider additional sources for validation"
            )
        
        # Consistency recommendations
        if dimension_scores['consistency'] < 0.7:
            recommendations.append(
                "Check data consistency across sources - resolve conflicting information"
            )
        
        # Freshness recommendations
        if dimension_scores['freshness'] < 0.6:
            recommendations.append(
                "Data appears outdated - search for more recent information"
            )
        
        # Reliability recommendations
        if dimension_scores['source_reliability'] < 0.7:
            recommendations.append(
                "Use more reliable sources - prioritize government and official company data"
            )
        
        return recommendations
```

---

## 🔄 **CONTINUOUS QUALITY IMPROVEMENT**

### **Quality Learning System**

```python
class QualityLearningSystem:
    """
    Lernt aus Qualitätsdaten zur kontinuierlichen Verbesserung
    Passt Validation Rules und Scoring basierend auf Erfahrungen an
    """
    
    def __init__(self):
        self.quality_history = QualityHistoryDatabase()
        self.learning_algorithms = {
            'source_reliability': SourceReliabilityLearner(),
            'field_accuracy': FieldAccuracyLearner(),
            'validation_rules': ValidationRuleLearner()
        }
    
    async def update_quality_models(self, quality_reports: List[QualityScoreReport]):
        """
        Aktualisiert Qualitäts-Modelle basierend auf neuen Daten
        """
        
        logger.info(f"Updating quality models with {len(quality_reports)} reports")
        
        # Store quality data
        for report in quality_reports:
            await self.quality_history.store_quality_report(report)
        
        # Update learning models
        for model_name, learning_algorithm in self.learning_algorithms.items():
            try:
                await learning_algorithm.update_model(quality_reports)
                logger.info(f"Updated {model_name} learning model")
            except Exception as e:
                logger.error(f"Failed to update {model_name} model: {e}")
    
    async def get_adaptive_validation_rules(self, mine_name: str, data_context: Dict) -> Dict:
        """
        Liefert adaptive Validierungsregeln basierend auf gelernten Patterns
        """
        
        # Get learned patterns for similar mines
        similar_patterns = await self.quality_history.get_similar_mine_patterns(mine_name)
        
        # Adapt validation rules based on context
        adaptive_rules = {
            'restoration_cost_range': self._adapt_cost_validation(similar_patterns),
            'coordinate_precision': self._adapt_coordinate_validation(data_context),
            'source_priorities': self._adapt_source_priorities(similar_patterns)
        }
        
        return adaptive_rules
    
    def _adapt_cost_validation(self, similar_patterns: List[Dict]) -> Dict:
        """
        Adaptiert Kostenvalidierung basierend auf ähnlichen Minen
        """
        
        if not similar_patterns:
            return {'min_cost': 10_000, 'max_cost': 10_000_000_000}
        
        # Extract cost ranges from similar mines
        costs = []
        for pattern in similar_patterns:
            if pattern.get('restoration_costs'):
                try:
                    cost = float(pattern['restoration_costs'])
                    costs.append(cost)
                except ValueError:
                    continue
        
        if costs:
            # Set range based on percentiles
            costs.sort()
            p10 = costs[int(len(costs) * 0.1)]
            p90 = costs[int(len(costs) * 0.9)]
            
            return {
                'min_cost': max(1_000, p10 * 0.1),  # 10% of 10th percentile
                'max_cost': min(50_000_000_000, p90 * 10)  # 10x of 90th percentile
            }
        
        return {'min_cost': 10_000, 'max_cost': 10_000_000_000}
```

---

## 📋 **QUALITY REPORTING & MONITORING**

### **Real-time Quality Dashboard**

```python
class QualityDashboard:
    """
    Real-time Dashboard für Qualitäts-Monitoring
    Visualisiert Qualitätstrends und Alert-Management
    """
    
    def __init__(self):
        self.quality_metrics = QualityMetricsCollector()
        self.alert_manager = QualityAlertManager()
    
    async def generate_quality_report(self, time_period: str = "24h") -> QualityDashboardReport:
        """
        Generiert umfassenden Qualitätsbericht
        """
        
        # Collect metrics for time period
        metrics = await self.quality_metrics.collect_period_metrics(time_period)
        
        # Calculate quality trends
        trends = self._calculate_quality_trends(metrics)
        
        # Identify quality alerts
        alerts = await self.alert_manager.get_active_alerts()
        
        # Generate recommendations
        recommendations = self._generate_dashboard_recommendations(metrics, trends, alerts)
        
        return QualityDashboardReport(
            period=time_period,
            overall_quality_trend=trends['overall'],
            dimension_trends=trends['dimensions'],
            active_alerts=alerts,
            recommendations=recommendations,
            top_performing_sources=metrics['top_sources'],
            quality_bottlenecks=metrics['bottlenecks']
        )
    
    def _calculate_quality_trends(self, metrics: Dict) -> Dict:
        """
        Berechnet Qualitätstrends über Zeit
        """
        
        trends = {}
        
        # Overall quality trend
        quality_scores = metrics.get('quality_scores_over_time', [])
        if len(quality_scores) >= 2:
            recent_avg = sum(quality_scores[-5:]) / min(5, len(quality_scores))
            older_avg = sum(quality_scores[:5]) / min(5, len(quality_scores))
            
            trends['overall'] = {
                'direction': 'improving' if recent_avg > older_avg else 'declining',
                'change_percentage': ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0
            }
        else:
            trends['overall'] = {'direction': 'stable', 'change_percentage': 0}
        
        # Dimension-specific trends
        trends['dimensions'] = {}
        for dimension in ['completeness', 'accuracy', 'consistency', 'freshness', 'reliability']:
            dimension_scores = metrics.get(f'{dimension}_scores', [])
            if len(dimension_scores) >= 2:
                recent = sum(dimension_scores[-3:]) / min(3, len(dimension_scores))
                older = sum(dimension_scores[:3]) / min(3, len(dimension_scores))
                
                trends['dimensions'][dimension] = {
                    'current_score': recent,
                    'trend': 'up' if recent > older else 'down',
                    'change': recent - older
                }
        
        return trends
```

---

**Status:** ✅ Quality Assurance Framework Complete  
**Implementation Priority:** Integriert in alle Phasen (Phase 1+)  
**Quality Target:** 85%+ overall quality score für production data  

*Dieses umfassende Qualitätssicherungs-Framework gewährleistet höchste Datenqualität durch multi-level Validation und kontinuierliche Verbesserung.*