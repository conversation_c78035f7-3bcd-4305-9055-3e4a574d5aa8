"""
Test Suite für MineExtractorWeb v1.0
Comprehensive testing für alle Komponenten
"""

import unittest
import asyncio
import os
import sys
from pathlib import Path

# Add src to path for testing
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class TestMineDataModels(unittest.TestCase):
    """Tests für Mine Data Models"""
    
    def setUp(self):
        from src.mine_data_models import MineDataFields, MineResearchResult
        self.MineDataFields = MineDataFields
        self.MineResearchResult = MineResearchResult
    
    def test_mine_data_fields_creation(self):
        """Test MineDataFields creation and basic functionality"""
        mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv",
            restaurationskosten_cad="1000000"
        )
        
        self.assertEqual(mine.name, "Test Mine")
        self.assertEqual(mine.betreiber, "Test Company")
        self.assertEqual(mine.aktivitaetsstatus, "aktiv")
        self.assertEqual(mine.restaurationskosten_cad, "1000000")
        
        # Test ID generation
        self.assertTrue(mine.id)
        self.assertIsInstance(mine.id, str)
    
    def test_mine_data_completion_rate(self):
        """Test completion rate calculation"""
        # Empty mine
        empty_mine = self.MineDataFields()
        self.assertEqual(empty_mine.get_completion_rate(), 0.0)
        
        # Partially filled mine
        partial_mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company"
        )
        completion = partial_mine.get_completion_rate()
        self.assertGreater(completion, 0.0)
        self.assertLess(completion, 1.0)
    
    def test_mine_data_validation(self):
        """Test mine data validation"""
        # Valid mine
        valid_mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company",
            aktivitaetsstatus="aktiv",
            rohstoffabbau="Gold"
        )
        self.assertTrue(valid_mine.is_valid_mine_data())
        
        # Invalid mine (only name)
        invalid_mine = self.MineDataFields(name="Test Mine")
        self.assertFalse(invalid_mine.is_valid_mine_data())
    
    def test_csv_conversion(self):
        """Test CSV dictionary conversion"""
        mine = self.MineDataFields(
            name="Test Mine",
            betreiber="Test Company"
        )
        
        csv_dict = mine.to_dict()
        self.assertIsInstance(csv_dict, dict)
        self.assertEqual(csv_dict['Name'], "Test Mine")
        self.assertEqual(csv_dict['Betreiber'], "Test Company")
        
        # Test reverse conversion
        mine_from_dict = self.MineDataFields.from_dict(csv_dict)
        self.assertEqual(mine_from_dict.name, "Test Mine")
        self.assertEqual(mine_from_dict.betreiber, "Test Company")

class TestDataParser(unittest.TestCase):
    """Tests für Data Parser"""
    
    def setUp(self):
        from src.data_parser import MiningDataParser, quick_parse
        self.parser = MiningDataParser()
        self.quick_parse = quick_parse
    
    def test_operator_extraction(self):
        """Test operator extraction"""
        test_text = "The mine is operated by Newmont Corporation."
        result = self.parser._extract_operator(test_text)
        self.assertTrue(result)
        self.assertIn("Newmont", result)
    
    def test_cost_extraction(self):
        """Test restoration cost extraction"""
        test_cases = [
            "Restoration costs are $45 million CAD",
            "Environmental costs: CAD $45,000,000",
            "Closure costs estimated at 45.2 million dollars",
            "Coûts de restauration: 45 millions CAD"
        ]
        
        for test_text in test_cases:
            result = self.parser._extract_restoration_costs(test_text)
            if result:  # Some patterns might not match all formats
                self.assertTrue(result.isdigit() or '.' in result)
    
    def test_coordinates_extraction(self):
        """Test coordinate extraction"""
        test_text = "The mine is located at 53.4°N, 76.8°W"
        x_coord, y_coord = self.parser._extract_coordinates(test_text)
        
        if x_coord and y_coord:
            self.assertTrue(float(x_coord) < 0)  # West longitude
            self.assertTrue(float(y_coord) > 0)  # North latitude
    
    def test_status_extraction(self):
        """Test status extraction"""
        test_cases = [
            ("The mine is currently active", "aktiv"),
            ("Operations have ceased", "geschlossen"),
            ("Mine is planned for development", "geplant"),
            ("Operations are suspended", "ausgesetzt")
        ]
        
        for test_text, expected_status in test_cases:
            result = self.parser._extract_status(test_text)
            if result:  # Some patterns might not match
                self.assertEqual(result, expected_status)
    
    def test_quick_parse_function(self):
        """Test quick parse utility function"""
        sample_text = """
        Éléonore mine is operated by Newmont Corporation. 
        The mine is currently active.
        Environmental restoration costs are estimated at $45.2 million CAD.
        This is an underground gold mine.
        """
        
        result = self.quick_parse(sample_text, "Éléonore Test")
        
        self.assertEqual(result.name, "Éléonore Test")
        self.assertTrue(result.betreiber)
        self.assertTrue(result.aktivitaetsstatus)

class TestPerplexityClient(unittest.TestCase):
    """Tests für Perplexity Client"""
    
    def setUp(self):
        # Skip if no API key available
        self.api_key = os.getenv('PERPLEXITY_API_KEY', 'test-key')
        self.skip_real_tests = self.api_key == 'test-key'
    
    def test_client_initialization(self):
        """Test client initialization"""
        from src.perplexity_client import PerplexityClient
        
        # Test with API key
        client = PerplexityClient(self.api_key)
        self.assertEqual(client.api_key, self.api_key)
        
        # Test without API key (should raise error)
        with self.assertRaises(ValueError):
            PerplexityClient("")
    
    @unittest.skipIf(os.getenv('PERPLEXITY_API_KEY') is None, "No API key available")
    def test_real_api_connection(self):
        """Test real API connection (only if API key is available)"""
        from src.perplexity_client import test_api_key
        
        async def run_test():
            result = await test_api_key(self.api_key)
            return result
        
        if not self.skip_real_tests:
            result = asyncio.run(run_test())
            self.assertTrue(result)

class TestWebResearcher(unittest.TestCase):
    """Tests für Web Researcher"""
    
    def setUp(self):
        from src.web_researcher import WebMiningResearcher
        self.WebMiningResearcher = WebMiningResearcher
        
        # Test configuration
        self.test_config = {'perplexity_key': 'test-key'}
    
    def test_researcher_initialization(self):
        """Test researcher initialization"""
        researcher = self.WebMiningResearcher(self.test_config)
        
        self.assertEqual(researcher.config, self.test_config)
        self.assertTrue(hasattr(researcher, 'parser'))
        self.assertTrue(hasattr(researcher, 'api_clients'))
    
    def test_api_client_setup(self):
        """Test API client setup"""
        researcher = self.WebMiningResearcher(self.test_config)
        
        # Should have perplexity configured
        self.assertIn('perplexity', researcher.api_clients)
        self.assertTrue(researcher.api_clients['perplexity']['enabled'])
    
    def test_mine_name_normalization(self):
        """Test mine name normalization"""
        researcher = self.WebMiningResearcher(self.test_config)
        
        # Test known mappings
        normalized = researcher._normalize_mine_name("eleonore")
        expected = "Éléonore"  # From settings mappings
        # Note: This might vary based on settings availability
        
        # At minimum, should return clean string
        self.assertIsInstance(normalized, str)
        self.assertTrue(len(normalized) > 0)
    
    def test_statistics_tracking(self):
        """Test statistics tracking"""
        researcher = self.WebMiningResearcher(self.test_config)
        
        # Initial stats
        stats = researcher.get_research_statistics()
        self.assertEqual(stats['total_researched'], 0)
        self.assertEqual(stats['successful_researches'], 0)
        
        # Reset stats
        researcher.reset_statistics()
        stats_after_reset = researcher.get_research_statistics()
        self.assertEqual(stats_after_reset['total_researched'], 0)

class TestIntegration(unittest.TestCase):
    """Integration Tests"""
    
    def test_config_loading(self):
        """Test configuration loading"""
        try:
            from config.api_keys import APIConfig
            from config.settings import ProjectSettings
            
            # Test API config
            validation = APIConfig.validate_config()
            self.assertIsInstance(validation, dict)
            
            # Test project settings
            info = ProjectSettings.get_project_info()
            self.assertIsInstance(info, dict)
            self.assertIn('name', info)
            self.assertIn('version', info)
            
        except ImportError:
            self.skipTest("Configuration modules not available")
    
    def test_end_to_end_mock(self):
        """Test end-to-end workflow with mock data"""
        from src.data_parser import quick_parse
        
        # Mock API response
        mock_response = {
            'content': '''
            Éléonore mine is operated by Newmont Corporation in Quebec, Canada.
            The mine is currently active and produces gold.
            Environmental restoration costs are estimated at $45 million CAD as of 2023.
            This is an underground mining operation that started production in 2014.
            The mine site covers approximately 125 hectares.
            ''',
            'citations': [
                {'url': 'https://example.com/eleonore', 'title': 'Éléonore Mine Info'}
            ]
        }
        
        # Parse mock response
        mine_data = quick_parse(mock_response['content'], "Éléonore")
        
        # Verify parsed data
        self.assertEqual(mine_data.name, "Éléonore")
        self.assertTrue(mine_data.betreiber)
        self.assertTrue(mine_data.aktivitaetsstatus)
        self.assertTrue(mine_data.rohstoffabbau)
        
        # Test CSV conversion
        csv_dict = mine_data.to_dict()
        self.assertIsInstance(csv_dict, dict)
        self.assertEqual(csv_dict['Name'], "Éléonore")

def run_all_tests():
    """Run all tests"""
    print("🧪 Running MineExtractorWeb Test Suite")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestMineDataModels,
        TestDataParser,
        TestPerplexityClient,
        TestWebResearcher,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nOverall Result: {status}")
    
    return success

def run_quick_tests():
    """Run only quick tests (no API calls)"""
    print("⚡ Running Quick Tests (No API Calls)")
    print("=" * 40)
    
    # Create test suite with only non-API tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add only specific test classes/methods
    suite.addTests(loader.loadTestsFromTestCase(TestMineDataModels))
    suite.addTests(loader.loadTestsFromTestCase(TestDataParser))
    
    # Add specific non-API methods from other classes
    suite.addTest(TestWebResearcher('test_researcher_initialization'))
    suite.addTest(TestWebResearcher('test_api_client_setup'))
    suite.addTest(TestIntegration('test_end_to_end_mock'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nQuick Tests Result: {status}")
    
    return success

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MineExtractorWeb Test Suite")
    parser.add_argument("--quick", action="store_true", help="Run only quick tests (no API calls)")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    
    args = parser.parse_args()
    
    if args.quick:
        success = run_quick_tests()
    else:
        success = run_all_tests()
    
    sys.exit(0 if success else 1)
