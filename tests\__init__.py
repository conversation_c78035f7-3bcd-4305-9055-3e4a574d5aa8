"""
Test Package für MineExtractorWeb v1.0
"""

# Version info
__version__ = "1.0.0"

# Test utilities
def setup_test_environment():
    """Setup test environment with mock data"""
    import os
    import sys
    from pathlib import Path
    
    # Add src to path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root / "src"))
    
    # Set test environment variables
    os.environ['TEST_MODE'] = 'True'
    os.environ['LOG_LEVEL'] = 'WARNING'  # Reduce log noise during tests
    
    return project_root

def create_mock_api_response():
    """Create mock API response for testing"""
    return {
        'success': True,
        'content': '''
        Éléonore mine is operated by Newmont Corporation in Quebec, Canada.
        The mine is currently active and produces gold.
        Environmental restoration costs are estimated at $45 million CAD as of 2023.
        This is an underground mining operation that started production in 2014.
        The mine site covers approximately 125 hectares.
        Location coordinates: 53.4°N, 76.8°W
        Annual production: 125,000 tonnes of ore.
        ''',
        'citations': [
            {
                'url': 'https://example.com/eleonore-mine',
                'title': 'Éléonore Mine Overview',
                'snippet': 'Comprehensive information about Éléonore mine operations'
            },
            {
                'url': 'https://newmont.com/operations/eleonore',
                'title': 'Newmont - Éléonore Operations',
                'snippet': 'Official company information about the mine'
            }
        ],
        'source_count': 2,
        'response_time': 15.3,
        'api_source': 'perplexity'
    }

def get_test_mine_names():
    """Get list of test mine names"""
    return [
        "Éléonore",
        "Canadian Malartic",
        "Raglan",
        "Casa Berardi",
        "LaRonde"
    ]

# Quick test runner
def run_quick_tests():
    """Run quick tests without API calls"""
    from .test_main import run_quick_tests
    return run_quick_tests()

# Test data
TEST_MINES = get_test_mine_names()
MOCK_API_RESPONSE = create_mock_api_response()
