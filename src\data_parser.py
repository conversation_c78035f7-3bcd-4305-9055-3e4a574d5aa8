"""
Data Parser für MineExtractorWeb v1.0
Intelligente Extraktion von Mining-Daten aus API-Responses und Web-Content
"""

import re
import json
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from datetime import datetime
import unicodedata

try:
    from .mine_data_models import MineDataFields, MineResearchResult, SourceMetadata
except ImportError:
    from mine_data_models import MineDataFields, MineResearchResult, SourceMetadata

logger = logging.getLogger(__name__)

@dataclass
class ExtractionPattern:
    """Pattern für Datenextraktion"""
    field: str
    patterns: List[str]
    weight: float = 1.0
    required: bool = False

class MiningDataParser:
    """
    Intelligenter Parser für Mining-Daten aus verschiedenen Quellen
    Verwendet Regex-Patterns und Context-Analyse für robuste Extraktion
    """
    
    def __init__(self):
        """Initialisiert Parser mit vorkonfigurierten Patterns"""
        
        # Setup extraction patterns
        self._setup_extraction_patterns()
        
        # Normalization mappings
        self._setup_normalization_mappings()
        
        # Statistics tracking
        self.parsing_stats = {
            'total_parsed': 0,
            'successful_extractions': 0,
            'field_extraction_rates': {},
            'confidence_scores': [],
            'processing_times': []
        }
        
        logger.info("MiningDataParser initialized with comprehensive patterns")
    
    def _setup_extraction_patterns(self):
        """Konfiguriert Extraction-Patterns für verschiedene Datenfelder"""
        
        self.extraction_patterns = {
            # Operator/Betreiber Patterns
            'betreiber': ExtractionPattern(
                field='betreiber',
                patterns=[
                    r'operator[:\s]+([^,.\n]+)',
                    r'operated by\s+([^,.\n]+)',
                    r'owner[:\s]+([^,.\n]+)',
                    r'company[:\s]+([^,.\n]+)',
                    r'betreiber[:\s]+([^,.\n]+)',
                    r'exploitant[:\s]+([^,.\n]+)',
                    r'(?:operated|owned|managed)\s+by\s+([A-Z][^,.\n]+)',
                    r'([A-Z][a-zA-Z\s&-]+(?:Inc|Corp|Ltd|Limited|Corporation|Company|Mining|Resources))',
                ],
                weight=0.9,
                required=True
            ),
            
            # Restaurationskosten Patterns
            'restaurationskosten_cad': ExtractionPattern(
                field='restaurationskosten_cad',
                patterns=[
                    r'restoration\s+cost[s]?\s*[:$]*\s*(?:CAD\s*)?(\$?[\d,.]+ ?\w*)',
                    r'closure\s+cost[s]?\s*[:$]*\s*(?:CAD\s*)?(\$?[\d,.]+ ?\w*)',
                    r'environmental\s+cost[s]?\s*[:$]*\s*(?:CAD\s*)?(\$?[\d,.]+ ?\w*)',
                    r'reclamation\s+cost[s]?\s*[:$]*\s*(?:CAD\s*)?(\$?[\d,.]+ ?\w*)',
                    r'rehabilitation\s+cost[s]?\s*[:$]*\s*(?:CAD\s*)?(\$?[\d,.]+ ?\w*)',
                    r'(\$[\d,.]+ ?\w*)\s*(?:CAD|Canadian|million|billion)',
                    r'(?:CAD|C\$)\s*(\$?[\d,.]+ ?\w*)',
                    r'coût[s]?\s+de\s+restauration\s*[:$]*\s*(\$?[\d,.]+ ?\w*)',
                ],
                weight=1.0,
                required=True
            ),
            
            # Status Patterns
            'aktivitaetsstatus': ExtractionPattern(
                field='aktivitaetsstatus',
                patterns=[
                    r'status[:\s]+(active|inactive|closed|operating|operational|suspended|planned)',
                    r'(?:currently|mine\s+is)\s+(active|inactive|closed|operating|operational|suspended)',
                    r'statut[:\s]+(actif|inactif|fermé|exploité)',
                    r'mine\s+(?:is|status)[:\s]+(active|inactive|closed|operating|operational)',
                ],
                weight=0.8,
                required=True
            ),
            
            # Rohstoff Patterns
            'rohstoffabbau': ExtractionPattern(
                field='rohstoffabbau',
                patterns=[
                    r'commodity[:\s]+(gold|copper|iron|zinc|nickel|uranium|coal|silver)',
                    r'mineral[:\s]+(gold|copper|iron|zinc|nickel|uranium|coal|silver)',
                    r'produces?\s+(gold|copper|iron|zinc|nickel|uranium|coal|silver)',
                    r'mining\s+(gold|copper|iron|zinc|nickel|uranium|coal|silver)',
                    r'(gold|copper|iron|zinc|nickel|uranium|coal|silver)\s+mine',
                    r'extraction\s+(?:of\s+)?(gold|copper|iron|zinc|nickel|uranium|coal|silver)',
                ],
                weight=0.8,
                required=True
            ),
            
            # Mine Type Patterns
            'minentyp': ExtractionPattern(
                field='minentyp',
                patterns=[
                    r'mine\s+type[:\s]+(open.?pit|underground|surface|strip)',
                    r'(open.?pit|underground|surface|strip)\s+mine',
                    r'mining\s+method[:\s]+(open.?pit|underground|surface|strip)',
                    r'exploitation[:\s]+(à\s+ciel\s+ouvert|souterraine)',
                ],
                weight=0.7
            ),
            
            # Koordinaten Patterns
            'koordinaten': ExtractionPattern(
                field='koordinaten',
                patterns=[
                    r'coordinates?[:\s]*([+-]?\d+\.?\d*°?\s*[NS]?,?\s*[+-]?\d+\.?\d*°?\s*[EW]?)',
                    r'location[:\s]*([+-]?\d+\.?\d*°?\s*[NS]?,?\s*[+-]?\d+\.?\d*°?\s*[EW]?)',
                    r'lat[itude]*[:\s]*([+-]?\d+\.?\d*).*?lon[gitude]*[:\s]*([+-]?\d+\.?\d*)',
                    r'([+-]?\d{2,3}\.\d+)[°\s]*[NS]?[,\s]+([+-]?\d{2,3}\.\d+)[°\s]*[EW]?',
                ],
                weight=0.6
            ),
            
            # Produktionsdaten Patterns
            'produktion': ExtractionPattern(
                field='produktion',
                patterns=[
                    r'production\s+start[ed]*[:\s]*(\d{4})',
                    r'start[ed]*\s+(?:production|mining|operations)[:\s]*(\d{4})',
                    r'first\s+production[:\s]*(\d{4})',
                    r'began\s+(?:production|mining|operations)[:\s]*(\d{4})',
                    r'commercial\s+production[:\s]*(\d{4})',
                ],
                weight=0.6
            ),
            
            # Flächeninformation Patterns
            'flaeche': ExtractionPattern(
                field='flaeche',
                patterns=[
                    r'area[:\s]*([\d,.]+)\s*(?:km²|km2|square\s+km|hectares)',
                    r'size[:\s]*([\d,.]+)\s*(?:km²|km2|square\s+km|hectares)',
                    r'surface[:\s]*([\d,.]+)\s*(?:km²|km2)',
                    r'([\d,.]+)\s*(?:km²|km2|square\s+kilometers)',
                ],
                weight=0.5
            )
        }
    
    def _setup_normalization_mappings(self):
        """Konfiguriert Normalisierungs-Mappings"""
        
        self.status_mappings = {
            'active': 'aktiv',
            'operating': 'aktiv', 
            'operational': 'aktiv',
            'inactive': 'inaktiv',
            'suspended': 'stillgelegt',
            'closed': 'geschlossen',
            'planned': 'geplant',
            'actif': 'aktiv',
            'inactif': 'inaktiv',
            'fermé': 'geschlossen'
        }
        
        self.commodity_mappings = {
            'gold': 'Gold',
            'copper': 'Kupfer',
            'iron': 'Eisenerz',
            'zinc': 'Zink',
            'nickel': 'Nickel',
            'uranium': 'Uran',
            'coal': 'Kohle',
            'silver': 'Silber'
        }
        
        self.mine_type_mappings = {
            'open-pit': 'Tagebau',
            'open pit': 'Tagebau',
            'openpit': 'Tagebau',
            'underground': 'Untertage',
            'surface': 'Oberfläche',
            'strip': 'Tagebau',
            'à ciel ouvert': 'Tagebau',
            'souterraine': 'Untertage'
        }
    
    def parse_api_response(self, api_response: Dict[str, Any], mine_name: str, 
                          api_source: str = "unknown") -> MineDataFields:
        """
        Parst API-Response und extrahiert Mining-Daten
        
        Args:
            api_response: Raw API response dictionary
            mine_name: Name der Mine
            api_source: Quelle der API (perplexity, tavily, etc.)
            
        Returns:
            MineDataFields mit extrahierten Daten
        """
        
        start_time = datetime.now()
        logger.info(f"Parsing API response for {mine_name} from {api_source}")
        
        # Initialize result
        mine_data = MineDataFields()
        mine_data.name = self._normalize_mine_name(mine_name)[0]
        mine_data.id = MineDataFields.generate_id(mine_data.name)
        
        try:
            # Extract content from API response
            content = self._extract_content_from_response(api_response, api_source)
            
            if not content:
                logger.warning(f"No content extracted from {api_source} response for {mine_name}")
                return mine_data
            
            # Extract all fields using patterns
            extracted_data = self._extract_all_fields(content, mine_name)
            
            # Apply extracted data to mine_data
            self._apply_extracted_data(mine_data, extracted_data)
            
            # Extract and set sources
            sources = self._extract_sources_from_response(api_response, api_source)
            mine_data.quellenangaben = "; ".join(sources[:5])  # Top 5 sources
            
            # Update statistics
            self._update_parsing_stats(mine_name, extracted_data, start_time)
            
            logger.info(f"Parsing completed for {mine_name}: {mine_data.get_completion_rate():.1%} complete")
            
            return mine_data
            
        except Exception as e:
            logger.error(f"Error parsing API response for {mine_name}: {e}")
            self.parsing_stats['total_parsed'] += 1
            return mine_data
    
    def _extract_content_from_response(self, response: Dict[str, Any], api_source: str) -> str:
        """Extrahiert Text-Content aus API-Response"""
        
        content = ""
        
        try:
            if api_source.lower() == "perplexity":
                if 'content' in response:
                    content = response['content']
                elif 'data' in response and 'choices' in response['data']:
                    content = response['data']['choices'][0]['message']['content']
                elif 'raw_response' in response:
                    content = response['raw_response']['choices'][0]['message']['content']
            
            elif api_source.lower() == "tavily":
                # Future implementation for Tavily
                if 'results' in response:
                    content = " ".join([r.get('content', '') for r in response['results']])
            
            elif api_source.lower() == "exa":
                # Future implementation for Exa
                if 'results' in response:
                    content = " ".join([r.get('text', '') for r in response['results']])
            
            else:
                # Generic extraction
                if isinstance(response, dict):
                    for key in ['content', 'text', 'body', 'result']:
                        if key in response:
                            content = str(response[key])
                            break
                elif isinstance(response, str):
                    content = response
            
        except Exception as e:
            logger.error(f"Error extracting content from {api_source}: {e}")
        
        return content.strip()
    
    def _extract_all_fields(self, content: str, mine_name: str) -> Dict[str, Any]:
        """Extrahiert alle Datenfelder aus Content"""
        
        extracted_data = {}
        
        # Clean content for better matching
        cleaned_content = self._clean_content(content)
        
        # Extract each field using patterns
        for field_name, pattern_config in self.extraction_patterns.items():
            extracted_value = self._extract_field_with_patterns(
                cleaned_content, pattern_config, mine_name
            )
            
            if extracted_value:
                extracted_data[field_name] = extracted_value
                logger.debug(f"Extracted {field_name}: {extracted_value}")
        
        return extracted_data
    
    def _clean_content(self, content: str) -> str:
        """Bereinigt Content für bessere Pattern-Erkennung"""
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove common unwanted characters
        content = re.sub(r'[^\w\s\$\.\,\-\:\;\(\)°NSEW]', '', content)
        
        # Normalize currency symbols
        content = re.sub(r'C\$|CAD\$', '$', content)
        
        return content.strip()
    
    def _extract_field_with_patterns(self, content: str, pattern_config: ExtractionPattern, 
                                   mine_name: str) -> Optional[str]:
        """Extrahiert einzelnes Feld mit konfigurierten Patterns"""
        
        field_name = pattern_config.field
        
        for pattern in pattern_config.patterns:
            try:
                # Case-insensitive matching
                matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    if match.groups():
                        extracted_value = match.group(1).strip()
                        
                        # Post-process based on field type
                        processed_value = self._post_process_field_value(
                            field_name, extracted_value, content, mine_name
                        )
                        
                        if processed_value:
                            return processed_value
                        
            except Exception as e:
                logger.debug(f"Pattern matching error for {field_name}: {e}")
                continue
        
        return None
    
    def _post_process_field_value(self, field_name: str, value: str, 
                                context: str, mine_name: str) -> Optional[str]:
        """Post-Processing für extrahierte Feldwerte"""
        
        if not value or len(value.strip()) < 2:
            return None
        
        value = value.strip()
        
        if field_name == 'betreiber':
            return self._post_process_operator(value, context, mine_name)
        
        elif field_name == 'restaurationskosten_cad':
            return self._post_process_restoration_costs(value)
        
        elif field_name == 'aktivitaetsstatus':
            return self._post_process_status(value)
        
        elif field_name == 'rohstoffabbau':
            return self._post_process_commodity(value)
        
        elif field_name == 'minentyp':
            return self._post_process_mine_type(value)
        
        elif field_name == 'koordinaten':
            return self._post_process_coordinates(value)
        
        elif field_name in ['produktion', 'produktionsstart']:
            return self._post_process_year(value)
        
        elif field_name == 'flaeche':
            return self._post_process_area(value)
        
        return value
    
    def _post_process_operator(self, value: str, context: str, mine_name: str) -> Optional[str]:
        """Post-Processing für Betreiber"""
        
        # Remove common prefixes/suffixes
        value = re.sub(r'^(the\s+)?', '', value, flags=re.IGNORECASE)
        value = re.sub(r'\s+(company|corp|corporation|inc|ltd|limited)\.?$', '', value, flags=re.IGNORECASE)
        
        # Validate operator name
        if len(value) < 3 or len(value) > 100:
            return None
        
        # Check if it's actually a mine name (avoid self-reference)
        if mine_name.lower() in value.lower() and len(value) < len(mine_name) + 10:
            return None
        
        # Common non-operator words to exclude
        exclusion_words = ['mine', 'mining', 'project', 'deposit', 'site', 'area']
        if any(word in value.lower() for word in exclusion_words) and len(value) < 20:
            return None
        
        return value.title()
    
    def _post_process_restoration_costs(self, value: str) -> Optional[str]:
        """Post-Processing für Restaurationskosten"""
        
        # Extract numeric value and unit
        amount_pattern = r'(\$?)([\d,\.]+)\s*(\w+)?'
        match = re.search(amount_pattern, value)
        
        if not match:
            return None
        
        currency_symbol, amount, unit = match.groups()
        
        # Clean amount
        amount = amount.replace(',', '')
        
        try:
            numeric_value = float(amount)
        except ValueError:
            return None
        
        # Handle units (million, billion, etc.)
        if unit and unit.lower() in ['million', 'mil', 'm']:
            numeric_value *= 1_000_000
        elif unit and unit.lower() in ['billion', 'bil', 'b']:
            numeric_value *= 1_000_000_000
        
        # Format as CAD
        if numeric_value >= 1_000_000:
            return f"${numeric_value:,.0f} CAD"
        else:
            return f"${numeric_value:,.0f} CAD"
    
    def _post_process_status(self, value: str) -> Optional[str]:
        """Post-Processing für Aktivitätsstatus"""
        
        value_lower = value.lower().strip()
        
        # Apply normalization mapping
        return self.status_mappings.get(value_lower, value.lower())
    
    def _post_process_commodity(self, value: str) -> Optional[str]:
        """Post-Processing für Rohstoffabbau"""
        
        value_lower = value.lower().strip()
        
        # Apply commodity mapping
        return self.commodity_mappings.get(value_lower, value.title())
    
    def _post_process_mine_type(self, value: str) -> Optional[str]:
        """Post-Processing für Minentyp"""
        
        value_lower = value.lower().strip()
        
        # Apply mine type mapping
        return self.mine_type_mappings.get(value_lower, value.title())
    
    def _post_process_coordinates(self, value: str) -> Optional[str]:
        """Post-Processing für Koordinaten"""
        
        # Extract latitude and longitude
        coord_pattern = r'([+-]?\d+\.?\d*)[°\s]*[NS]?[,\s]+([+-]?\d+\.?\d*)[°\s]*[EW]?'
        match = re.search(coord_pattern, value)
        
        if match:
            lat, lon = match.groups()
            try:
                lat_f = float(lat)
                lon_f = float(lon)
                
                # Validate coordinate ranges
                if -90 <= lat_f <= 90 and -180 <= lon_f <= 180:
                    return f"{lat_f:.6f}, {lon_f:.6f}"
            except ValueError:
                pass
        
        return None
    
    def _post_process_year(self, value: str) -> Optional[str]:
        """Post-Processing für Jahreszahlen"""
        
        year_match = re.search(r'(\d{4})', value)
        if year_match:
            year = int(year_match.group(1))
            # Validate year range
            if 1800 <= year <= 2030:
                return str(year)
        
        return None
    
    def _post_process_area(self, value: str) -> Optional[str]:
        """Post-Processing für Flächenangaben"""
        
        # Extract numeric value
        area_match = re.search(r'([\d,\.]+)', value)
        if area_match:
            try:
                area_value = float(area_match.group(1).replace(',', ''))
                return f"{area_value:.2f}"
            except ValueError:
                pass
        
        return None
    
    def _apply_extracted_data(self, mine_data: MineDataFields, extracted_data: Dict[str, Any]):
        """Wendet extrahierte Daten auf MineDataFields an"""
        
        field_mapping = {
            'betreiber': 'betreiber',
            'restaurationskosten_cad': 'restaurationskosten_cad',
            'aktivitaetsstatus': 'aktivitaetsstatus',
            'rohstoffabbau': 'rohstoffabbau',
            'minentyp': 'minentyp',
            'koordinaten': ['x_koordinate', 'y_koordinate'],
            'produktion': 'produktionsstart',
            'flaeche': 'flaeche_km2'
        }
        
        for extract_key, field_name in field_mapping.items():
            if extract_key in extracted_data:
                value = extracted_data[extract_key]
                
                if extract_key == 'koordinaten' and isinstance(field_name, list):
                    # Split coordinates
                    coords = value.split(',')
                    if len(coords) == 2:
                        setattr(mine_data, field_name[0], coords[0].strip())
                        setattr(mine_data, field_name[1], coords[1].strip())
                else:
                    setattr(mine_data, field_name, value)
    
    def _extract_sources_from_response(self, response: Dict[str, Any], api_source: str) -> List[str]:
        """Extrahiert Quellen-URLs aus API-Response"""
        
        sources = []
        
        try:
            if api_source.lower() == "perplexity":
                # Extract from citations
                if 'citations' in response:
                    sources = [c.get('url', '') for c in response['citations'] if c.get('url')]
                elif 'processed_sources' in response:
                    sources = [s.get('url', '') for s in response['processed_sources'] if s.get('url')]
            
            # Clean and validate URLs
            valid_sources = []
            for source in sources:
                if source and source.startswith(('http://', 'https://')):
                    valid_sources.append(source)
            
            return valid_sources[:10]  # Limit to 10 sources
            
        except Exception as e:
            logger.error(f"Error extracting sources: {e}")
            return []
    
    def _normalize_mine_name(self, mine_name: str) -> str:
        """Normalisiert Mine-Namen (entfernt Zusätze und gibt auch akzentfreie Variante zurück)"""
        name = re.sub(r'\s+(mine|mining|project|deposit)$', '', mine_name, flags=re.IGNORECASE)
        name = re.sub(r'^(the\s+)', '', name, flags=re.IGNORECASE)
        name = name.strip()
        # Akzentfreie Variante
        name_no_accents = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        return name, name_no_accents
    
    def _update_parsing_stats(self, mine_name: str, extracted_data: Dict[str, Any], start_time: datetime):
        """Aktualisiert Parsing-Statistiken"""
        
        self.parsing_stats['total_parsed'] += 1
        
        # Processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        self.parsing_stats['processing_times'].append(processing_time)
        
        # Field extraction rates
        for field_name in self.extraction_patterns.keys():
            if field_name not in self.parsing_stats['field_extraction_rates']:
                self.parsing_stats['field_extraction_rates'][field_name] = {
                    'success': 0,
                    'total': 0,
                    'success_rate': 0.0
                }
            
            self.parsing_stats['field_extraction_rates'][field_name]['total'] += 1
            
            if field_name in extracted_data:
                self.parsing_stats['field_extraction_rates'][field_name]['success'] += 1
        
        # Update success rates
        for field_name, stats in self.parsing_stats['field_extraction_rates'].items():
            if stats['total'] > 0:
                stats['success_rate'] = stats['success'] / stats['total']
        
        # Overall success
        if extracted_data:
            self.parsing_stats['successful_extractions'] += 1
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Gibt Parsing-Statistiken zurück"""
        
        stats = self.parsing_stats.copy()
        
        # Calculate derived metrics
        if stats['total_parsed'] > 0:
            stats['overall_success_rate'] = stats['successful_extractions'] / stats['total_parsed']
            stats['avg_processing_time'] = sum(stats['processing_times']) / len(stats['processing_times']) if stats['processing_times'] else 0
        else:
            stats['overall_success_rate'] = 0.0
            stats['avg_processing_time'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """Setzt Parsing-Statistiken zurück"""
        
        self.parsing_stats = {
            'total_parsed': 0,
            'successful_extractions': 0,
            'field_extraction_rates': {},
            'confidence_scores': [],
            'processing_times': []
        }
        
        logger.info("Parsing statistics reset")

# Convenience functions

def quick_parse(content: str, mine_name: str) -> MineDataFields:
    """
    Quick parsing function für einfache Nutzung
    
    Args:
        content: Text content to parse
        mine_name: Name der Mine
        
    Returns:
        MineDataFields mit extrahierten Daten
    """
    
    parser = MiningDataParser()
    
    # Create mock API response
    mock_response = {
        'content': content,
        'citations': [],
        'processed_sources': []
    }
    
    return parser.parse_api_response(mock_response, mine_name, 'manual')

def test_patterns():
    """Testet Extraction-Patterns mit Beispiel-Content"""
    
    print("🧪 Testing extraction patterns...")
    
    sample_content = """
    The Éléonore mine is operated by Newmont Corporation.
    The mine is currently active and produces gold.
    Restoration costs are estimated at $45 million CAD.
    It's an underground mine located at coordinates 52.7°N, 76.1°W.
    Commercial production started in 2014.
    The mine covers an area of 15.2 km².
    """
    
    result = quick_parse(sample_content, "Éléonore Test")
    
    print(f"✅ Parsed mine: {result.name}")
    print(f"   Operator: {result.betreiber}")
    print(f"   Status: {result.aktivitaetsstatus}")
    print(f"   Commodity: {result.rohstoffabbau}")
    print(f"   Restoration Costs: {result.restaurationskosten_cad}")
    print(f"   Coordinates: {result.x_koordinate}, {result.y_koordinate}")
    print(f"   Completion: {result.get_completion_rate():.1%}")
    
    return result

if __name__ == "__main__":
    # Test the parser
    test_patterns()
    print("\n✅ Data parser test completed!")
