#!/usr/bin/env python3
"""
Quick Start Demo für MineExtractorWeb v1.0
Einfache Demonstration der Hauptfunktionalität
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import List

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class MineExtractorDemo:
    """Demonstration der MineExtractorWeb Funktionalität"""
    
    def __init__(self):
        self.demo_mines = [
            "Éléonore",
            "Canadian Malartic", 
            "Raglan",
            "Casa Berardi",
            "LaRonde"
        ]
        
        self.sample_responses = {
            "Éléonore": {
                'content': """
                Éléonore mine is operated by Newmont Corporation in Quebec, Canada.
                The mine is currently active and operational since 2014.
                Environmental restoration costs are estimated at $45.2 million CAD as of 2023.
                This is an underground gold mine located in the James Bay region.
                The mine covers approximately 125 hectares and produces around 400,000 ounces of gold annually.
                Coordinates: approximately 53.4°N, 76.8°W
                """,
                'citations': [
                    {'url': 'https://newmont.com/operations/eleonore/', 'title': 'Newmont Éléonore Mine'}
                ]
            }
        }
    
    def print_banner(self):
        """Zeigt Demo-Banner"""
        print("=" * 70)
        print("🌐 MineExtractorWeb v1.0 - Quick Start Demo")
        print("   Demonstriert automatische Mining-Daten-Extraktion")
        print("=" * 70)
    
    def check_prerequisites(self) -> bool:
        """Prüft Demo-Voraussetzungen"""
        print("\n🔍 Checking prerequisites...")
        
        # Check if source modules are available
        try:
            from mine_data_models import MineDataFields
            from data_parser import MiningDataParser
            from web_researcher import WebMiningResearcher
            print("   ✅ Core modules available")
        except ImportError as e:
            print(f"   ❌ Missing modules: {e}")
            print("   💡 Run from project root directory")
            return False
        
        # Check configuration
        try:
            from config.api_keys import APIConfig
            if APIConfig.PERPLEXITY_API_KEY and not APIConfig.PERPLEXITY_API_KEY.startswith('your_'):
                print("   ✅ API key configured")
                return True
            else:
                print("   ⚠️  API key not configured (using demo mode)")
                return True
        except:
            print("   ⚠️  Configuration issues (using demo mode)")
            return True
    
    def demonstrate_data_models(self):
        """Demonstriert Data Models"""
        print("\n📊 Demonstrating Data Models...")
        
        from mine_data_models import MineDataFields, MineResearchResult
        
        # Create sample mine data
        mine = MineDataFields(
            name="Éléonore",
            betreiber="Newmont Corporation",
            aktivitaetsstatus="aktiv",
            restaurationskosten_cad="45200000",
            rohstoffabbau="Gold",
            minentyp="Untertage",
            produktionsstart="2014",
            x_koordinate="-76.8",
            y_koordinate="53.4"
        )
        
        print(f"   Created mine data: {mine}")
        print(f"   Completion rate: {mine.get_completion_rate():.1%}")
        print(f"   Valid data: {mine.is_valid_mine_data()}")
        
        # Demonstrate CSV conversion
        csv_dict = mine.to_dict()
        print(f"   CSV fields: {len(csv_dict)} columns")
        print(f"   Sample fields: Name='{csv_dict['Name']}', Betreiber='{csv_dict['Betreiber']}'")
        
        # Create research result
        result = MineResearchResult(mine_data=mine)
        result.add_source("https://newmont.com", "Newmont Official", "company", 0.9)
        result.confidence_score = 0.85
        
        print(f"   Research result: {result}")
        print(f"   Sources: {len(result.sources)}")
        
        return mine, result
    
    def demonstrate_data_parser(self):
        """Demonstriert Data Parser"""
        print("\n🔧 Demonstrating Data Parser...")
        
        from data_parser import MiningDataParser, quick_parse
        
        # Use sample content
        sample_content = self.sample_responses["Éléonore"]['content']
        
        print("   Sample content:")
        print("   " + sample_content.strip().replace('\n', '\n   '))
        
        # Parse with quick_parse
        result = quick_parse(sample_content, "Éléonore")
        
        print(f"\n   Parsed result: {result}")
        print(f"   Extracted operator: '{result.betreiber}'")
        print(f"   Extracted status: '{result.aktivitaetsstatus}'")
        print(f"   Extracted costs: '{result.restaurationskosten_cad}' CAD")
        print(f"   Extracted commodity: '{result.rohstoffabbau}'")
        print(f"   Extracted type: '{result.minentyp}'")
        
        # Test parser patterns
        parser = MiningDataParser()
        
        # Test specific extraction methods
        operator = parser._extract_operator(sample_content)
        status = parser._extract_status(sample_content)
        costs = parser._extract_restoration_costs(sample_content)
        
        print(f"\n   Individual extractions:")
        print(f"   Operator: '{operator}'")
        print(f"   Status: '{status}'")
        print(f"   Costs: '{costs}'")
        
        return result
    
    async def demonstrate_web_researcher(self, use_real_api: bool = False):
        """Demonstriert Web Researcher"""
        print("\n🌐 Demonstrating Web Researcher...")
        
        from web_researcher import WebMiningResearcher
        
        if use_real_api:
            # Real API demonstration
            try:
                from config.api_keys import APIConfig
                config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
                
                researcher = WebMiningResearcher(config)
                print("   ✅ Real API researcher initialized")
                
                # Test API connection
                print("   🧪 Testing API connection...")
                api_tests = await researcher.test_api_connections()
                
                for api_name, success in api_tests.items():
                    status = "✅" if success else "❌"
                    print(f"      {status} {api_name.title()} API")
                
                if any(api_tests.values()):
                    print("   🔍 Running real research for Éléonore...")
                    result = await researcher.research_mine_comprehensive("Éléonore")
                    
                    print(f"   Results:")
                    print(f"      Name: {result.mine_data.name}")
                    print(f"      Completion: {result.data_completeness:.1%}")
                    print(f"      Confidence: {result.confidence_score:.1%}")
                    print(f"      Duration: {result.research_duration:.1f}s")
                    print(f"      Sources: {len(result.sources)}")
                    
                    if result.mine_data.betreiber:
                        print(f"      Operator: {result.mine_data.betreiber}")
                    if result.mine_data.restaurationskosten_cad:
                        print(f"      Restoration Costs: ${result.mine_data.restaurationskosten_cad} CAD")
                    
                    return result
                
            except Exception as e:
                print(f"   ❌ Real API demonstration failed: {e}")
                print("   🔄 Falling back to demo mode...")
        
        # Demo mode (no real API calls)
        print("   🎭 Demo mode (simulated results)")
        
        config = {'perplexity_key': 'demo-key'}
        researcher = WebMiningResearcher(config)
        print("   ✅ Demo researcher initialized")
        
        # Simulate research process
        print("   🔍 Simulating research for Éléonore...")
        print("      📡 API call simulation...")
        await asyncio.sleep(1)  # Simulate processing time
        
        # Create simulated result
        from mine_data_models import MineDataFields, MineResearchResult
        
        mine_data = MineDataFields(
            name="Éléonore",
            betreiber="Newmont Corporation",
            aktivitaetsstatus="aktiv",
            restaurationskosten_cad="45200000",
            rohstoffabbau="Gold",
            minentyp="Untertage",
            produktionsstart="2014"
        )
        
        result = MineResearchResult(mine_data=mine_data)
        result.research_duration = 42.5
        result.confidence_score = 0.87
        result.add_source("https://newmont.com", "Newmont Official", "company", 0.9)
        
        print(f"   ✅ Simulated research completed!")
        print(f"      Completion: {result.data_completeness:.1%}")
        print(f"      Confidence: {result.confidence_score:.1%}")
        print(f"      Duration: {result.research_duration:.1f}s")
        
        return result
    
    def demonstrate_csv_export(self, mine_data_list: List):
        """Demonstriert CSV Export"""
        print("\n💾 Demonstrating CSV Export...")
        
        import pandas as pd
        from datetime import datetime
        
        # Convert mine data to CSV format
        csv_rows = []
        for mine_data in mine_data_list:
            if hasattr(mine_data, 'to_dict'):
                csv_rows.append(mine_data.to_dict())
            elif hasattr(mine_data, 'mine_data'):
                csv_rows.append(mine_data.mine_data.to_dict())
        
        if not csv_rows:
            print("   ⚠️  No data to export")
            return
        
        # Create DataFrame
        df = pd.DataFrame(csv_rows)
        
        # Generate output filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = project_root / "demos" / f"demo_results_{timestamp}.csv"
        
        # Save CSV (same format as production system)
        try:
            from config.settings import ProjectSettings
            df.to_csv(output_file, index=False, 
                     encoding=ProjectSettings.CSV_ENCODING,
                     sep=ProjectSettings.CSV_DELIMITER)
        except:
            df.to_csv(output_file, index=False, encoding='utf-8-sig', sep='|')
        
        print(f"   ✅ CSV exported to: {output_file}")
        print(f"   📊 Records: {len(df)} mines")
        print(f"   📋 Columns: {len(df.columns)}")
        
        # Show sample data
        print(f"\n   Sample data preview:")
        for col in ['Name', 'Betreiber', 'Aktivitätsstatus', 'Restaurationskosten in $ CAD'][:4]:
            if col in df.columns:
                values = df[col].dropna().head(3).tolist()
                print(f"      {col}: {values}")
        
        return output_file
    
    async def run_complete_demo(self):
        """Führt komplette Demo durch"""
        
        self.print_banner()
        
        if not self.check_prerequisites():
            print("\n❌ Prerequisites not met. Please check installation.")
            return False
        
        try:
            # Demonstrate core components
            mine_data, research_result = self.demonstrate_data_models()
            parsed_mine = self.demonstrate_data_parser()
            
            # Check if we should use real API
            use_real_api = False
            try:
                from config.api_keys import APIConfig
                if (APIConfig.PERPLEXITY_API_KEY and 
                    not APIConfig.PERPLEXITY_API_KEY.startswith('your_')):
                    
                    use_api = input("\n   🔑 API key found. Use real API for demo? (y/n): ").lower().strip()
                    use_real_api = (use_api == 'y')
            except:
                pass
            
            # Web researcher demo
            web_result = await self.demonstrate_web_researcher(use_real_api)
            
            # CSV export demo
            demo_data = [mine_data, parsed_mine]
            if web_result:
                demo_data.append(web_result)
            
            output_file = self.demonstrate_csv_export(demo_data)
            
            # Success summary
            print("\n" + "=" * 70)
            print("🎉 DEMO COMPLETED SUCCESSFULLY!")
            print("=" * 70)
            
            print(f"\n📋 Demo Summary:")
            print(f"   ✅ Data Models: Functional")
            print(f"   ✅ Data Parser: Functional")
            print(f"   ✅ Web Researcher: {'Real API' if use_real_api else 'Demo Mode'}")
            print(f"   ✅ CSV Export: {output_file.name}")
            
            print(f"\n🎯 Next Steps:")
            if use_real_api:
                print(f"   1. 🚀 System is ready for production use!")
                print(f"   2. 🖥️  Start GUI: python main.py")
                print(f"   3. 📊 Process your mine lists")
            else:
                print(f"   1. 🔑 Configure API key in .env file")
                print(f"   2. 🧪 Test real API: python main.py --test-apis")
                print(f"   3. 🖥️  Start GUI: python main.py")
            
            print(f"\n💡 Tips:")
            print(f"   - Use GUI for interactive research")
            print(f"   - Check demos/demo_results_*.csv for sample output")
            print(f"   - Read README.md for comprehensive documentation")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """Hauptfunktion"""
    
    demo = MineExtractorDemo()
    success = await demo.run_complete_demo()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
