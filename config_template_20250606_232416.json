{"timestamp": "2025-06-06T23:24:16.574192", "version": "1.0.0", "api_providers": {"PERPLEXITY_API_KEY": {"name": "Perplexity AI", "required": true, "url": "https://www.perplexity.ai/", "cost": "$20/month", "description": "Primary research API for deep mining data analysis"}, "TAVILY_API_KEY": {"name": "Tavily AI", "required": false, "url": "https://tavily.com/", "cost": "$20/month", "description": "Government and regulatory data specialist"}, "EXA_API_KEY": {"name": "Exa.ai", "required": false, "url": "https://exa.ai/", "cost": "$29/month", "description": "Semantic search for hard-to-find mining data"}, "APIFY_API_KEY": {"name": "Apify", "required": false, "url": "https://apify.com/", "cost": "$49/month", "description": "Government database scraping platform"}, "SCRAPINGBEE_API_KEY": {"name": "ScrapingBee", "required": false, "url": "https://www.scrapingbee.com/", "cost": "$29/month", "description": "JavaScript-heavy sites scraping service"}}, "settings": {"LOG_LEVEL": "INFO", "DEBUG_MODE": false, "MAX_CONCURRENT_REQUESTS": 3, "REQUEST_TIMEOUT": 120}}