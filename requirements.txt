# MineExtractorWeb v1.0 - Essential Dependencies
# Minimale, getestete Dependencies für stabilen Betrieb

# Core HTTP & Async
aiohttp>=3.8.0,<4.0.0
asyncio

# Data Processing
pandas>=1.5.0,<3.0.0
numpy>=1.20.0,<2.0.0

# Environment & Configuration
python-dotenv>=0.19.0,<2.0.0

# HTTP Requests (Backup)
requests>=2.25.0,<3.0.0

# Error Handling & Retry
tenacity>=8.0.0,<9.0.0

# Text Processing
regex>=2020.0.0

# Logging
colorama>=0.4.0,<1.0.0

# Type Support
typing-extensions>=4.0.0

# Date/Time
python-dateutil>=2.8.0,<3.0.0

# Development & Testing (Optional)
pytest>=7.0.0,<8.0.0
pytest-asyncio>=0.20.0,<1.0.0

# GUI (tkinter is built-in with Python)
# No additional GUI dependencies needed

# Optional but recommended
chardet>=4.0.0,<6.0.0
certifi>=2020.0.0
