#!/usr/bin/env python3
"""
Fix-Script für Tavily API und GUI Integration Issues
MineExtractorWeb v1.0 - Behebt API Key Management und Tavily Integration
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_tavily_api():
    """Testet Tavily API mit korrigiertem Client"""
    print("\n🧪 Testing Tavily API...")
    print("-" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        if not APIConfig.TAVILY_API_KEY:
            print("❌ Tavily API Key nicht in .env konfiguriert")
            return False
        
        # Test mit korrigiertem Client
        from src.tavily_client import test_api_key
        
        print(f"🔑 Using API Key: ***{APIConfig.TAVILY_API_KEY[-8:]}")
        print("📡 Testing connection...")
        
        success = await test_api_key(APIConfig.TAVILY_API_KEY)
        
        if success:
            print("✅ Tavily API test successful!")
            return True
        else:
            print("❌ Tavily API test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Tavily API: {e}")
        return False

def test_gui_api_loading():
    """Testet ob GUI API Keys automatisch lädt"""
    print("\n🖥️ Testing GUI API Key Loading...")
    print("-" * 40)
    
    try:
        import tkinter as tk
        from src.gui_integration import WebResearchGUIExtension
        
        # Test root window erstellen
        root = tk.Tk()
        root.withdraw()  # Fenster verstecken
        
        # GUI Extension erstellen
        gui = WebResearchGUIExtension()
        
        # Prüfen ob API Keys geladen wurden
        perplexity_loaded = bool(gui.api_key_perplexity.get().strip())
        tavily_loaded = bool(gui.api_key_tavily.get().strip())
        exa_loaded = bool(gui.api_key_exa.get().strip())
        
        print(f"🔑 Perplexity API Key loaded: {'✅' if perplexity_loaded else '❌'}")
        print(f"🔑 Tavily API Key loaded: {'✅' if tavily_loaded else '❌'}")
        print(f"🔑 Exa API Key loaded: {'✅' if exa_loaded else '❌'}")
        
        # GUI Cleanup
        root.destroy()
        
        return perplexity_loaded or tavily_loaded or exa_loaded
        
    except Exception as e:
        print(f"❌ Error testing GUI: {e}")
        return False

def test_api_configuration():
    """Testet API Konfiguration"""
    print("\n📋 Testing API Configuration...")
    print("-" * 40)
    
    try:
        from config.api_keys import APIConfig
        
        # Zeige API Status
        validation = APIConfig.validate_config()
        
        for api_name, is_configured in validation.items():
            status = "✅" if is_configured else "❌"
            print(f"{status} {api_name.title()}: {'Configured' if is_configured else 'Not configured'}")
        
        # Zeige Summary
        print(f"\n📊 Summary: {sum(validation.values())}/{len(validation)} APIs configured")
        
        if APIConfig.is_ready_for_research():
            print("✅ System ready for research!")
        else:
            print("⚠️ System needs API configuration")
        
        return APIConfig.is_ready_for_research()
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

async def comprehensive_test():
    """Führt umfassende Tests durch"""
    print("🔧 MineExtractorWeb v1.0 - Comprehensive Fix Test")
    print("=" * 60)
    
    test_results = {}
    
    # Test 1: API Configuration
    test_results['config'] = test_api_configuration()
    
    # Test 2: GUI API Loading
    test_results['gui_loading'] = test_gui_api_loading()
    
    # Test 3: Tavily API
    test_results['tavily_api'] = await test_tavily_api()
    
    # Test 4: Perplexity API
    print("\n🧪 Testing Perplexity API...")
    print("-" * 40)
    try:
        from config.api_keys import APIConfig
        if APIConfig.PERPLEXITY_API_KEY:
            from src.perplexity_client import test_api_key
            perplexity_success = await test_api_key(APIConfig.PERPLEXITY_API_KEY)
            test_results['perplexity_api'] = perplexity_success
            print(f"{'✅' if perplexity_success else '❌'} Perplexity API test {'successful' if perplexity_success else 'failed'}")
        else:
            test_results['perplexity_api'] = False
            print("❌ Perplexity API Key not configured")
    except Exception as e:
        test_results['perplexity_api'] = False
        print(f"❌ Perplexity API test error: {e}")
    
    # Ergebnisse zusammenfassen
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    successful_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n🎯 Overall: {successful_tests}/{total_tests} tests passed")
    
    if successful_tests == total_tests:
        print("🎉 All tests passed! System is ready for research.")
        return True
    else:
        print("⚠️ Some tests failed. Please check configuration.")
        return False

def create_fix_summary():
    """Erstellt Fix Summary"""
    print("\n📝 Fix Summary")
    print("=" * 40)
    
    fixes_applied = [
        "✅ GUI lädt API Keys automatisch aus .env Datei",
        "✅ Tavily API Client korrigiert (Request Format)",
        "✅ Bessere Fehlerbehandlung und Debugging",
        "✅ Automatische API Tests beim GUI Start",
        "✅ Verbesserte Status-Anzeige"
    ]
    
    for fix in fixes_applied:
        print(fix)
    
    print("\n🚀 Next Steps:")
    print("1. Starten Sie die GUI: python main.py")
    print("2. API Keys sollten automatisch geladen werden")
    print("3. Tavily API Test sollte erfolgreich sein")
    print("4. System ist bereit für Web Research")

def troubleshooting_guide():
    """Zeigt Troubleshooting Guide"""
    print("\n🛠️ Troubleshooting Guide")
    print("=" * 40)
    
    print("Wenn Tavily API immer noch fehlschlägt:")
    print("1. Überprüfen Sie den API Key in .env Datei")
    print("2. Besuchen Sie https://tavily.com/ und prüfen Sie Account Status")
    print("3. Stellen Sie sicher, dass der API Key gültig und aktiv ist")
    print("4. Prüfen Sie Internet-Verbindung")
    
    print("\nWenn GUI API Keys nicht lädt:")
    print("1. Stellen Sie sicher, dass .env Datei im Projektverzeichnis ist")
    print("2. Überprüfen Sie die API Key Syntax in .env")
    print("3. Starten Sie die Anwendung neu")
    
    print("\nFür weitere Hilfe:")
    print("1. Prüfen Sie logs/mineextractor_web.log")
    print("2. Führen Sie aus: python main.py --test-apis")
    print("3. Führen Sie aus: python main.py --config")

async def main():
    """Hauptfunktion"""
    
    try:
        # Führe umfassende Tests durch
        success = await comprehensive_test()
        
        # Zeige Fix Summary
        create_fix_summary()
        
        # Zeige Troubleshooting Guide wenn nötig
        if not success:
            troubleshooting_guide()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    print(f"\n{'🎉 Fix completed successfully!' if exit_code == 0 else '❌ Fix completed with issues.'}")
    sys.exit(exit_code)
