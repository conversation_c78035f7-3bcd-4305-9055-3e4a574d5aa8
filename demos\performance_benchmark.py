#!/usr/bin/env python3
"""
Performance Benchmark Tool für MineExtractorWeb v1.0
Führt umfassende Performance-Tests durch
"""

import os
import sys
import asyncio
import time
import statistics
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import psutil

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class PerformanceBenchmark:
    """Umfassende Performance-Benchmarks"""
    
    def __init__(self):
        self.benchmark_results = {
            'timestamp': datetime.now().isoformat(),
            'system_info': self._get_system_info(),
            'tests': {},
            'summary': {}
        }
        
        self.test_mines = [
            "Éléonore", "Canadian Malartic", "Raglan", "Casa Berardi", "LaRonde"
        ]
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Sammelt System-Informationen"""
        try:
            return {
                'python_version': sys.version,
                'platform': sys.platform,
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                'memory_available_gb': round(psutil.virtual_memory().available / (1024**3), 2)
            }
        except:
            return {'info': 'System info collection failed'}
    
    def print_banner(self):
        """Zeigt Benchmark-Banner"""
        print("=" * 70)
        print("🏃 MineExtractorWeb v1.0 - Performance Benchmark")
        print("   Comprehensive Performance Testing & Analysis")
        print("=" * 70)
        
        # Show system info
        sys_info = self.benchmark_results['system_info']
        if 'cpu_count' in sys_info:
            print(f"\n💻 System Info:")
            print(f"   CPU Cores: {sys_info['cpu_count']}")
            print(f"   Memory: {sys_info['memory_available_gb']:.1f}GB available / {sys_info['memory_total_gb']:.1f}GB total")
            print(f"   Platform: {sys_info['platform']}")
    
    async def benchmark_single_mine_research(self, iterations: int = 3) -> Dict[str, Any]:
        """Benchmarkt einzelne Mine-Recherche"""
        print(f"\n🔍 Benchmarking Single Mine Research ({iterations} iterations)...")
        
        from web_researcher import WebMiningResearcher
        
        # Check if real API is available
        use_real_api = False
        try:
            from config.api_keys import APIConfig
            if (APIConfig.PERPLEXITY_API_KEY and 
                not APIConfig.PERPLEXITY_API_KEY.startswith('your_')):
                use_real_api = True
                config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
                print("   Using real API for benchmark")
            else:
                config = {'perplexity_key': 'demo-key'}
                print("   Using demo mode for benchmark")
        except:
            config = {'perplexity_key': 'demo-key'}
            print("   Using demo mode for benchmark")
        
        researcher = WebMiningResearcher(config)
        
        # Benchmark data
        times = []
        success_count = 0
        confidence_scores = []
        completion_rates = []
        memory_usage = []
        
        for i in range(iterations):
            mine_name = self.test_mines[i % len(self.test_mines)]
            
            # Memory before
            memory_before = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            start_time = time.time()
            
            try:
                if use_real_api:
                    result = await researcher.research_mine_comprehensive(mine_name)
                else:
                    # Simulate processing for consistent timing
                    await asyncio.sleep(1.0 + (hash(mine_name) % 100) / 1000)
                    
                    from mine_data_models import MineDataFields, MineResearchResult
                    mine_data = MineDataFields(name=mine_name)
                    result = MineResearchResult(mine_data=mine_data)
                    result.confidence_score = 0.7 + (hash(mine_name) % 30) / 100
                    result.data_completeness = 0.6 + (hash(mine_name) % 40) / 100
                
                duration = time.time() - start_time
                times.append(duration)
                
                if result.validation_status != 'invalid':
                    success_count += 1
                    confidence_scores.append(result.confidence_score)
                    completion_rates.append(result.data_completeness)
                
                # Memory after
                memory_after = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                memory_usage.append(memory_after - memory_before)
                
                print(f"   Iteration {i+1}: {mine_name} - {duration:.2f}s")
                
            except Exception as e:
                duration = time.time() - start_time
                times.append(duration)
                print(f"   Iteration {i+1}: {mine_name} - FAILED ({duration:.2f}s)")
        
        # Calculate statistics
        results = {
            'iterations': iterations,
            'success_rate': success_count / iterations,
            'times': {
                'min': min(times),
                'max': max(times),
                'mean': statistics.mean(times),
                'median': statistics.median(times),
                'stdev': statistics.stdev(times) if len(times) > 1 else 0
            },
            'confidence': {
                'mean': statistics.mean(confidence_scores) if confidence_scores else 0,
                'min': min(confidence_scores) if confidence_scores else 0,
                'max': max(confidence_scores) if confidence_scores else 0
            },
            'completion': {
                'mean': statistics.mean(completion_rates) if completion_rates else 0,
                'min': min(completion_rates) if completion_rates else 0,
                'max': max(completion_rates) if completion_rates else 0
            },
            'memory': {
                'mean_mb': statistics.mean(memory_usage) if memory_usage else 0,
                'max_mb': max(memory_usage) if memory_usage else 0
            },
            'throughput_per_hour': 3600 / statistics.mean(times) if times else 0
        }
        
        print(f"\n   📊 Results:")
        print(f"      Success Rate: {results['success_rate']:.1%}")
        print(f"      Avg Time: {results['times']['mean']:.2f}s (±{results['times']['stdev']:.2f}s)")
        print(f"      Throughput: {results['throughput_per_hour']:.0f} mines/hour")
        print(f"      Avg Confidence: {results['confidence']['mean']:.1%}")
        print(f"      Avg Completion: {results['completion']['mean']:.1%}")
        
        self.benchmark_results['tests']['single_mine'] = results
        return results
    
    async def benchmark_batch_processing(self, batch_sizes: List[int] = [3, 5, 10]) -> Dict[str, Any]:
        """Benchmarkt Batch-Verarbeitung"""
        print(f"\n📦 Benchmarking Batch Processing...")
        
        from web_researcher import WebMiningResearcher
        
        # Setup
        try:
            from config.api_keys import APIConfig
            if (APIConfig.PERPLEXITY_API_KEY and 
                not APIConfig.PERPLEXITY_API_KEY.startswith('your_')):
                config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
                use_real_api = True
            else:
                config = {'perplexity_key': 'demo-key'}
                use_real_api = False
        except:
            config = {'perplexity_key': 'demo-key'}
            use_real_api = False
        
        batch_results = {}
        
        for batch_size in batch_sizes:
            print(f"\n   Testing batch size: {batch_size}")
            
            researcher = WebMiningResearcher(config)
            researcher.request_delay = 0.5  # Reduced for benchmarking
            
            # Select mines for this batch
            mine_names = self.test_mines[:batch_size]
            
            # Memory before
            memory_before = psutil.Process().memory_info().rss / 1024 / 1024
            
            start_time = time.time()
            
            try:
                if use_real_api:
                    batch_result = await researcher.research_mine_list(mine_names)
                    success_count = batch_result.success_count
                    total_count = len(batch_result.results)
                else:
                    # Simulate batch processing
                    for mine_name in mine_names:
                        await asyncio.sleep(0.3)  # Simulate processing
                    success_count = batch_size
                    total_count = batch_size
                
                duration = time.time() - start_time
                
                # Memory after
                memory_after = psutil.Process().memory_info().rss / 1024 / 1024
                
                batch_results[batch_size] = {
                    'duration': duration,
                    'success_rate': success_count / total_count,
                    'avg_per_mine': duration / batch_size,
                    'throughput_per_hour': batch_size / duration * 3600,
                    'memory_usage_mb': memory_after - memory_before,
                    'efficiency': batch_size / duration  # mines per second
                }
                
                print(f"      Duration: {duration:.1f}s")
                print(f"      Success: {success_count}/{total_count}")
                print(f"      Avg/mine: {duration/batch_size:.1f}s")
                print(f"      Throughput: {batch_size/duration*3600:.0f} mines/hour")
                
            except Exception as e:
                print(f"      FAILED: {e}")
                batch_results[batch_size] = {'error': str(e)}
        
        # Analyze scaling efficiency
        if len(batch_results) > 1:
            print(f"\n   📈 Scaling Analysis:")
            
            base_size = min(batch_sizes)
            base_efficiency = batch_results[base_size]['efficiency']
            
            for size in batch_sizes:
                if size in batch_results and 'efficiency' in batch_results[size]:
                    efficiency = batch_results[size]['efficiency']
                    scaling_factor = efficiency / base_efficiency
                    print(f"      {size} mines: {scaling_factor:.2f}x efficiency vs {base_size}")
        
        self.benchmark_results['tests']['batch_processing'] = batch_results
        return batch_results
    
    async def benchmark_data_parsing(self, iterations: int = 100) -> Dict[str, Any]:
        """Benchmarkt Data Parsing Performance"""
        print(f"\n🔧 Benchmarking Data Parsing ({iterations} iterations)...")
        
        from data_parser import MiningDataParser, quick_parse
        
        # Sample content for parsing
        sample_content = """
        Éléonore mine is operated by Newmont Corporation in Quebec, Canada.
        The mine is currently active and operational since 2014.
        Environmental restoration costs are estimated at $45.2 million CAD as of 2023.
        This is an underground gold mine located in the James Bay region.
        The mine covers approximately 125 hectares and produces around 400,000 ounces of gold annually.
        Coordinates: approximately 53.4°N, 76.8°W
        """
        
        parser = MiningDataParser()
        
        # Benchmark quick_parse
        quick_parse_times = []
        for i in range(iterations):
            start_time = time.time()
            result = quick_parse(sample_content, f"Test Mine {i}")
            duration = time.time() - start_time
            quick_parse_times.append(duration)
        
        # Benchmark individual extraction methods
        extraction_times = {}
        
        methods_to_test = [
            ('operator', parser._extract_operator),
            ('status', parser._extract_status),
            ('costs', parser._extract_restoration_costs),
            ('commodity', parser._extract_commodity),
            ('coordinates', parser._extract_coordinates)
        ]
        
        for method_name, method in methods_to_test:
            times = []
            for i in range(iterations):
                start_time = time.time()
                method(sample_content)
                duration = time.time() - start_time
                times.append(duration)
            
            extraction_times[method_name] = {
                'mean': statistics.mean(times),
                'min': min(times),
                'max': max(times)
            }
        
        results = {
            'iterations': iterations,
            'quick_parse': {
                'mean': statistics.mean(quick_parse_times),
                'min': min(quick_parse_times),
                'max': max(quick_parse_times),
                'stdev': statistics.stdev(quick_parse_times)
            },
            'extraction_methods': extraction_times,
            'parsing_rate': iterations / sum(quick_parse_times)  # parses per second
        }
        
        print(f"   📊 Results:")
        print(f"      Quick Parse: {results['quick_parse']['mean']*1000:.2f}ms avg")
        print(f"      Parsing Rate: {results['parsing_rate']:.0f} parses/second")
        print(f"      Fastest Method: {min(extraction_times.keys(), key=lambda k: extraction_times[k]['mean'])}")
        
        self.benchmark_results['tests']['data_parsing'] = results
        return results
    
    async def benchmark_memory_usage(self) -> Dict[str, Any]:
        """Benchmarkt Memory Usage"""
        print(f"\n💾 Benchmarking Memory Usage...")
        
        from web_researcher import WebMiningResearcher
        from mine_data_models import MineDataFields, MineResearchResult
        
        # Initial memory
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Test object creation overhead
        print("   Testing object creation...")
        
        # Create many mine data objects
        mine_objects = []
        for i in range(1000):
            mine = MineDataFields(name=f"Test Mine {i}")
            mine_objects.append(mine)
        
        after_mines = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Create many research results
        research_objects = []
        for i in range(100):
            result = MineResearchResult(mine_data=mine_objects[i])
            research_objects.append(result)
        
        after_results = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Test researcher memory usage
        config = {'perplexity_key': 'demo-key'}
        researcher = WebMiningResearcher(config)
        
        after_researcher = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Memory analysis
        results = {
            'initial_mb': initial_memory,
            'after_1000_mines_mb': after_mines,
            'after_100_results_mb': after_results,
            'after_researcher_mb': after_researcher,
            'memory_per_mine_kb': (after_mines - initial_memory) * 1024 / 1000,
            'memory_per_result_kb': (after_results - after_mines) * 1024 / 100,
            'researcher_overhead_mb': after_researcher - after_results
        }
        
        print(f"   📊 Results:")
        print(f"      Initial Memory: {initial_memory:.1f}MB")
        print(f"      Per Mine Object: {results['memory_per_mine_kb']:.1f}KB")
        print(f"      Per Result Object: {results['memory_per_result_kb']:.1f}KB")
        print(f"      Researcher Overhead: {results['researcher_overhead_mb']:.1f}MB")
        
        # Cleanup
        del mine_objects, research_objects, researcher
        
        self.benchmark_results['tests']['memory_usage'] = results
        return results
    
    def analyze_bottlenecks(self) -> Dict[str, str]:
        """Analysiert Performance-Bottlenecks"""
        print(f"\n🔍 Analyzing Performance Bottlenecks...")
        
        bottlenecks = {}
        tests = self.benchmark_results['tests']
        
        # Single mine performance
        if 'single_mine' in tests:
            single_mine = tests['single_mine']
            avg_time = single_mine['times']['mean']
            
            if avg_time > 60:
                bottlenecks['api_response'] = f"Slow API responses ({avg_time:.1f}s avg)"
            elif avg_time > 30:
                bottlenecks['api_response'] = f"Moderate API latency ({avg_time:.1f}s avg)"
            
            if single_mine['success_rate'] < 0.8:
                bottlenecks['api_reliability'] = f"Low success rate ({single_mine['success_rate']:.1%})"
        
        # Memory usage
        if 'memory_usage' in tests:
            memory = tests['memory_usage']
            if memory['memory_per_mine_kb'] > 10:
                bottlenecks['memory_efficiency'] = f"High memory per mine ({memory['memory_per_mine_kb']:.1f}KB)"
        
        # Parsing performance
        if 'data_parsing' in tests:
            parsing = tests['data_parsing']
            if parsing['quick_parse']['mean'] > 0.01:  # 10ms
                bottlenecks['parsing_speed'] = f"Slow parsing ({parsing['quick_parse']['mean']*1000:.1f}ms)"
        
        # Batch scaling
        if 'batch_processing' in tests:
            batch = tests['batch_processing']
            if len(batch) > 1:
                # Check if scaling is sublinear
                sizes = sorted(batch.keys())
                if len(sizes) >= 2:
                    small_eff = batch[sizes[0]].get('efficiency', 0)
                    large_eff = batch[sizes[-1]].get('efficiency', 0)
                    
                    if large_eff < small_eff * 0.8:  # Less than 80% efficiency retained
                        bottlenecks['batch_scaling'] = "Poor batch scaling efficiency"
        
        if bottlenecks:
            print("   🚨 Identified Bottlenecks:")
            for bottleneck, description in bottlenecks.items():
                print(f"      • {description}")
        else:
            print("   ✅ No significant bottlenecks identified")
        
        return bottlenecks
    
    def generate_recommendations(self, bottlenecks: Dict[str, str]) -> List[str]:
        """Generiert Performance-Empfehlungen"""
        recommendations = []
        
        if 'api_response' in bottlenecks:
            recommendations.append("🔧 Optimize API requests: Reduce timeout, use caching")
            recommendations.append("📡 Consider using multiple API providers for redundancy")
        
        if 'api_reliability' in bottlenecks:
            recommendations.append("🔄 Implement better retry logic and error handling")
            recommendations.append("🎯 Use multiple APIs for cross-validation")
        
        if 'memory_efficiency' in bottlenecks:
            recommendations.append("💾 Optimize data models for memory efficiency")
            recommendations.append("🗑️ Implement garbage collection between batches")
        
        if 'parsing_speed' in bottlenecks:
            recommendations.append("⚡ Optimize regex patterns for faster parsing")
            recommendations.append("🔀 Consider parallel parsing for large responses")
        
        if 'batch_scaling' in bottlenecks:
            recommendations.append("📊 Optimize batch sizes for better throughput")
            recommendations.append("⚖️ Implement adaptive batching based on performance")
        
        # General recommendations
        if not bottlenecks:
            recommendations.append("✅ Performance is good! Consider scaling up batch sizes")
            recommendations.append("🚀 Ready for production workloads")
        
        return recommendations
    
    def save_benchmark_report(self):
        """Speichert Benchmark-Report"""
        
        # Analyze bottlenecks
        bottlenecks = self.analyze_bottlenecks()
        recommendations = self.generate_recommendations(bottlenecks)
        
        self.benchmark_results['analysis'] = {
            'bottlenecks': bottlenecks,
            'recommendations': recommendations
        }
        
        # Calculate overall performance score
        score = self._calculate_performance_score()
        self.benchmark_results['performance_score'] = score
        
        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = project_root / "demos" / f"benchmark_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.benchmark_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Benchmark report saved: {report_file.name}")
        return report_file
    
    def _calculate_performance_score(self) -> Dict[str, Any]:
        """Berechnet Performance-Score"""
        
        tests = self.benchmark_results['tests']
        scores = {}
        
        # Single mine performance (0-100)
        if 'single_mine' in tests:
            single = tests['single_mine']
            
            # Time score (faster = better)
            time_score = max(0, 100 - single['times']['mean'] * 2)  # 50s = 0 points
            
            # Success rate score
            success_score = single['success_rate'] * 100
            
            # Confidence score
            confidence_score = single['confidence']['mean'] * 100
            
            scores['single_mine'] = (time_score + success_score + confidence_score) / 3
        
        # Overall score
        overall_score = statistics.mean(scores.values()) if scores else 0
        
        return {
            'overall': overall_score,
            'components': scores,
            'grade': self._score_to_grade(overall_score)
        }
    
    def _score_to_grade(self, score: float) -> str:
        """Konvertiert Score zu Grade"""
        if score >= 90:
            return "A+ (Excellent)"
        elif score >= 80:
            return "A (Very Good)"
        elif score >= 70:
            return "B (Good)"
        elif score >= 60:
            return "C (Acceptable)"
        else:
            return "D (Needs Improvement)"
    
    def print_summary(self):
        """Druckt Benchmark-Zusammenfassung"""
        
        print("\n" + "=" * 70)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 70)
        
        # Performance score
        if 'performance_score' in self.benchmark_results:
            score_info = self.benchmark_results['performance_score']
            print(f"\n🎯 Overall Performance Score: {score_info['overall']:.1f}/100 ({score_info['grade']})")
        
        # Key metrics
        tests = self.benchmark_results['tests']
        
        if 'single_mine' in tests:
            single = tests['single_mine']
            print(f"\n🔍 Single Mine Research:")
            print(f"   Average Time: {single['times']['mean']:.1f}s")
            print(f"   Success Rate: {single['success_rate']:.1%}")
            print(f"   Throughput: {single['throughput_per_hour']:.0f} mines/hour")
        
        if 'batch_processing' in tests:
            batch = tests['batch_processing']
            best_size = max(batch.keys(), key=lambda k: batch[k].get('throughput_per_hour', 0))
            print(f"\n📦 Best Batch Performance:")
            print(f"   Optimal Batch Size: {best_size} mines")
            print(f"   Throughput: {batch[best_size]['throughput_per_hour']:.0f} mines/hour")
        
        # Recommendations
        if 'analysis' in self.benchmark_results:
            analysis = self.benchmark_results['analysis']
            
            if analysis['bottlenecks']:
                print(f"\n🚨 Performance Issues:")
                for issue in analysis['bottlenecks'].values():
                    print(f"   • {issue}")
            
            if analysis['recommendations']:
                print(f"\n💡 Recommendations:")
                for rec in analysis['recommendations'][:3]:  # Top 3
                    print(f"   • {rec}")
        
        print("\n" + "=" * 70)

async def main():
    """Hauptfunktion"""
    
    benchmark = PerformanceBenchmark()
    benchmark.print_banner()
    
    print("\n🏃 Starting comprehensive performance benchmark...")
    
    try:
        # Run all benchmarks
        await benchmark.benchmark_single_mine_research(iterations=3)
        await benchmark.benchmark_batch_processing([3, 5])
        await benchmark.benchmark_data_parsing(iterations=50)
        await benchmark.benchmark_memory_usage()
        
        # Analyze and save
        benchmark.save_benchmark_report()
        benchmark.print_summary()
        
        print(f"\n✅ Benchmark completed successfully!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
