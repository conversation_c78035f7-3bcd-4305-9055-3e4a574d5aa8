#!/usr/bin/env python3
"""
Setup Script für MineExtractorWeb v1.0
Automatische Installation und Konfiguration des kompletten Systems
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

class MineExtractorSetup:
    """Automatisches Setup für MineExtractorWeb v1.0"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.requirements_file = self.project_root / "requirements.txt"
        self.env_template = self.project_root / ".env.template"
        self.env_file = self.project_root / ".env"
        
        self.setup_log = []
        self.errors = []
        
    def log(self, message: str, level: str = "info"):
        """Logging für Setup-Prozess"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        if level == "error":
            self.errors.append(log_entry)
            print(f"❌ {message}")
        elif level == "warning":
            print(f"⚠️  {message}")
        elif level == "success":
            print(f"✅ {message}")
        else:
            print(f"ℹ️  {message}")
        
        self.setup_log.append(log_entry)
    
    def print_banner(self):
        """Setup Banner"""
        print("=" * 70)
        print("🚀 MineExtractorWeb v1.0 - Automatic Setup")
        print("   Complete system installation and configuration")
        print("=" * 70)
    
    def check_python_version(self) -> bool:
        """Prüft Python Version"""
        self.log("Checking Python version...")
        
        version_info = sys.version_info
        
        if version_info.major < 3 or (version_info.major == 3 and version_info.minor < 8):
            self.log(f"Python {version_info.major}.{version_info.minor} detected - minimum Python 3.8 required", "error")
            return False
        
        self.log(f"Python {version_info.major}.{version_info.minor}.{version_info.micro} - OK", "success")
        return True
    
    def check_pip_availability(self) -> bool:
        """Prüft pip Verfügbarkeit"""
        self.log("Checking pip availability...")
        
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            self.log("pip is available", "success")
            return True
        except subprocess.CalledProcessError:
            self.log("pip is not available", "error")
            return False
    
    def install_dependencies(self) -> bool:
        """Installiert Python Dependencies"""
        self.log("Installing Python dependencies...")
        
        # Try fix_dependencies.py first
        fix_script = self.project_root / "fix_dependencies.py"
        if fix_script.exists():
            try:
                self.log("Using enhanced dependency installer...")
                result = subprocess.run([sys.executable, str(fix_script)], 
                                      check=True, capture_output=True, text=True)
                self.log("Dependencies installed successfully", "success")
                return True
            except subprocess.CalledProcessError as e:
                self.log(f"Enhanced installer failed, trying fallback: {e}", "warning")
        
        # Fallback to requirements.txt
        if not self.requirements_file.exists():
            self.log("requirements.txt not found", "error")
            return False
        
        try:
            # Upgrade pip first
            self.log("Upgrading pip...")
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # Install requirements
            self.log("Installing packages from requirements.txt...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)], 
                         check=True, capture_output=True)
            
            self.log("Dependencies installed successfully", "success")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install dependencies: {e}", "error")
            # Try essential packages only
            return self._install_essential_packages()
    
    def _install_essential_packages(self) -> bool:
        """Installiert nur die essentiellsten Pakete"""
        self.log("Trying to install essential packages only...", "warning")
        
        essential = ["pandas", "aiohttp", "python-dotenv", "requests"]
        success_count = 0
        
        for package in essential:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                self.log(f"Essential package installed: {package}")
                success_count += 1
            except subprocess.CalledProcessError:
                self.log(f"Failed to install essential package: {package}", "warning")
        
        if success_count >= 3:
            self.log(f"Essential packages installed ({success_count}/{len(essential)})", "success")
            return True
        else:
            self.log(f"Too few essential packages installed ({success_count}/{len(essential)})", "error")
            return False
    
    def create_project_directories(self) -> bool:
        """Erstellt Projektverzeichnisse"""
        self.log("Creating project directories...")
        
        directories = [
            "logs",
            "output", 
            "cache",
            "temp"
        ]
        
        success = True
        for directory in directories:
            dir_path = self.project_root / directory
            try:
                dir_path.mkdir(exist_ok=True)
                self.log(f"Directory created: {directory}")
            except Exception as e:
                self.log(f"Failed to create directory {directory}: {e}", "error")
                success = False
        
        if success:
            self.log("All directories created successfully", "success")
        
        return success
    
    def setup_environment_file(self) -> bool:
        """Erstellt .env Datei aus Template"""
        self.log("Setting up environment file...")
        
        if self.env_file.exists():
            backup_choice = input("  .env file exists. Create backup? (y/n): ").lower().strip()
            if backup_choice == 'y':
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = self.project_root / f".env.backup_{timestamp}"
                try:
                    shutil.copy2(self.env_file, backup_file)
                    self.log(f"Backup created: {backup_file.name}")
                except Exception as e:
                    self.log(f"Backup failed: {e}", "warning")
        
        # Create .env from template
        if self.env_template.exists():
            try:
                shutil.copy2(self.env_template, self.env_file)
                self.log("Environment file created from template", "success")
                return True
            except Exception as e:
                self.log(f"Failed to create .env file: {e}", "error")
                return False
        else:
            # Create basic .env file
            try:
                env_content = """# MineExtractorWeb v1.0 - API Configuration
# Configure your API keys below

# Perplexity AI (Primary - Required)
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Tavily AI (Optional - Phase 2)
TAVILY_API_KEY=your_tavily_api_key_here

# Exa.ai (Optional - Phase 2)
EXA_API_KEY=your_exa_api_key_here

# System Settings
LOG_LEVEL=INFO
DEBUG_MODE=False
MAX_CONCURRENT_REQUESTS=3
REQUEST_TIMEOUT=120
"""
                
                with open(self.env_file, 'w', encoding='utf-8') as f:
                    f.write(env_content)
                
                self.log("Basic environment file created", "success")
                return True
                
            except Exception as e:
                self.log(f"Failed to create .env file: {e}", "error")
                return False
    
    def test_core_imports(self) -> bool:
        """Testet kritische Imports"""
        self.log("Testing core module imports...")
        
        # Add src to path for testing
        sys.path.insert(0, str(self.project_root / "src"))
        
        critical_imports = [
            ("mine_data_models", "MineDataFields"),
            ("data_parser", "MiningDataParser"),
            ("web_researcher", "WebMiningResearcher"),
            ("perplexity_client", "PerplexityClient"),
            ("gui_integration", "WebResearchGUIExtension")
        ]
        
        config_imports = [
            ("config.api_keys", "APIConfig"),
            ("config.settings", "ProjectSettings"),
            ("config.mining_prompts", "PromptBuilder")
        ]
        
        success = True
        
        # Test core imports
        for module_name, class_name in critical_imports:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                self.log(f"Core import OK: {module_name}.{class_name}")
            except ImportError as e:
                self.log(f"Core import failed: {module_name}.{class_name} - {e}", "error")
                success = False
            except AttributeError as e:
                self.log(f"Core class missing: {module_name}.{class_name} - {e}", "error")
                success = False
        
        # Test config imports
        for module_name, class_name in config_imports:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                self.log(f"Config import OK: {module_name}.{class_name}")
            except ImportError as e:
                self.log(f"Config import failed: {module_name}.{class_name} - {e}", "error")
                success = False
            except AttributeError as e:
                self.log(f"Config class missing: {module_name}.{class_name} - {e}", "error")
                success = False
        
        if success:
            self.log("All core imports successful", "success")
        else:
            self.log("Some imports failed - check error messages above", "error")
        
        return success
    
    def test_basic_functionality(self) -> bool:
        """Testet Basis-Funktionalität"""
        self.log("Testing basic functionality...")
        
        try:
            # Test data models
            from mine_data_models import MineDataFields
            mine = MineDataFields(name="Test Mine", betreiber="Test Company")
            assert mine.name == "Test Mine"
            self.log("Data models test passed")
            
            # Test data parser
            from data_parser import quick_parse
            parsed = quick_parse("Test mine operated by Test Company", "Test Mine")
            assert parsed.name == "Test Mine"
            self.log("Data parser test passed")
            
            # Test web researcher initialization
            from web_researcher import WebMiningResearcher
            researcher = WebMiningResearcher({'perplexity_key': 'test-key'})
            assert len(researcher.api_clients) > 0
            self.log("Web researcher test passed")
            
            # Test configuration
            from config.settings import ProjectSettings
            info = ProjectSettings.get_project_info()
            assert info['name'] == "MineExtractorWeb"
            self.log("Configuration test passed")
            
            self.log("All functionality tests passed", "success")
            return True
            
        except Exception as e:
            self.log(f"Functionality test failed: {e}", "error")
            return False
    
    def create_desktop_shortcut(self) -> bool:
        """Erstellt Desktop-Verknüpfung (Windows)"""
        if sys.platform != "win32":
            self.log("Desktop shortcut creation only supported on Windows")
            return True
        
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            shortcut_path = os.path.join(desktop, "MineExtractorWeb.lnk")
            target = sys.executable
            arguments = str(self.project_root / "main.py")
            icon = str(self.project_root / "main.py")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = target
            shortcut.Arguments = arguments
            shortcut.WorkingDirectory = str(self.project_root)
            shortcut.IconLocation = icon
            shortcut.save()
            
            self.log("Desktop shortcut created", "success")
            return True
            
        except ImportError:
            self.log("Desktop shortcut creation requires: pip install winshell pywin32", "warning")
            return True
        except Exception as e:
            self.log(f"Desktop shortcut creation failed: {e}", "warning")
            return True
    
    def run_interactive_api_setup(self) -> bool:
        """Führt interaktive API-Konfiguration durch"""
        print("\n🔑 API Configuration Setup")
        print("=" * 40)
        
        configure_apis = input("Configure API keys now? (y/n): ").lower().strip()
        
        if configure_apis == 'y':
            try:
                # Import and run API key manager
                sys.path.insert(0, str(self.project_root / "tools"))
                from api_key_manager import APIKeyManager
                
                manager = APIKeyManager()
                print("\nStarting interactive API configuration...")
                manager.interactive_setup()
                
                self.log("API configuration completed", "success")
                return True
                
            except Exception as e:
                self.log(f"API configuration failed: {e}", "error")
                return False
        else:
            self.log("API configuration skipped - configure later using tools/api_key_manager.py")
            return True
    
    def generate_setup_report(self) -> Path:
        """Generiert Setup-Bericht"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.project_root / f"setup_report_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("MineExtractorWeb v1.0 - Setup Report\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Setup Date: {datetime.now().isoformat()}\n")
                f.write(f"Python Version: {sys.version}\n")
                f.write(f"Project Root: {self.project_root}\n\n")
                
                f.write("Setup Log:\n")
                f.write("-" * 20 + "\n")
                for entry in self.setup_log:
                    f.write(entry + "\n")
                
                if self.errors:
                    f.write("\nErrors:\n")
                    f.write("-" * 20 + "\n")
                    for error in self.errors:
                        f.write(error + "\n")
                
                f.write(f"\nSetup completed with {len(self.errors)} errors\n")
            
            self.log(f"Setup report saved: {report_file.name}")
            return report_file
            
        except Exception as e:
            self.log(f"Failed to generate setup report: {e}", "error")
            return None
    
    def show_next_steps(self):
        """Zeigt nächste Schritte"""
        print("\n" + "=" * 70)
        print("🎯 SETUP COMPLETED - Next Steps")
        print("=" * 70)
        
        if len(self.errors) == 0:
            print("\n✅ Setup completed successfully!")
            
            print("\n🚀 Quick Start Guide:")
            print("   1. Configure API keys:")
            print(f"      python {self.project_root}/tools/api_key_manager.py")
            print("   ")
            print("   2. Test the system:")
            print(f"      python {self.project_root}/main.py --test-apis")
            print("   ")
            print("   3. Start the GUI:")
            print(f"      python {self.project_root}/main.py")
            print("   ")
            print("   4. Run a demo:")
            print(f"      python {self.project_root}/demos/quick_start_demo.py")
            
            print("\n📚 Documentation:")
            print(f"   • README.md - Complete user guide")
            print(f"   • Doku/ - Technical documentation") 
            print(f"   • demos/ - Example scripts")
            
            print("\n💡 Tips:")
            print("   • Start with Perplexity AI API key (required)")
            print("   • Test with small mine lists first")
            print("   • Check logs/ directory for detailed logs")
            
        else:
            print(f"\n⚠️  Setup completed with {len(self.errors)} errors")
            print("\n❌ Issues to resolve:")
            for error in self.errors[-5:]:  # Show last 5 errors
                print(f"   {error}")
            
            print("\n🔧 Troubleshooting:")
            print("   • Check Python version (3.8+ required)")
            print("   • Install missing dependencies manually")
            print("   • Check file permissions")
            print("   • Run setup as administrator if needed")
        
        print("\n🆘 Support:")
        print("   • Check setup_report_*.txt for detailed logs")
        print("   • Review error messages above")
        print("   • Consult documentation in Doku/ folder")
        
        print("\n" + "=" * 70)
    
    def run_complete_setup(self) -> bool:
        """Führt komplettes Setup durch"""
        self.print_banner()
        
        success = True
        
        # Step 1: Python version check
        if not self.check_python_version():
            return False
        
        # Step 2: pip availability
        if not self.check_pip_availability():
            return False
        
        # Step 3: Install dependencies
        if not self.install_dependencies():
            success = False
        
        # Step 4: Create directories
        if not self.create_project_directories():
            success = False
        
        # Step 5: Setup environment
        if not self.setup_environment_file():
            success = False
        
        # Step 6: Test imports
        if not self.test_core_imports():
            success = False
        
        # Step 7: Test functionality
        if not self.test_basic_functionality():
            success = False
        
        # Step 8: Desktop shortcut (optional)
        self.create_desktop_shortcut()
        
        # Step 9: API setup (optional)
        self.run_interactive_api_setup()
        
        # Step 10: Generate report
        self.generate_setup_report()
        
        # Step 11: Show next steps
        self.show_next_steps()
        
        return success and len(self.errors) == 0

def main():
    """Hauptfunktion"""
    
    try:
        setup = MineExtractorSetup()
        success = setup.run_complete_setup()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Setup failed with unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
