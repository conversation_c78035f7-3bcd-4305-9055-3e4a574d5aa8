#!/usr/bin/env python3
"""
Batch Processing Demo für MineExtractorWeb v1.0
Demonstriert Verarbeitung großer Mine-Listen
"""

import os
import sys
import asyncio
import time
import csv
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

class BatchProcessingDemo:
    """Demonstriert Batch-Verarbeitung von Mine-Listen"""
    
    def __init__(self):
        self.quebec_mines = [
            # Gold Mines
            "Éléonore", "Canadian Malartic", "Casa Berardi", "LaRonde", 
            "Goldex", "Beaufor", "Sigma", "Kiena", "Lamaque",
            
            # Iron Ore Mines
            "Mont Wright", "Fire Lake", "Bloom Lake",
            
            # Copper/Nickel Mines
            "Raglan", "Lac Tio",
            
            # Other Mines
            "Troilus", "Niobec", "Dumagami", "Windfall Lake",
            
            # Planned/Development
            "Otish Mountains", "Strange Lake", "Grevet"
        ]
        
        self.demo_batch_sizes = [3, 5, 10]
        self.performance_metrics = {
            'batch_times': [],
            'success_rates': [],
            'avg_per_mine': [],
            'total_processed': 0
        }
    
    def print_banner(self):
        """Zeigt Demo-Banner"""
        print("=" * 70)
        print("📊 MineExtractorWeb v1.0 - Batch Processing Demo")
        print("   Demonstriert effiziente Verarbeitung großer Mine-Listen")
        print("=" * 70)
    
    def show_available_mines(self):
        """Zeigt verfügbare Minen für Demo"""
        print(f"\n📋 Available Quebec Mines ({len(self.quebec_mines)} total):")
        
        categories = {
            "Gold Mines": self.quebec_mines[:9],
            "Iron Ore": self.quebec_mines[9:12], 
            "Copper/Nickel": self.quebec_mines[12:14],
            "Other Active": self.quebec_mines[14:17],
            "Development": self.quebec_mines[17:]
        }
        
        for category, mines in categories.items():
            print(f"\n   {category}:")
            for mine in mines:
                print(f"      • {mine}")
    
    def create_demo_batch_files(self):
        """Erstellt Demo-Batch-Dateien"""
        print(f"\n📁 Creating demo batch files...")
        
        batch_files = {}
        
        for size in self.demo_batch_sizes:
            filename = f"demo_batch_{size}_mines.txt"
            filepath = project_root / "demos" / filename
            
            # Select mines for this batch size
            selected_mines = self.quebec_mines[:size]
            
            # Write to file
            with open(filepath, 'w', encoding='utf-8') as f:
                for mine in selected_mines:
                    f.write(f"{mine}\n")
            
            batch_files[size] = filepath
            print(f"   ✅ {filename} - {size} mines")
        
        # Create comprehensive batch
        comprehensive_file = project_root / "demos" / "comprehensive_quebec_mines.txt"
        with open(comprehensive_file, 'w', encoding='utf-8') as f:
            for mine in self.quebec_mines:
                f.write(f"{mine}\n")
        
        batch_files['comprehensive'] = comprehensive_file
        print(f"   ✅ comprehensive_quebec_mines.txt - {len(self.quebec_mines)} mines")
        
        return batch_files
    
    async def simulate_batch_processing(self, mine_names: List[str], 
                                      batch_size: int = 5,
                                      use_real_api: bool = False) -> Dict:
        """Simuliert Batch-Verarbeitung"""
        
        print(f"\n🚀 Processing batch of {len(mine_names)} mines (batch size: {batch_size})")
        
        from web_researcher import WebMiningResearcher
        from mine_data_models import MineDataFields, MineResearchResult, BatchResearchResult
        
        # Initialize researcher
        if use_real_api:
            try:
                from config.api_keys import APIConfig
                config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
                print("   🔑 Using real API")
            except:
                config = {'perplexity_key': 'demo-key'}
                use_real_api = False
                print("   🎭 Falling back to demo mode")
        else:
            config = {'perplexity_key': 'demo-key'}
            print("   🎭 Demo mode (simulated processing)")
        
        researcher = WebMiningResearcher(config)
        researcher.request_delay = 1.0  # Reduced delay for demo
        
        start_time = time.time()
        results = []
        
        # Process in batches
        for i in range(0, len(mine_names), batch_size):
            batch = mine_names[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(mine_names) + batch_size - 1) // batch_size
            
            print(f"   📦 Batch {batch_num}/{total_batches}: {', '.join(batch)}")
            
            batch_start = time.time()
            
            for mine_name in batch:
                if use_real_api:
                    try:
                        # Real API processing
                        result = await researcher.research_mine_comprehensive(mine_name)
                        results.append(result)
                        print(f"      ✅ {mine_name} - {result.data_completeness:.1%} complete")
                    except Exception as e:
                        # Error handling
                        error_result = MineResearchResult()
                        error_result.mine_data.name = mine_name
                        error_result.add_error(str(e))
                        results.append(error_result)
                        print(f"      ❌ {mine_name} - Error: {str(e)[:50]}...")
                else:
                    # Simulate processing
                    await asyncio.sleep(0.5)  # Simulate processing time
                    
                    # Create simulated result
                    mine_data = MineDataFields(name=mine_name)
                    
                    # Add some simulated data based on known mines
                    if "gold" in mine_name.lower() or mine_name in ["Éléonore", "Canadian Malartic", "LaRonde"]:
                        mine_data.rohstoffabbau = "Gold"
                        mine_data.restaurationskosten_cad = str(25000000 + (hash(mine_name) % 50000000))
                    elif mine_name in ["Raglan"]:
                        mine_data.rohstoffabbau = "Nickel"
                        mine_data.restaurationskosten_cad = str(15000000 + (hash(mine_name) % 30000000))
                    elif "wright" in mine_name.lower() or "lake" in mine_name.lower():
                        mine_data.rohstoffabbau = "Eisenerz"
                        mine_data.restaurationskosten_cad = str(40000000 + (hash(mine_name) % 60000000))
                    
                    mine_data.aktivitaetsstatus = "aktiv" if hash(mine_name) % 3 != 0 else "geschlossen"
                    
                    result = MineResearchResult(mine_data=mine_data)
                    result.research_duration = 0.5 + (hash(mine_name) % 100) / 100
                    result.confidence_score = 0.6 + (hash(mine_name) % 40) / 100
                    
                    results.append(result)
                    print(f"      ✅ {mine_name} - {result.confidence_score:.1%} confidence")
            
            batch_time = time.time() - batch_start
            print(f"      ⏱️  Batch completed in {batch_time:.1f}s")
            
            # Rate limiting between batches
            if batch_num < total_batches:
                await asyncio.sleep(0.5)
        
        total_time = time.time() - start_time
        
        # Create batch result
        batch_result = BatchResearchResult(results=results)
        batch_result.total_duration = total_time
        
        # Calculate metrics
        successful = sum(1 for r in results if r.validation_status != 'invalid')
        success_rate = successful / len(results) if results else 0
        avg_time_per_mine = total_time / len(results) if results else 0
        
        print(f"\n   📊 Batch Summary:")
        print(f"      Total time: {total_time:.1f}s")
        print(f"      Success rate: {success_rate:.1%} ({successful}/{len(results)})")
        print(f"      Avg per mine: {avg_time_per_mine:.1f}s")
        print(f"      Throughput: {len(results)/total_time*3600:.0f} mines/hour")
        
        # Store metrics
        self.performance_metrics['batch_times'].append(total_time)
        self.performance_metrics['success_rates'].append(success_rate)
        self.performance_metrics['avg_per_mine'].append(avg_time_per_mine)
        self.performance_metrics['total_processed'] += len(results)
        
        return {
            'batch_result': batch_result,
            'metrics': {
                'total_time': total_time,
                'success_rate': success_rate,
                'avg_time_per_mine': avg_time_per_mine,
                'throughput_per_hour': len(results)/total_time*3600
            }
        }
    
    def save_batch_results(self, batch_results: Dict, batch_name: str):
        """Speichert Batch-Ergebnisse"""
        print(f"\n💾 Saving batch results...")
        
        batch_result = batch_results['batch_result']
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save CSV results
        csv_file = project_root / "demos" / f"batch_results_{batch_name}_{timestamp}.csv"
        
        import pandas as pd
        
        # Convert to DataFrame
        rows = []
        for result in batch_result.results:
            rows.append(result.mine_data.to_dict())
        
        df = pd.DataFrame(rows)
        
        # Save with production format
        try:
            from config.settings import ProjectSettings
            df.to_csv(csv_file, index=False,
                     encoding=ProjectSettings.CSV_ENCODING,
                     sep=ProjectSettings.CSV_DELIMITER)
        except:
            df.to_csv(csv_file, index=False, encoding='utf-8-sig', sep='|')
        
        print(f"   ✅ CSV saved: {csv_file.name}")
        
        # Save detailed JSON
        json_file = project_root / "demos" / f"batch_detailed_{batch_name}_{timestamp}.json"
        
        detailed_data = {
            'batch_info': {
                'name': batch_name,
                'timestamp': timestamp,
                'total_mines': len(batch_result.results),
                'success_count': batch_result.success_count,
                'failure_count': batch_result.failure_count,
                'total_duration': batch_result.total_duration
            },
            'metrics': batch_results['metrics'],
            'results': []
        }
        
        for result in batch_result.results:
            detailed_data['results'].append({
                'mine_name': result.mine_data.name,
                'completion_rate': result.data_completeness,
                'confidence_score': result.confidence_score,
                'duration': result.research_duration,
                'validation_status': result.validation_status,
                'sources_count': len(result.sources),
                'errors': result.errors
            })
        
        import json
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_data, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ JSON saved: {json_file.name}")
        
        return csv_file, json_file
    
    def show_performance_analysis(self):
        """Zeigt Performance-Analyse"""
        print(f"\n📈 Performance Analysis:")
        
        if not self.performance_metrics['batch_times']:
            print("   No batch data available yet")
            return
        
        metrics = self.performance_metrics
        
        print(f"   Total mines processed: {metrics['total_processed']}")
        print(f"   Average batch time: {sum(metrics['batch_times'])/len(metrics['batch_times']):.1f}s")
        print(f"   Average success rate: {sum(metrics['success_rates'])/len(metrics['success_rates']):.1%}")
        print(f"   Average time per mine: {sum(metrics['avg_per_mine'])/len(metrics['avg_per_mine']):.1f}s")
        
        # Estimate large-scale performance
        avg_per_mine = sum(metrics['avg_per_mine'])/len(metrics['avg_per_mine'])
        
        print(f"\n   📊 Scaling Estimates:")
        print(f"      100 mines: ~{100 * avg_per_mine / 60:.1f} minutes")
        print(f"      500 mines: ~{500 * avg_per_mine / 3600:.1f} hours")
        print(f"      1000 mines: ~{1000 * avg_per_mine / 3600:.1f} hours")
        
        # Cost estimates (if using real API)
        print(f"\n   💰 Cost Estimates (Perplexity Pro $20/month):")
        print(f"      Per mine: ~$0.20 (vs $100 manual research)")
        print(f"      100 mines: ~$20 (vs $10,000 manual)")
        print(f"      ROI: {10000/20:.0f}x cost savings")
    
    async def run_interactive_demo(self):
        """Führt interaktive Demo durch"""
        
        self.print_banner()
        self.show_available_mines()
        
        # Create batch files
        batch_files = self.create_demo_batch_files()
        
        # Ask user for demo type
        print(f"\n🎯 Demo Options:")
        print(f"   1. Quick Demo (3 mines) - ~2 minutes")
        print(f"   2. Standard Demo (5 mines) - ~3 minutes")
        print(f"   3. Extended Demo (10 mines) - ~5 minutes")
        print(f"   4. Custom batch from file")
        print(f"   5. Performance comparison")
        
        choice = input("\n   Select demo (1-5): ").strip()
        
        # Check for real API
        use_real_api = False
        try:
            from config.api_keys import APIConfig
            if (APIConfig.PERPLEXITY_API_KEY and 
                not APIConfig.PERPLEXITY_API_KEY.startswith('your_')):
                
                api_choice = input("   🔑 Use real API? (y/n): ").lower().strip()
                use_real_api = (api_choice == 'y')
        except:
            pass
        
        # Run selected demo
        if choice == "1":
            await self._run_quick_demo(use_real_api)
        elif choice == "2":
            await self._run_standard_demo(use_real_api)
        elif choice == "3":
            await self._run_extended_demo(use_real_api)
        elif choice == "4":
            await self._run_custom_demo(use_real_api)
        elif choice == "5":
            await self._run_performance_comparison(use_real_api)
        else:
            print("   Invalid choice. Running quick demo...")
            await self._run_quick_demo(use_real_api)
    
    async def _run_quick_demo(self, use_real_api: bool):
        """Führt Quick Demo durch"""
        mine_names = self.quebec_mines[:3]
        results = await self.simulate_batch_processing(mine_names, batch_size=2, use_real_api=use_real_api)
        self.save_batch_results(results, "quick")
    
    async def _run_standard_demo(self, use_real_api: bool):
        """Führt Standard Demo durch"""
        mine_names = self.quebec_mines[:5]
        results = await self.simulate_batch_processing(mine_names, batch_size=3, use_real_api=use_real_api)
        self.save_batch_results(results, "standard")
    
    async def _run_extended_demo(self, use_real_api: bool):
        """Führt Extended Demo durch"""
        mine_names = self.quebec_mines[:10]
        results = await self.simulate_batch_processing(mine_names, batch_size=5, use_real_api=use_real_api)
        self.save_batch_results(results, "extended")
    
    async def _run_custom_demo(self, use_real_api: bool):
        """Führt Custom Demo durch"""
        print("\n   📁 Available batch files:")
        demos_dir = project_root / "demos"
        txt_files = list(demos_dir.glob("*.txt"))
        
        for i, file in enumerate(txt_files, 1):
            print(f"      {i}. {file.name}")
        
        try:
            file_choice = int(input("   Select file (number): ")) - 1
            selected_file = txt_files[file_choice]
            
            # Read mine names from file
            with open(selected_file, 'r', encoding='utf-8') as f:
                mine_names = [line.strip() for line in f if line.strip()]
            
            batch_size = int(input(f"   Batch size (1-{len(mine_names)}): "))
            
            results = await self.simulate_batch_processing(mine_names, batch_size, use_real_api)
            self.save_batch_results(results, f"custom_{selected_file.stem}")
            
        except (ValueError, IndexError):
            print("   Invalid selection. Running standard demo...")
            await self._run_standard_demo(use_real_api)
    
    async def _run_performance_comparison(self, use_real_api: bool):
        """Führt Performance-Vergleich durch"""
        print("\n🏃 Running performance comparison...")
        
        test_mines = self.quebec_mines[:6]  # Use 6 mines for comparison
        
        for batch_size in [2, 3, 6]:
            print(f"\n   Testing batch size: {batch_size}")
            results = await self.simulate_batch_processing(test_mines, batch_size, use_real_api)
        
        self.show_performance_analysis()

async def main():
    """Hauptfunktion"""
    
    demo = BatchProcessingDemo()
    await demo.run_interactive_demo()
    
    print(f"\n🎉 Batch processing demo completed!")
    print(f"📁 Check demos/ directory for output files")
    print(f"💡 Use this approach for production batch processing")

if __name__ == "__main__":
    asyncio.run(main())
