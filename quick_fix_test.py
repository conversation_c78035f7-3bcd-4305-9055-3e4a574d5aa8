#!/usr/bin/env python3
"""
Quick Test Script for Enhanced Research Engine Fixes
Tests the basic functionality after bug fixes
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def quick_test_fixes():
    """Testet die Fixes für die Enhanced Research Engine"""
    
    print("🔧 Quick Test: Enhanced Research Engine Fixes")
    print("=" * 50)
    
    try:
        # Test 1: Import Enhanced Research Engine
        print("Test 1: Import Enhanced Research Engine...")
        from enhanced_web_researcher import EnhancedMiningResearcher
        print("✅ Enhanced Research Engine imported successfully")
        
        # Test 2: Check if specialized prompts are correct
        print("\nTest 2: Check specialized prompts...")
        test_config = {'perplexity_key': 'test-key'}
        researcher = EnhancedMiningResearcher(test_config)
        
        # Check if prompts are strings (not f-strings)
        for prompt_key, prompt_text in researcher.specialized_prompts.items():
            if "{mine_name}" in prompt_text:
                print(f"✅ {prompt_key} prompt has correct placeholder")
            else:
                print(f"❌ {prompt_key} prompt missing placeholder")
        
        # Test 3: Test prompt formatting
        print("\nTest 3: Test prompt formatting...")
        try:
            test_prompt = researcher.specialized_prompts["production_data"]
            formatted_prompt = test_prompt.format(mine_name="Test Mine")
            print("✅ Prompt formatting works correctly")
        except Exception as e:
            print(f"❌ Prompt formatting failed: {e}")
        
        # Test 4: Check research phases
        print("\nTest 4: Check research phases...")
        expected_phases = 6
        actual_phases = len(researcher.research_phases)
        if actual_phases == expected_phases:
            print(f"✅ All {actual_phases} research phases available")
        else:
            print(f"❌ Expected {expected_phases} phases, found {actual_phases}")
        
        # Test 5: Check API key loading
        print("\nTest 5: Check API key loading...")
        try:
            from config.api_keys import APIConfig
            if APIConfig.PERPLEXITY_API_KEY:
                print("✅ Perplexity API key found")
                
                # Test 6: Create async context (without actual API call)
                print("\nTest 6: Test async context creation...")
                config = {'perplexity_key': APIConfig.PERPLEXITY_API_KEY}
                test_researcher = EnhancedMiningResearcher(config)
                
                print("✅ Enhanced Research Engine can be created with real API key")
                print("✅ Ready for actual research testing")
                
            else:
                print("⚠️ Perplexity API key not configured")
                print("   Tests passed but real research not possible")
                
        except Exception as e:
            print(f"❌ API key loading failed: {e}")
        
        print("\n🎉 All basic tests passed!")
        print("✅ Enhanced Research Engine fixes are working")
        print("\n💡 Next step: Run full test with:")
        print("   python test_enhanced_improvements.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Hauptfunktion"""
    
    success = await quick_test_fixes()
    
    if success:
        print("\n🚀 Enhanced Research Engine is ready!")
        return 0
    else:
        print("\n❌ Enhanced Research Engine has issues")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
