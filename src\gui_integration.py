"""
GUI Integration für MineExtractorWeb v1.0
Erweitert bestehende MineExtractor GUI um Web-Research-Funktionalität
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import threading
from typing import List, Dict, Callable, Optional
import pandas as pd
from datetime import datetime
import os
import sys
import json

# Flexible imports
try:
    from .web_researcher import WebMiningResearcher
    from .mine_data_models import MineDataFields, BatchResearchResult
except ImportError:
    from web_researcher import WebMiningResearcher
    from mine_data_models import MineDataFields, BatchResearchResult

class WebResearchGUIExtension:
    """
    Erweitert bestehende MineExtractor GUI um Web-Research-Funktionalität
    Integriert nahtlos als neuer Tab mit vollständiger Funktionalität
    """
    
    def __init__(self, parent_gui=None, parent_notebook=None):
        """
        Initialisiert GUI-Erweiterung
        
        Args:
            parent_gui: Bestehende MineExtractorGUI Instanz (optional für Standalone)
            parent_notebook: Bestehende ttk.Notebook Instanz (optional für Standalone)
        """
        
        self.parent_gui = parent_gui
        self.parent_notebook = parent_notebook
        
        # Web Research Engine
        self.web_researcher = None
        self.research_thread = None
        self.is_researching = False
        
        # Load settings
        try:
            from config.settings import ProjectSettings
            self.settings = ProjectSettings
        except ImportError:
            self.settings = None
        
        # Results storage
        self.current_results = None
        self.last_batch_result = None
        
        # GUI State Variables will be setup after root window creation
        self.gui_vars_initialized = False
        
        # Setup GUI
        if parent_notebook:
            self._setup_gui_variables()  # Parent has root already
            self.setup_web_research_tab()
        else:
            # Standalone mode - setup variables after root creation
            self.setup_standalone_window()
    
    def _setup_gui_variables(self):
        """Initialisiert alle tkinter Variables"""
        
        # API Configuration
        self.api_key_perplexity = tk.StringVar()
        self.api_key_tavily = tk.StringVar()
        self.api_key_exa = tk.StringVar()
        
        # Load API keys from configuration
        self._load_api_keys_from_config()
        
        # Research Settings
        self.research_timeout = tk.StringVar(value="120")
        self.batch_delay = tk.StringVar(value="2")
        
        # File paths
        self.output_file_web = tk.StringVar()
        self._set_default_output_path()
        
        # Progress tracking
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="✅ Ready for Web Research")
        self.mines_processed = tk.StringVar(value="0 / 0")
        
        # Options
        self.save_detailed_json = tk.BooleanVar(value=False)
        self.auto_open_results = tk.BooleanVar(value=True)
        self.focus_financial_data = tk.BooleanVar(value=True)
    
    def _set_default_output_path(self):
        """Setzt Standard-Ausgabepfad"""
        if self.settings:
            default_path = self.settings.get_default_output_path()
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_path = f"web_research_results_{timestamp}.csv"
        
        self.output_file_web.set(str(default_path))
    
    def setup_standalone_window(self):
        """Erstellt Standalone-Fenster für Testing"""
        
        self.root = tk.Tk()
        self.root.title("MineExtractorWeb v1.0 - Standalone")
        self.root.geometry("1000x800")
        
        # NOW setup GUI variables after root exists
        self._setup_gui_variables()
        self.gui_vars_initialized = True
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create web research tab
        self.setup_web_research_tab()
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Zentriert Fenster auf Bildschirm"""
        if hasattr(self, 'root'):
            self.root.update_idletasks()
            x = (self.root.winfo_screenwidth() - self.root.winfo_width()) // 2
            y = (self.root.winfo_screenheight() - self.root.winfo_height()) // 2
            self.root.geometry(f"+{x}+{y}")
    
    def _load_api_keys_from_config(self):
        """Lädt API Keys automatisch aus der Konfiguration"""
        try:
            from config.api_keys import APIConfig
            
            # Load API keys from config
            if APIConfig.PERPLEXITY_API_KEY:
                self.api_key_perplexity.set(APIConfig.PERPLEXITY_API_KEY)
                
            if APIConfig.TAVILY_API_KEY:
                self.api_key_tavily.set(APIConfig.TAVILY_API_KEY)
                
            if APIConfig.EXA_API_KEY:
                self.api_key_exa.set(APIConfig.EXA_API_KEY)
            
            # Auto-test APIs if keys are loaded (delayed to allow GUI setup)
            if hasattr(self, 'root') or (self.parent_gui and hasattr(self.parent_gui, 'root')):
                # Delay auto-test to allow GUI components to be created
                if hasattr(self, 'root'):
                    self.root.after(1000, self._auto_test_apis)
                elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                    self.parent_gui.root.after(1000, self._auto_test_apis)
            
            # Log successful loading
            keys_loaded = []
            if APIConfig.PERPLEXITY_API_KEY: keys_loaded.append("Perplexity")
            if APIConfig.TAVILY_API_KEY: keys_loaded.append("Tavily")
            if APIConfig.EXA_API_KEY: keys_loaded.append("Exa")
            
            if keys_loaded:
                keys_str = ", ".join(keys_loaded)
                if hasattr(self, 'log_text'):  # Only log if log_text exists
                    self.log(f"🔑 Loaded API keys: {keys_str}", "info")
            
        except ImportError as e:
            if hasattr(self, 'log_text'):
                self.log(f"⚠️ Could not load API configuration: {e}", "warning")
        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log(f"❌ Error loading API keys: {e}", "error")
    
    def _auto_test_apis(self):
        """Testet automatisch alle konfigurierten APIs beim Start"""
        try:
            # Only auto-test if GUI components exist
            if not hasattr(self, 'perplexity_status') or not hasattr(self, 'tavily_status'):
                return
            
            # Test Perplexity if configured
            if self.api_key_perplexity.get().strip():
                self.log("🧪 Auto-testing Perplexity API...", "info")
                threading.Thread(target=self._test_api_thread, 
                               args=('perplexity', self.api_key_perplexity.get().strip(), self.perplexity_status), 
                               daemon=True).start()
            
            # Test Tavily if configured
            if self.api_key_tavily.get().strip():
                self.log("🧪 Auto-testing Tavily API...", "info")
                threading.Thread(target=self._test_api_thread, 
                               args=('tavily', self.api_key_tavily.get().strip(), self.tavily_status), 
                               daemon=True).start()
            
            # Update status summary after delay to allow tests to complete
            if hasattr(self, 'root'):
                self.root.after(5000, self.update_api_status_summary)
            elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                self.parent_gui.root.after(5000, self.update_api_status_summary)
                
        except Exception as e:
            self.log(f"❌ Auto-test failed: {e}", "error")
    
    def setup_web_research_tab(self):
        """Erstellt Web Research Tab"""
        
        # Determine parent notebook
        notebook = self.parent_notebook if self.parent_notebook else self.notebook
        
        # Create Web Research Tab
        self.web_tab = ttk.Frame(notebook)
        notebook.add(self.web_tab, text="🌐 Web Research")
        
        # Create scrollable frame
        self.setup_scrollable_content()
    
    def setup_scrollable_content(self):
        """Erstellt scrollbaren Content-Bereich"""
        
        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(self.web_tab, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.web_tab, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scrollable elements
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # Setup main content
        self.setup_main_content()
    
    def setup_main_content(self):
        """Erstellt Hauptinhalt der GUI"""
        
        main_frame = self.scrollable_frame
        main_frame.columnconfigure(1, weight=1)
        
        current_row = 0
        
        # Title Section
        current_row = self.setup_title_section(main_frame, current_row)
        
        # API Configuration Section
        current_row = self.setup_api_config_section(main_frame, current_row)
        
        # Input Section
        current_row = self.setup_input_section(main_frame, current_row)
        
        # Options Section
        current_row = self.setup_options_section(main_frame, current_row)
        
        # Control Buttons
        current_row = self.setup_control_buttons(main_frame, current_row)
        
        # Progress Section
        current_row = self.setup_progress_section(main_frame, current_row)
        
        # Results Section
        current_row = self.setup_results_section(main_frame, current_row)
        
        # Statistics Section
        current_row = self.setup_statistics_section(main_frame, current_row)
    
    def setup_title_section(self, parent, row):
        """Erstellt Titel-Bereich"""
        
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(1, weight=1)
        
        # Main title
        title_label = ttk.Label(title_frame, 
                               text="🌐 MineExtractorWeb v1.0", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 5))
        
        # Subtitle
        subtitle_label = ttk.Label(title_frame,
                                  text="Multi-System Web Research Engine für Quebec Mining Data",
                                  font=("Arial", 11), foreground="blue")
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # Status indicator
        self.api_status_summary = ttk.Label(title_frame, text="❌ APIs nicht konfiguriert", 
                                           foreground="red", font=("Arial", 10, "bold"))
        self.api_status_summary.grid(row=2, column=0, columnspan=3)
        
        return row + 1
    
    def setup_api_config_section(self, parent, row):
        """Erstellt API-Konfiguration Section"""
        
        api_frame = ttk.LabelFrame(parent, text="🔑 API Configuration", padding="15")
        api_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        api_frame.columnconfigure(1, weight=1)
        
        # Perplexity API (Primary)
        ttk.Label(api_frame, text="Perplexity AI (Primary):", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        self.perplexity_entry = ttk.Entry(api_frame, textvariable=self.api_key_perplexity, 
                                         show="*", width=50, font=("Consolas", 9))
        self.perplexity_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.perplexity_test_btn = ttk.Button(api_frame, text="Test", 
                                            command=lambda: self.test_api_key('perplexity'))
        self.perplexity_test_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Status indicators
        self.perplexity_status = ttk.Label(api_frame, text="❌ Not configured", foreground="red")
        self.perplexity_status.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Tavily AI (Government Data)
        ttk.Label(api_frame, text="Tavily AI (Government Data):", font=("Arial", 10)).grid(
            row=2, column=0, sticky=tk.W, pady=5)
        self.tavily_entry = ttk.Entry(api_frame, textvariable=self.api_key_tavily, 
                                     show="*", width=50, font=("Consolas", 9))
        self.tavily_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.tavily_test_btn = ttk.Button(api_frame, text="Test", 
                                        command=lambda: self.test_api_key('tavily'))
        self.tavily_test_btn.grid(row=2, column=2, padx=5, pady=5)
        
        # Tavily status indicator
        self.tavily_status = ttk.Label(api_frame, text="❌ Not configured", foreground="red")
        self.tavily_status.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Exa.ai (Future)
        ttk.Label(api_frame, text="Exa.ai (Future):", font=("Arial", 10)).grid(
            row=4, column=0, sticky=tk.W, pady=5)
        self.exa_entry = ttk.Entry(api_frame, textvariable=self.api_key_exa, 
                                  show="*", width=50, font=("Consolas", 9), state="disabled")
        self.exa_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(api_frame, text="Phase 2", state="disabled").grid(row=4, column=2, padx=5, pady=5)
        
        # API Info
        info_text = ("💡 Hinweis: Perplexity AI ist erforderlich. Tavily AI ist optional für Government Data.")
        ttk.Label(api_frame, text=info_text, foreground="gray", font=("Arial", 9)).grid(
            row=5, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        return row + 1
    
    def setup_input_section(self, parent, row):
        """Erstellt Mine Input Section"""
        
        input_frame = ttk.LabelFrame(parent, text="📝 Mine Names Input", padding="15")
        input_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        # Instructions
        instructions = """Geben Sie Mine-Namen ein (eine pro Zeile oder komma-getrennt):
Beispiele: Éléonore, Canadian Malartic, Raglan, Casa Berardi, LaRonde, Mont Wright"""
        ttk.Label(input_frame, text=instructions, foreground="gray", font=("Arial", 9)).grid(
            row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Text Input with scrollbar
        text_frame = ttk.Frame(input_frame)
        text_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        text_frame.columnconfigure(0, weight=1)
        
        self.mine_input_text = tk.Text(text_frame, height=10, width=70, wrap=tk.WORD, 
                                      font=("Arial", 10))
        self.mine_input_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        input_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, 
                                       command=self.mine_input_text.yview)
        input_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.mine_input_text.configure(yscrollcommand=input_scrollbar.set)
        
        # Quick fill buttons
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="📋 Quebec Top 10", 
                  command=self.fill_quebec_top_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🥇 Gold Mines", 
                  command=self.fill_gold_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 Test Mines", 
                  command=self.fill_test_mines).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ Clear", 
                  command=self.clear_mine_input).pack(side=tk.LEFT, padx=5)
        
        return row + 1
    
    def setup_options_section(self, parent, row):
        """Erstellt Options Section"""
        
        options_frame = ttk.LabelFrame(parent, text="⚙️ Research Options", padding="15")
        options_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        options_frame.columnconfigure(1, weight=1)
        
        # Research settings
        ttk.Label(options_frame, text="Timeout (s):").grid(row=0, column=0, sticky=tk.W, padx=(20, 5), pady=5)
        ttk.Entry(options_frame, textvariable=self.research_timeout, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(options_frame, text="Batch Delay (s):").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        ttk.Entry(options_frame, textvariable=self.batch_delay, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Output file
        ttk.Label(options_frame, text="Output CSV:").grid(row=1, column=0, sticky=tk.W, pady=5)
        output_entry = ttk.Entry(options_frame, textvariable=self.output_file_web, width=60)
        output_entry.grid(row=1, column=1, columnspan=4, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(options_frame, text="📂", command=self.browse_output_file).grid(row=1, column=5, pady=5)
        
        # Checkboxes
        checkbox_frame = ttk.Frame(options_frame)
        checkbox_frame.grid(row=2, column=0, columnspan=6, sticky=tk.W, pady=(10, 0))
        
        ttk.Checkbutton(checkbox_frame, text="💰 Focus on Financial Data (Restoration Costs)", 
                       variable=self.focus_financial_data).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="💾 Save Detailed JSON", 
                       variable=self.save_detailed_json).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(checkbox_frame, text="📁 Auto-Open Results", 
                       variable=self.auto_open_results).pack(side=tk.LEFT)
        
        return row + 1
    
    def setup_control_buttons(self, parent, row):
        """Erstellt Control Buttons Section"""
        
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        # Main action buttons
        self.start_button = ttk.Button(button_frame, text="🚀 Start Web Research", 
                                      command=self.start_web_research,
                                      style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Research", 
                                     command=self.stop_web_research, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Result buttons
        self.open_results_button = ttk.Button(button_frame, text="📁 Open Results", 
                                             command=self.open_results, state=tk.DISABLED)
        self.open_results_button.pack(side=tk.LEFT, padx=10)
        
        self.export_json_button = ttk.Button(button_frame, text="📊 Export JSON", 
                                           command=self.export_detailed_json, state=tk.DISABLED)
        self.export_json_button.pack(side=tk.LEFT, padx=5)
        
        return row + 1
    
    def setup_progress_section(self, parent, row):
        """Erstellt Progress Section"""
        
        progress_frame = ttk.LabelFrame(parent, text="📊 Progress & Status", padding="15")
        progress_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=500, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Status labels
        status_info_frame = ttk.Frame(progress_frame)
        status_info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        status_info_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_info_frame, textvariable=self.status_var, 
                                     font=("Arial", 11, "bold"))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.mines_label = ttk.Label(status_info_frame, textvariable=self.mines_processed, 
                                    font=("Arial", 10))
        self.mines_label.grid(row=0, column=1, sticky=tk.E)
        
        return row + 1
    
    def setup_results_section(self, parent, row):
        """Erstellt Results Display Section"""
        
        results_frame = ttk.LabelFrame(parent, text="📝 Research Log", padding="15")
        results_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD, 
                                                 font=("Consolas", 9), background="#f8f9fa",
                                                 foreground="#333333")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure text tags for colored output
        self.log_text.tag_configure("success", foreground="#28a745")
        self.log_text.tag_configure("error", foreground="#dc3545")
        self.log_text.tag_configure("warning", foreground="#ffc107")
        self.log_text.tag_configure("info", foreground="#17a2b8")
        
        # Log control buttons
        log_button_frame = ttk.Frame(results_frame)
        log_button_frame.grid(row=1, column=0, pady=(10, 0))
        
        ttk.Button(log_button_frame, text="🗑️ Clear Log", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="📋 Copy Log", 
                  command=self.copy_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="💾 Save Log", 
                  command=self.save_log).pack(side=tk.LEFT, padx=5)
        
        # Initial log message
        self.log("🌐 MineExtractorWeb v1.0 initialized", "info")
        self.log("💡 Configure Perplexity API key and enter mine names to start", "info")
        
        return row + 1
    
    def setup_statistics_section(self, parent, row):
        """Erstellt Statistics Section"""
        
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistics", padding="15")
        stats_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # Statistics will be populated during research
        self.stats_text = tk.Text(stats_frame, height=6, width=80, 
                                 font=("Consolas", 9), background="#f0f0f0",
                                 state=tk.DISABLED)
        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, 
                                       command=self.stats_text.yview)
        stats_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        # Initial stats
        self.update_statistics_display()
        
        return row + 1
    
    # =========================
    # Event Handlers
    # =========================
    
    def test_api_key(self, api_name: str):
        """Testet API Key"""
        
        if api_name == 'perplexity':
            api_key = self.api_key_perplexity.get().strip()
            status_label = self.perplexity_status
        elif api_name == 'tavily':
            api_key = self.api_key_tavily.get().strip()
            status_label = self.tavily_status
        else:
            self.log(f"⚠️ {api_name.title()} API testing not implemented yet", "warning")
            return
        
        if not api_key:
            messagebox.showerror("Error", f"Please enter {api_name.title()} API Key")
            return
        
        self.log(f"🧪 Testing {api_name.title()} API...", "info")
        status_label.config(text="🔄 Testing...", foreground="orange")
        
        # Run test in separate thread
        threading.Thread(target=self._test_api_thread, 
                        args=(api_name, api_key, status_label), daemon=True).start()
    
    def _test_api_thread(self, api_name: str, api_key: str, status_label):
        """API Test in separatem Thread"""
        
        try:
            if api_name == 'perplexity':
                from .perplexity_client import test_api_key
                success = asyncio.run(test_api_key(api_key))
            elif api_name == 'tavily':
                from .tavily_client import test_api_key
                success = asyncio.run(test_api_key(api_key))
            else:
                success = False
            
            # Update GUI from main thread
            if hasattr(self, 'root'):
                self.root.after(0, lambda: self._api_test_completed(api_name, success, status_label))
            elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                self.parent_gui.root.after(0, lambda: self._api_test_completed(api_name, success, status_label))
            
        except Exception as e:
            if hasattr(self, 'root'):
                self.root.after(0, lambda: self._api_test_failed(api_name, str(e), status_label))
            elif self.parent_gui and hasattr(self.parent_gui, 'root'):
                self.parent_gui.root.after(0, lambda: self._api_test_failed(api_name, str(e), status_label))
    
    def _api_test_completed(self, api_name: str, success: bool, status_label):
        """API Test abgeschlossen"""
        
        if success:
            status_label.config(text="✅ Connected", foreground="green")
            self.log(f"✅ {api_name.title()} API test successful!", "success")
            self.update_api_status_summary()
            messagebox.showinfo("Success", f"{api_name.title()} API connected successfully!")
        else:
            status_label.config(text="❌ Connection failed", foreground="red")
            self.log(f"❌ {api_name.title()} API test failed", "error")
            messagebox.showerror("API Test Failed", f"{api_name.title()} API connection failed")
    
    def _api_test_failed(self, api_name: str, error: str, status_label):
        """API Test fehlgeschlagen"""
        
        status_label.config(text="❌ Test failed", foreground="red")
        self.log(f"❌ {api_name.title()} API test failed: {error}", "error")
        messagebox.showerror("API Test Failed", f"Error: {error}")
    
    def update_api_status_summary(self):
        """Aktualisiert API Status Summary"""
        
        perplexity_configured = bool(self.api_key_perplexity.get().strip())
        tavily_configured = bool(self.api_key_tavily.get().strip())
        
        if perplexity_configured:
            if tavily_configured:
                self.api_status_summary.config(text="✅ Ready for Enhanced Research (Perplexity + Tavily)", foreground="green")
            else:
                self.api_status_summary.config(text="✅ Ready for Basic Research (Perplexity only)", foreground="green")
        else:
            self.api_status_summary.config(text="❌ Please configure Perplexity API key", foreground="red")
    
    def fill_quebec_top_mines(self):
        """Füllt Eingabefeld mit Quebec Top-Minen"""
        
        top_mines = [
            "Éléonore",
            "Canadian Malartic", 
            "Raglan",
            "Casa Berardi",
            "LaRonde",
            "Mont Wright",
            "Lac Tio",
            "Troilus",
            "Niobec",
            "Beaufor"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(top_mines))
        self.log(f"📝 Loaded {len(top_mines)} Quebec top mines", "info")
    
    def fill_gold_mines(self):
        """Füllt Eingabefeld mit Gold-Minen"""
        
        gold_mines = [
            "Éléonore",
            "Canadian Malartic",
            "Casa Berardi", 
            "LaRonde",
            "Goldex",
            "Beaufor",
            "Sigma",
            "Kiena",
            "Lamaque"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(gold_mines))
        self.log(f"📝 Loaded {len(gold_mines)} Quebec gold mines", "info")
    
    def fill_test_mines(self):
        """Füllt Eingabefeld mit Test-Minen"""
        
        test_mines = [
            "Éléonore",
            "Canadian Malartic",
            "Raglan"
        ]
        
        self.mine_input_text.delete(1.0, tk.END)
        self.mine_input_text.insert(1.0, "\n".join(test_mines))
        self.log(f"📝 Loaded {len(test_mines)} test mines", "info")
    
    def clear_mine_input(self):
        """Leert Eingabefeld"""
        
        self.mine_input_text.delete(1.0, tk.END)
        self.log("🗑️ Mine input cleared", "info")
    
    def browse_output_file(self):
        """Datei-Browser für Output-Datei"""
        
        filename = filedialog.asksaveasfilename(
            title="Save Web Research Results",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialname=os.path.basename(self.output_file_web.get())
        )
        
        if filename:
            self.output_file_web.set(filename)
            self.log(f"💾 Output file: {os.path.basename(filename)}", "info")
    
    def start_web_research(self):
        """Startet Web Research"""
        
        # Validierung
        if not self._validate_research_inputs():
            return
        
        # UI Updates
        self.is_researching = True
        self._update_ui_for_research_start()
        
        # Parse mine names
        mine_names = self._parse_mine_names()
        self.log(f"🎯 Starting research for {len(mine_names)} mines", "info")
        
        # Reset progress
        self.progress_var.set(0)
        self.mines_processed.set(f"0 / {len(mine_names)}")
        
        # Start research in separate thread
        self.research_thread = threading.Thread(
            target=self._research_thread, args=(mine_names,), daemon=True)
        self.research_thread.start()
    
    def _validate_research_inputs(self) -> bool:
        """Validiert Research-Eingaben"""
        
        # API Key check
        if not self.api_key_perplexity.get().strip():
            messagebox.showerror("Error", "Please configure Perplexity API Key")
            return False
        
        # Mine names check
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        if not mine_text:
            messagebox.showerror("Error", "Please enter mine names")
            return False
        
        # Output file check
        if not self.output_file_web.get().strip():
            messagebox.showerror("Error", "Please specify output file")
            return False
        
        return True
    
    def _update_ui_for_research_start(self):
        """Aktualisiert UI für Research-Start"""
        
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.open_results_button.config(state=tk.DISABLED)
        self.export_json_button.config(state=tk.DISABLED)
        self.status_var.set("🚀 Starting web research...")
    
    def _parse_mine_names(self) -> List[str]:
        """Parst Mine-Namen aus Eingabefeld"""
        mine_text = self.mine_input_text.get(1.0, tk.END).strip()
        # Split by newlines or commas
        if '\n' in mine_text:
            mine_names = [name.strip() for name in mine_text.split('\n')]
        else:
            mine_names = [name.strip() for name in mine_text.split(',')]
        # Filter empty names
        mine_names = [name for name in mine_names if name]
        return mine_names
    
    def _research_thread(self, mine_names: List[str]):
        """Research in separatem Thread"""
        
        try:
            # Create researcher
            config = {
                'perplexity_key': self.api_key_perplexity.get().strip()
            }
            
            timeout = int(self.research_timeout.get() or 120)
            batch_delay = float(self.batch_delay.get() or 2)
            
            # Run async research
            batch_result = asyncio.run(self._run_research(mine_names, config, timeout, batch_delay))
            
            # Save results
            self._save_research_results(batch_result)
            
            # Success callback
            self._schedule_ui_update(lambda: self._research_completed_success(batch_result))
            
        except Exception as e:
            # Error callback
            self._schedule_ui_update(lambda: self._research_completed_error(str(e)))
    
    async def _run_research(self, mine_names: List[str], config: Dict[str, str], 
                          timeout: int, batch_delay: float) -> BatchResearchResult:
        """Führt Enhanced Web Research durch"""
        
        # Use Enhanced Research Engine if available
        try:
            from .enhanced_web_researcher import EnhancedMiningResearcher
            
            async with EnhancedMiningResearcher(config) as enhanced_researcher:
                
                # Progress tracking
                total_mines = len(mine_names)
                results = []
                
                for i, mine_name in enumerate(mine_names):
                    if not self.is_researching:
                        break
                    
                    # Enhanced progress callback
                    def enhanced_progress_callback(phase_progress, phase_status):
                        overall_progress = ((i / total_mines) * 100) + (phase_progress / total_mines)
                        status = f"Mine {i+1}/{total_mines}: {phase_status}"
                        self._schedule_ui_update(lambda: self._update_progress(overall_progress, status))
                    
                    self._schedule_ui_update(lambda: self.log(f"🔍 Starting enhanced research for {mine_name}...", "info"))
                    
                    # Run enhanced research
                    enhanced_result = await enhanced_researcher.research_mine_comprehensive(
                        mine_name, 
                        progress_callback=enhanced_progress_callback
                    )
                    
                    # Convert enhanced result to compatible format
                    compatible_result = self._convert_enhanced_result(enhanced_result)
                    results.append(compatible_result)
                    
                    # Log enhanced results
                    self._schedule_ui_update(
                        lambda r=enhanced_result: self.log(
                            f"✅ Enhanced research completed for {r.mine_name}: "
                            f"{len(r.data_points)} data points, "
                            f"{r.data_completeness_score:.1%} completeness, "
                            f"{len(r.all_sources)} sources", "success"
                        )
                    )
                    
                    # Rate limiting
                    if i < len(mine_names) - 1:  # Don't delay after last mine
                        await asyncio.sleep(batch_delay)
                
                # Create enhanced batch result
                batch_result = BatchResearchResult(
                    results=results,
                    batch_timestamp=datetime.now().isoformat(),
                    total_duration=sum(r.research_duration for r in results),
                    success_count=len([r for r in results if r.validation_status == "success"]),
                    failure_count=len([r for r in results if r.validation_status != "success"])
                )
                
                return batch_result
                
        except ImportError:
            # Fallback to original researcher
            self._schedule_ui_update(lambda: self.log("⚠️ Enhanced research engine not available, using basic research", "warning"))
            
        # Original fallback code
        researcher = WebMiningResearcher(config)
        researcher.request_delay = batch_delay
        
        def progress_callback(progress: float, status: str):
            self._schedule_ui_update(lambda: self._update_progress(progress, status))
        
        batch_result = await researcher.research_mine_list(mine_names, progress_callback)
        self.web_researcher = researcher
        
        return batch_result
            
        # Original fallback code
        researcher = WebMiningResearcher(config)
        researcher.request_delay = batch_delay
        
        def progress_callback(progress: float, status: str):
            self._schedule_ui_update(lambda: self._update_progress(progress, status))
        
        batch_result = await researcher.research_mine_list(mine_names, progress_callback)
        self.web_researcher = researcher
        
        return batch_result
    
    def _convert_enhanced_result(self, enhanced_result):
        """Konvertiert Enhanced Result zu kompatiblem MineResearchResult Format"""
        
        try:
            from src.mine_data_models import MineResearchResult, MineDataFields, SourceMetadata
            
            # Erstelle kompatible Mine-Daten
            mine_data = MineDataFields()
            mine_data.name = enhanced_result.mine_name
            
            # Mappe Enhanced Data Points zu kompatiblen Feldern
            field_mapping = {
                'mine_operator': 'betreiber',
                'production_start_year': 'produktionsstart', 
                'production_end_year': 'produktionsende',
                'annual_production_tonnes': 'foerdermenge_jahr',
                'mine_area_km2': 'flaeche_km2',
                'restoration_costs_cad': 'restaurationskosten_cad',
                'commodity_type': 'rohstoffabbau',
                'mine_type': 'minentyp',
                'mine_status': 'aktivitaetsstatus',
                'coordinates_lat': 'y_koordinate',  # Latitude -> y
                'coordinates_lon': 'x_koordinate'   # Longitude -> x
            }
            
            # Fülle kompatible Felder
            for enhanced_field, compatible_field in field_mapping.items():
                if enhanced_field in enhanced_result.data_points:
                    data_point = enhanced_result.data_points[enhanced_field]
                    setattr(mine_data, compatible_field, str(data_point.value))
            
            # Erstelle Quellenangaben-String
            source_strings = []
            for source in enhanced_result.all_sources[:5]:  # Top 5 Quellen
                reliability_stars = "★" * int(source.reliability_score * 5)
                source_string = f"{source.title} ({reliability_stars}) - {source.url}"
                source_strings.append(source_string)
            
            mine_data.quellenangaben = "; ".join(source_strings)
            
            # Erstelle kompatible Quellen-Metadaten
            compatible_sources = []
            for source in enhanced_result.all_sources:
                source_meta = SourceMetadata(
                    url=source.url,
                    title=source.title,
                    date_accessed=source.date_accessed,
                    source_type=source.content_type,
                    confidence=source.reliability_score
                )
                compatible_sources.append(source_meta)
            
            # Erstelle kompatibles Research-Result
            result = MineResearchResult(
                mine_data=mine_data,
                research_timestamp=enhanced_result.all_sources[0].date_accessed if enhanced_result.all_sources else '',
                research_duration=enhanced_result.total_research_time,
                confidence_score=enhanced_result.data_completeness_score,
                data_completeness=enhanced_result.data_completeness_score,
                validation_status="success" if enhanced_result.data_completeness_score > 0.5 else "partial",
                sources=compatible_sources
            )
            
            return result
            
        except Exception as e:
            # Fallback: Erstelle minimales Result
            from src.mine_data_models import MineResearchResult, MineDataFields
            
            mine_data = MineDataFields(name=enhanced_result.mine_name)
            mine_data.quellenangaben = f"Enhanced research completed with {len(enhanced_result.data_points)} data points"
            
            return MineResearchResult(
                mine_data=mine_data,
                research_duration=enhanced_result.total_research_time,
                validation_status="partial"
            )
    
    def _schedule_ui_update(self, update_func: Callable):
        """Plant UI-Update im Hauptthread"""
        
        if hasattr(self, 'root'):
            self.root.after(0, update_func)
        elif self.parent_gui and hasattr(self.parent_gui, 'root'):
            self.parent_gui.root.after(0, update_func)
    
    def _update_progress(self, progress: float, status: str):
        """Aktualisiert Progress-Anzeige"""
        
        self.progress_var.set(progress)
        self.status_var.set(status)
        
        # Update mines processed count
        if "(" in status and "/" in status:
            try:
                # Extract current/total from status
                parts = status.split("(")[1].split(")")[0]
                self.mines_processed.set(parts)
            except:
                pass
    
    def _save_research_results(self, batch_result: BatchResearchResult):
        """Speichert Research-Ergebnisse"""
        
        try:
            # Save CSV
            self._save_csv_results(batch_result)
            
            # Save detailed JSON if requested
            if self.save_detailed_json.get():
                self._save_json_results(batch_result)
            
            # Store results
            self.last_batch_result = batch_result
            
        except Exception as e:
            self.log(f"❌ Error saving results: {e}", "error")
            raise
    
    def _save_csv_results(self, batch_result: BatchResearchResult):
        """Speichert CSV-Ergebnisse"""
        
        output_path = self.output_file_web.get()
        
        # Create DataFrame
        rows = []
        for result in batch_result.results:
            rows.append(result.mine_data.to_dict())
        
        df = pd.DataFrame(rows)
        
        # Save CSV with same format as PDF system
        if self.settings:
            df.to_csv(output_path, index=False, 
                     encoding=self.settings.CSV_ENCODING, 
                     sep=self.settings.CSV_DELIMITER)
        else:
            df.to_csv(output_path, index=False, encoding='utf-8-sig', sep='|')
        
        self.log(f"💾 CSV results saved to: {output_path}", "success")
    
    def _save_json_results(self, batch_result: BatchResearchResult):
        """Speichert detaillierte JSON-Ergebnisse"""
        
        output_path = self.output_file_web.get()
        json_path = output_path.replace('.csv', '_detailed.json')
        
        # Create detailed JSON
        json_data = {
            'batch_info': {
                'timestamp': batch_result.batch_timestamp,
                'total_duration': batch_result.total_duration,
                'success_count': batch_result.success_count,
                'failure_count': batch_result.failure_count,
                'statistics': batch_result.get_summary_stats()
            },
            'results': []
        }
        
        for result in batch_result.results:
            json_data['results'].append(json.loads(result.to_json()))
        
        # Save JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        self.log(f"📊 Detailed JSON saved to: {json_path}", "success")
    
    def _research_completed_success(self, batch_result: BatchResearchResult):
        """Research erfolgreich abgeschlossen"""
        
        self.is_researching = False
        self._update_ui_for_research_end()
        
        self.progress_var.set(100)
        self.status_var.set(f"✅ Research completed!")
        self.mines_processed.set(f"{len(batch_result.results)} / {len(batch_result.results)}")
        
        # Log results
        stats = batch_result.get_summary_stats()
        self.log(f"🎉 Research completed successfully!", "success")
        self.log(f"📊 Results: {stats['success_count']}/{stats['total_mines']} mines successful", "success")
        self.log(f"⏱️ Total time: {stats['total_duration']:.1f}s", "info")
        self.log(f"💾 Output saved to: {self.output_file_web.get()}", "info")
        
        # Update statistics
        self.update_statistics_display()
        
        # Auto-open results if requested
        if self.auto_open_results.get():
            self.open_results()
        
        # Show completion message
        messagebox.showinfo("Research Completed", 
                           f"Web research completed!\n\n"
                           f"✅ Successful: {stats['success_count']}/{stats['total_mines']} mines\n"
                           f"⏱️ Duration: {stats['total_duration']:.1f} seconds\n"
                           f"📊 Avg. per mine: {stats['avg_duration_per_mine']:.1f}s\n\n"
                           f"Results saved to:\n{os.path.basename(self.output_file_web.get())}")
    
    def _research_completed_error(self, error: str):
        """Research mit Fehler abgebrochen"""
        
        self.is_researching = False
        self._update_ui_for_research_end()
        
        self.progress_var.set(0)
        self.status_var.set("❌ Research failed")
        
        self.log(f"❌ Research failed: {error}", "error")
        messagebox.showerror("Research Failed", f"Error: {error}")
    
    def _update_ui_for_research_end(self):
        """Aktualisiert UI für Research-Ende"""
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.open_results_button.config(state=tk.NORMAL)
        self.export_json_button.config(state=tk.NORMAL)
    
    def stop_web_research(self):
        """Stoppt Web Research"""
        
        self.is_researching = False
        self.log("⏹️ Stopping web research...", "warning")
        self.status_var.set("⏹️ Stopping research...")
        
        # The research thread will check is_researching flag
    
    def open_results(self):
        """Öffnet Ergebnis-Datei"""
        
        output_path = self.output_file_web.get()
        if os.path.exists(output_path):
            try:
                if sys.platform.startswith('win'):
                    os.startfile(output_path)
                elif sys.platform.startswith('darwin'):
                    os.system(f'open "{output_path}"')
                else:
                    os.system(f'xdg-open "{output_path}"')
                
                self.log(f"📁 Opened: {os.path.basename(output_path)}", "info")
            except Exception as e:
                messagebox.showinfo("File Location", f"Results saved to:\n{output_path}")
                self.log(f"💡 File location: {output_path}", "info")
        else:
            messagebox.showerror("File Not Found", f"Results file not found:\n{output_path}")
    
    def export_detailed_json(self):
        """Exportiert detaillierte JSON-Ergebnisse"""
        
        if not self.last_batch_result:
            messagebox.showwarning("No Results", "No research results available to export")
            return
        
        try:
            self._save_json_results(self.last_batch_result)
            messagebox.showinfo("Export Successful", "Detailed JSON results exported successfully!")
        except Exception as e:
            messagebox.showerror("Export Failed", f"Failed to export JSON: {e}")
    
    def update_statistics_display(self):
        """Aktualisiert Statistics-Anzeige"""
        
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        
        if self.web_researcher:
            stats = self.web_researcher.get_research_statistics()
            
            stats_text = f"""Research Statistics:
• Total Researched: {stats['total_researched']} mines
• Success Rate: {stats['success_rate']:.1%}
• Average Duration: {stats['avg_duration_per_mine']:.1f}s per mine
• Total Duration: {stats['total_duration']:.1f}s

API Usage:"""
            
            for api_name, api_stats in stats['api_usage'].items():
                stats_text += f"""
• {api_name.title()}: {api_stats['requests']} requests, {api_stats['successes']} successful
  Avg. Response Time: {api_stats['avg_response_time']:.1f}s"""
            
            if stats['parser_stats']['field_extraction_rates']:
                stats_text += "\n\nField Extraction Rates:"
                for field, rates in stats['parser_stats']['field_extraction_rates'].items():
                    if rates['total'] > 0:
                        stats_text += f"\n• {field}: {rates['success_rate']:.1%} ({rates['success']}/{rates['total']})"
        
        else:
            stats_text = "No research statistics available yet.\nRun web research to see detailed statistics."
        
        self.stats_text.insert(1.0, stats_text)
        self.stats_text.config(state=tk.DISABLED)
    
    def log(self, message: str, level: str = "info"):
        """Fügt Log-Nachricht hinzu"""
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # Insert with appropriate tag for coloring
        self.log_text.insert(tk.END, log_entry, level)
        self.log_text.see(tk.END)
        
        # Update GUI
        self._update_gui()
    
    def _update_gui(self):
        """Aktualisiert GUI"""
        
        if hasattr(self, 'root'):
            self.root.update_idletasks()
        elif self.parent_gui and hasattr(self.parent_gui, 'root'):
            self.parent_gui.root.update_idletasks()
    
    def clear_log(self):
        """Leert Log"""
        
        self.log_text.delete(1.0, tk.END)
        self.log("📝 Log cleared", "info")
    
    def copy_log(self):
        """Kopiert Log in Zwischenablage"""
        
        log_content = self.log_text.get(1.0, tk.END)
        root = self.root if hasattr(self, 'root') else self.parent_gui.root
        root.clipboard_clear()
        root.clipboard_append(log_content)
        self.log("📋 Log copied to clipboard", "info")
    
    def save_log(self):
        """Speichert Log in Datei"""
        
        filename = filedialog.asksaveasfilename(
            title="Save Research Log",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                
                self.log(f"💾 Log saved to: {os.path.basename(filename)}", "success")
                messagebox.showinfo("Success", "Log saved successfully!")
            
            except Exception as e:
                self.log(f"❌ Failed to save log: {e}", "error")
                messagebox.showerror("Error", f"Failed to save log: {e}")

# Integration functions
def integrate_with_existing_gui(existing_gui, existing_notebook):
    """
    Integriert Enhanced Web Research in bestehende MineExtractor GUI
    
    Args:
        existing_gui: Bestehende MineExtractorGUI Instanz
        existing_notebook: Bestehende ttk.Notebook Instanz
    
    Returns:
        EnhancedWebResearchGUI Instanz
    """
    
    print("🔗 Integrating Enhanced Web Research into existing GUI...")
    
    try:
        from .enhanced_gui_integration import EnhancedWebResearchGUI
        enhanced_extension = EnhancedWebResearchGUI(existing_gui, existing_notebook)
        print("✅ Enhanced Web Research successfully integrated!")
        return enhanced_extension
        
    except Exception as e:
        print(f"❌ Enhanced integration failed: {e}")
        # Fallback to basic integration
        web_extension = WebResearchGUIExtension(existing_gui, existing_notebook)
        print("✅ Basic Web Research integrated as fallback")
        return web_extension

def create_standalone_gui():
    """
    Erstellt Enhanced Standalone GUI
    
    Returns:
        EnhancedWebResearchGUI Instanz
    """
    
    print("🖥️ Creating Enhanced Web Research GUI...")
    
    try:
        from .enhanced_gui_integration import EnhancedWebResearchGUI
        enhanced_gui = EnhancedWebResearchGUI()
        print("✅ Enhanced GUI created successfully!")
        return enhanced_gui
        
    except Exception as e:
        print(f"❌ Enhanced GUI creation failed: {e}")
        print("🔄 Falling back to basic GUI...")
        # Fallback to basic GUI
        try:
            web_extension = WebResearchGUIExtension()
            print("✅ Basic GUI created as fallback")
            return web_extension
        except Exception as e2:
            print(f"❌ Both Enhanced and Basic GUI creation failed: {e2}")
            raise
