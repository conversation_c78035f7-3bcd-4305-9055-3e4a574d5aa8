@echo off
echo 🔧 MineExtractorWeb v1.0 - Tavily API Quick Fix
echo ================================================

echo.
echo 📋 Checking Python installation...
python --version
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo.
echo 📁 Changing to project directory...
cd /d "%~dp0"

echo.
echo 📦 Installing/updating dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ⚠️ Warning: Some dependencies might have failed to install
)

echo.
echo 🧪 Running comprehensive fix test...
python fix_tavily_issues.py
if errorlevel 1 (
    echo ❌ Fix test failed - please check configuration
    echo.
    echo 💡 Common issues:
    echo    1. Check API keys in .env file
    echo    2. Ensure internet connection
    echo    3. Verify Tavily account status
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 Testing main application...
echo Starting GUI in test mode...
timeout /t 3 /nobreak

echo.
echo ✅ Fix completed successfully!
echo.
echo 📋 What was fixed:
echo    ✅ GUI now loads API keys automatically from .env
echo    ✅ Tavily API request format corrected
echo    ✅ Better error handling and debugging
echo    ✅ Automatic API tests on startup
echo.
echo 🎯 Next steps:
echo    1. Start the application: python main.py
echo    2. API keys should load automatically
echo    3. Tavily API test should succeed
echo    4. System ready for web research!
echo.
echo 💡 If issues persist, check TAVILY_API_FIXES.md
echo.
pause
